load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "asm",
    autoglob = False,
    visibility = ["//ReactAndroid/..."],
    exported_deps = [
        ":asm-analysis",
        ":asm-commons",
        ":asm-core",
        ":asm-tree",
        ":asm-util",
    ],
)

rn_prebuilt_jar(
    name = "asm-core",
    binary_jar = ":download-asm.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-asm.jar",
    sha1 = "d74d4ba0dee443f68fb2dcb7fcdb945a2cd89912",
    url = "mvn:org.ow2.asm:asm:jar:7.0",
)

rn_prebuilt_jar(
    name = "asm-commons",
    binary_jar = ":download-asm-commons.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-asm-commons.jar",
    sha1 = "478006d07b7c561ae3a92ddc1829bca81ae0cdd1",
    url = "mvn:org.ow2.asm:asm-commons:jar:7.0",
)

rn_prebuilt_jar(
    name = "asm-tree",
    binary_jar = ":download-asm-tree.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-asm-tree.jar",
    sha1 = "29bc62dcb85573af6e62e5b2d735ef65966c4180",
    url = "mvn:org.ow2.asm:asm-tree:jar:7.0",
)

rn_prebuilt_jar(
    name = "asm-util",
    binary_jar = ":download-asm-util.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-asm-util.jar",
    sha1 = "18d4d07010c24405129a6dbb0e92057f8779fb9d",
    url = "mvn:org.ow2.asm:asm-util:jar:7.0",
)

rn_prebuilt_jar(
    name = "asm-analysis",
    binary_jar = ":download-asm-analysis.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-asm-analysis.jar",
    sha1 = "4b310d20d6f1c6b7197a75f1b5d69f169bc8ac1f",
    url = "mvn:org.ow2.asm:asm-analysis:jar:7.0",
)
