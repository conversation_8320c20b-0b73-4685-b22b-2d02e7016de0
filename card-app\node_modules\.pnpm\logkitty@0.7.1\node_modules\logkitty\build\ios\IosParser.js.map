{"version": 3, "sources": ["../../src/ios/IosParser.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "splitMessages", "raw", "messages", "data", "toString", "match", "timeRegex", "timeMatch", "slice", "index", "length", "nextMatch", "body", "push", "parseMessages", "map", "rawMessage", "Error", "headerMatch", "headerRegex", "priority", "pid", "tag", "platform", "date", "set", "parseInt", "trim", "Priority", "fromName", "reduce", "acc", "entry", "isSame", "appId"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;AAEe,MAAMA,SAAN,CAAmC;AAIhDC,EAAAA,aAAa,CAACC,GAAD,EAAwB;AACnC,UAAMC,QAAkB,GAAG,EAA3B;AACA,QAAIC,IAAI,GAAGF,GAAG,CAACG,QAAJ,EAAX;AACA,QAAIC,KAAK,GAAGF,IAAI,CAACE,KAAL,CAAWN,SAAS,CAACO,SAArB,CAAZ;;AACA,WAAOD,KAAP,EAAc;AACZ,YAAME,SAAS,GAAGF,KAAK,CAAC,CAAD,CAAvB;AACAF,MAAAA,IAAI,GAAGA,IAAI,CAACK,KAAL,CAAW,CAACH,KAAK,CAACI,KAAN,IAAe,CAAhB,IAAqBF,SAAS,CAACG,MAA1C,CAAP;AACA,YAAMC,SAAS,GAAGR,IAAI,CAACE,KAAL,CAAWN,SAAS,CAACO,SAArB,CAAlB;AACA,YAAMM,IAAI,GAAGD,SAAS,GAAGR,IAAI,CAACK,KAAL,CAAW,CAAX,EAAcG,SAAS,CAACF,KAAxB,CAAH,GAAoCN,IAA1D;AACAD,MAAAA,QAAQ,CAACW,IAAT,CAAe,GAAEN,SAAU,IAAGK,IAAK,EAAnC;AACAP,MAAAA,KAAK,GAAGM,SAAR;AACD;;AACD,WAAOT,QAAP;AACD;;AAEDY,EAAAA,aAAa,CAACZ,QAAD,EAA8B;AACzC,WAAOA,QAAQ,CACZa,GADI,CAEFC,UAAD,IAA+B;AAC7B,YAAMT,SAAS,GAAGS,UAAU,CAACX,KAAX,CAAiBN,SAAS,CAACO,SAA3B,CAAlB;;AACA,UAAI,CAACC,SAAL,EAAgB;AACd,cAAM,IAAIU,KAAJ,CACH,0CAAyCD,UAAW,EADjD,CAAN;AAGD;;AACD,YAAME,WAAW,GAAGF,UAAU,CAC3BR,KADiB,CACXD,SAAS,CAAC,CAAD,CAAT,CAAaG,MADF,EAEjBL,KAFiB,CAEXN,SAAS,CAACoB,WAFC,KAEe,CAAC,EAAD,EAAK,SAAL,EAAgB,IAAhB,EAAsB,SAAtB,CAFnC;AAGA,YAAM,GAAGC,QAAH,EAAaC,GAAb,EAAkBC,GAAlB,IAAyBJ,WAA/B;AACA,aAAO;AACLK,QAAAA,QAAQ,EAAE,KADL;AAELC,QAAAA,IAAI,EAAE,oBAAMjB,SAAS,CAAC,CAAD,CAAf,EAAoBkB,GAApB,CAAwB,aAAxB,EAAuC,CAAvC,CAFD;AAGLJ,QAAAA,GAAG,EAAEK,QAAQ,CAACL,GAAG,CAACM,IAAJ,EAAD,EAAa,EAAb,CAAR,IAA4B,CAH5B;AAILP,QAAAA,QAAQ,EAAEQ,oBAASC,QAAT,CAAkBT,QAAlB,CAJL;AAKLE,QAAAA,GALK;AAMLpB,QAAAA,QAAQ,EAAE,CACRc,UAAU,CACPR,KADH,CACSD,SAAS,CAAC,CAAD,CAAT,CAAaG,MAAb,GAAsBQ,WAAW,CAAC,CAAD,CAAX,CAAeR,MAD9C,EAEGiB,IAFH,EADQ;AANL,OAAP;AAYD,KAzBE,EA2BJG,MA3BI,CA2BG,CAACC,GAAD,EAAeC,KAAf,KAAgC;AACtC,UACED,GAAG,CAACrB,MAAJ,GAAa,CAAb,IACAqB,GAAG,CAACA,GAAG,CAACrB,MAAJ,GAAa,CAAd,CAAH,CAAoBc,IAApB,CAAyBS,MAAzB,CAAgCD,KAAK,CAACR,IAAtC,CADA,IAEAO,GAAG,CAACA,GAAG,CAACrB,MAAJ,GAAa,CAAd,CAAH,CAAoBwB,KAApB,KAA8BF,KAAK,CAACE,KAFpC,IAGAH,GAAG,CAACA,GAAG,CAACrB,MAAJ,GAAa,CAAd,CAAH,CAAoBW,GAApB,KAA4BW,KAAK,CAACX,GAHlC,IAIAU,GAAG,CAACA,GAAG,CAACrB,MAAJ,GAAa,CAAd,CAAH,CAAoBU,QAApB,KAAiCY,KAAK,CAACZ,QALzC,EAME;AACAW,QAAAA,GAAG,CAACA,GAAG,CAACrB,MAAJ,GAAa,CAAd,CAAH,CAAoBR,QAApB,CAA6BW,IAA7B,CAAkC,GAAGmB,KAAK,CAAC9B,QAA3C;AACA,eAAO6B,GAAP;AACD;;AACD,aAAO,CAAC,GAAGA,GAAJ,EAASC,KAAT,CAAP;AACD,KAvCI,EAuCF,EAvCE,CAAP;AAwCD;;AA5D+C;;;;gBAA7BjC,S,eACQ,+C;;gBADRA,S,iBAEU,4D", "sourcesContent": ["import DayJS from 'dayjs';\nimport { IParser, Entry } from '../types';\nimport { Priority, PriorityNames } from './constants';\n\nexport default class IosParser implements IParser {\n  static timeRegex: RegExp = /\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2}\\.[\\d+]+/m;\n  static headerRegex: RegExp = /^\\s+[a-z0-9]+\\s+(\\w+)\\s+[a-z0-9]+\\s+(\\d+)\\s+\\d+\\s+([^:]+):/;\n\n  splitMessages(raw: string): string[] {\n    const messages: string[] = [];\n    let data = raw.toString();\n    let match = data.match(IosParser.timeRegex);\n    while (match) {\n      const timeMatch = match[0];\n      data = data.slice((match.index || 0) + timeMatch.length);\n      const nextMatch = data.match(IosParser.timeRegex);\n      const body = nextMatch ? data.slice(0, nextMatch.index) : data;\n      messages.push(`${timeMatch} ${body}`);\n      match = nextMatch;\n    }\n    return messages;\n  }\n\n  parseMessages(messages: string[]): Entry[] {\n    return messages\n      .map(\n        (rawMessage: string): Entry => {\n          const timeMatch = rawMessage.match(IosParser.timeRegex);\n          if (!timeMatch) {\n            throw new Error(\n              `Time regex was not matched in message: ${rawMessage}`\n            );\n          }\n          const headerMatch = rawMessage\n            .slice(timeMatch[0].length)\n            .match(IosParser.headerRegex) || ['', 'Default', '-1', 'unknown'];\n          const [, priority, pid, tag] = headerMatch;\n          return {\n            platform: 'ios',\n            date: DayJS(timeMatch[0]).set('millisecond', 0),\n            pid: parseInt(pid.trim(), 10) || 0,\n            priority: Priority.fromName(priority as PriorityNames),\n            tag,\n            messages: [\n              rawMessage\n                .slice(timeMatch[0].length + headerMatch[0].length)\n                .trim(),\n            ],\n          };\n        }\n      )\n      .reduce((acc: Entry[], entry: Entry) => {\n        if (\n          acc.length > 0 &&\n          acc[acc.length - 1].date.isSame(entry.date) &&\n          acc[acc.length - 1].appId === entry.appId &&\n          acc[acc.length - 1].pid === entry.pid &&\n          acc[acc.length - 1].priority === entry.priority\n        ) {\n          acc[acc.length - 1].messages.push(...entry.messages);\n          return acc;\n        }\n        return [...acc, entry];\n      }, []);\n  }\n}\n"], "file": "IosParser.js"}