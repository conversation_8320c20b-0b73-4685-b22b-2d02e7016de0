load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "sqlite",
    autoglob = False,
    visibility = ["//ReactAndroid/..."],
    exported_deps = [
        ":sqlite4java",
    ],
)

rn_prebuilt_jar(
    name = "sqlite4java",
    binary_jar = ":download-sqlite4java.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-sqlite4java.jar",
    sha1 = "745a7e2f35fdbe6336922e0d492c979dbbfa74fb",
    url = "mvn:com.almworks.sqlite4java:sqlite4java:jar:0.282",
)
