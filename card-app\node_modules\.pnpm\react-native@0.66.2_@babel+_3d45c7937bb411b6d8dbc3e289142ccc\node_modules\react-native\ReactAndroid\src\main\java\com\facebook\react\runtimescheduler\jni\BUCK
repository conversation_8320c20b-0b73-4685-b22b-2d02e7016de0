load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "FBJNI_TARGET", "react_native_target", "react_native_xplat_target", "rn_xplat_cxx_library", "subdir_glob")

rn_xplat_cxx_library(
    name = "jni",
    srcs = glob(["*.cpp"]),
    headers = glob(["*.h"]),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "**/*.h"),
        ],
        prefix = "react/runtimescheduler",
    ),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    fbandroid_allow_jni_merging = True,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    platforms = ANDROID,
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    soname = "libruntimeschedulerjni.$(ext)",
    visibility = ["PUBLIC"],
    deps = [
        react_native_xplat_target("react/renderer/runtimescheduler:runtimescheduler"),
        react_native_xplat_target("runtimeexecutor:runtimeexecutor"),
        react_native_target("jni/react/jni:jni"),
        FBJNI_TARGET,
    ],
)
