{"version": 3, "sources": ["../../src/link/getBuildProperty.ts"], "names": ["getBuildProperty", "project", "prop", "target", "get<PERSON><PERSON>t<PERSON>arget", "firstTarget", "config", "pbxXCConfigurationList", "buildConfigurationList", "buildSection", "pbxXCBuildConfigurationSection", "buildConfigurations", "value", "buildSettings"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,gBAAT,CAA0BC,OAA1B,EAAwCC,IAAxC,EAAsD;AACnE,QAAMC,MAAM,GAAGF,OAAO,CAACG,cAAR,GAAyBC,WAAxC;AACA,QAAMC,MAAM,GAAGL,OAAO,CAACM,sBAAR,GACbJ,MAAM,CAACK,sBADM,CAAf;AAGA,QAAMC,YAAY,GAAGR,OAAO,CAACS,8BAAR,GACnBJ,MAAM,CAACK,mBAAP,CAA2B,CAA3B,EAA8BC,KADX,CAArB;AAIA,SAAOH,YAAY,CAACI,aAAb,CAA2BX,IAA3B,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Gets build property from the main target build section\n *\n * It differs from the project.getBuildProperty exposed by xcode in the way that:\n * - it only checks for build property in the main target `Debug` section\n * - `xcode` library iterates over all build sections and because it misses\n * an early return when property is found, it will return undefined/wrong value\n * when there's another build section typically after the one you want to access\n * without the property defined (e.g. CocoaPods sections appended to project\n * miss INFOPLIST_FILE), see: https://github.com/alunny/node-xcode/blob/master/lib/pbxProject.js#L1765\n */\nexport default function getBuildProperty(project: any, prop: string) {\n  const target = project.getFirstTarget().firstTarget;\n  const config = project.pbxXCConfigurationList()[\n    target.buildConfigurationList\n  ];\n  const buildSection = project.pbxXCBuildConfigurationSection()[\n    config.buildConfigurations[0].value\n  ];\n\n  return buildSection.buildSettings[prop];\n}\n"]}