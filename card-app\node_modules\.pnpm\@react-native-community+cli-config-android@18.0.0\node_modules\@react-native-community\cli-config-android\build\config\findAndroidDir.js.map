{"version": 3, "names": ["findAndroidDir", "root", "fs", "existsSync", "path", "join"], "sources": ["../../src/config/findAndroidDir.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport path from 'path';\n\nexport default function findAndroidDir(root: string) {\n  if (fs.existsSync(path.join(root, 'android'))) {\n    return 'android';\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AATxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAKe,SAASA,cAAc,CAACC,IAAY,EAAE;EACnD,IAAIC,aAAE,CAACC,UAAU,CAACC,eAAI,CAACC,IAAI,CAACJ,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE;IAC7C,OAAO,SAAS;EAClB;EAEA,OAAO,IAAI;AACb"}