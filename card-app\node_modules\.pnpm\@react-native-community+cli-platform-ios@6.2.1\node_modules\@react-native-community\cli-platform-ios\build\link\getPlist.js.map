{"version": 3, "sources": ["../../src/link/getPlist.ts"], "names": ["getPlist", "project", "sourceDir", "plist<PERSON><PERSON>", "fs", "existsSync", "p<PERSON><PERSON><PERSON><PERSON>", "parse", "readFileSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACe,SAASA,QAAT,CAAkBC,OAAlB,EAAgCC,SAAhC,EAAmD;AAChE,QAAMC,SAAS,GAAG,2BAAaF,OAAb,EAAsBC,SAAtB,CAAlB;;AAEA,MAAI,CAACC,SAAD,IAAc,CAACC,cAAGC,UAAH,CAAcF,SAAd,CAAnB,EAA6C;AAC3C,WAAO,IAAP;AACD;;AAED,SAAOG,iBAAYC,KAAZ,CAAkBH,cAAGI,YAAH,CAAgBL,SAAhB,EAA2B,OAA3B,CAAlB,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport plistParser from 'plist';\nimport fs from 'fs';\nimport getPlistPath from './getPlistPath';\n\n/**\n * Returns Info.plist located in the iOS project\n *\n * Returns `null` if INFOPLIST_FILE is not specified.\n */\nexport default function getPlist(project: any, sourceDir: string) {\n  const plistPath = getPlistPath(project, sourceDir);\n\n  if (!plistPath || !fs.existsSync(plistPath)) {\n    return null;\n  }\n\n  return plistParser.parse(fs.readFileSync(plistPath, 'utf-8'));\n}\n"]}