{"version": 3, "sources": ["../../src/link/addToHeaderSearchPaths.ts"], "names": ["addToHeaderSearchPaths", "project", "path", "logger", "debug", "searchPaths", "concat"], "mappings": ";;;;;;;AAQA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,sBAAT,CAAgCC,OAAhC,EAA8CC,IAA9C,EAA4D;AACzEC,qBAAOC,KAAP,CAAc,UAASF,IAAK,yBAA5B;;AACA,qCAAqBD,OAArB,EAA+BI,WAAD,IAAiBA,WAAW,CAACC,MAAZ,CAAmBJ,IAAnB,CAA/C;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport mapHeaderSearchPaths from './mapHeaderSearchPaths';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport default function addToHeaderSearchPaths(project: any, path: string) {\n  logger.debug(`Adding ${path} to header search paths`);\n  mapHeaderSearchPaths(project, (searchPaths) => searchPaths.concat(path));\n}\n"]}