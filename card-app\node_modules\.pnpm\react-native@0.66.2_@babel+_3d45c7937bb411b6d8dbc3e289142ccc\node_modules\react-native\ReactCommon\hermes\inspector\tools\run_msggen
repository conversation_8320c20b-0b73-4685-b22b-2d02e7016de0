#!/bin/bash

set -e

DIR=$(dirname "${BASH_SOURCE[0]}")
cd "${DIR}/msggen"

yarn install
yarn build

FBSOURCE=$(hg root)
MSGTYPES_PATH="${FBSOURCE}/xplat/js/react-native-github/ReactCommon/hermes/inspector/tools/message_types.txt"
HEADER_PATH="${FBSOURCE}/xplat/js/react-native-github/ReactCommon/hermes/inspector/chrome/MessageTypes.h"
CPP_PATH="${FBSOURCE}/xplat/js/react-native-github/ReactCommon/hermes/inspector/chrome/MessageTypes.cpp"

node bin/index.js \
  --ignore-experimental \
  --roots "$MSGTYPES_PATH" \
  "$HEADER_PATH" "$CPP_PATH"

clang-format -i --style=file "$HEADER_PATH"
clang-format -i --style=file "$CPP_PATH"

"${FBSOURCE}/tools/signedsource" sign "$HEADER_PATH"
"${FBSOURCE}/tools/signedsource" sign "$CPP_PATH"
