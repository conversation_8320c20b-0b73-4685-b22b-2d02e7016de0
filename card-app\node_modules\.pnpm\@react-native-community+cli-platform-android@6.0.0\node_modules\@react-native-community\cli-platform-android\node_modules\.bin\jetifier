#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jetifier@1.6.8/node_modules/jetifier/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jetifier@1.6.8/node_modules/jetifier/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jetifier@1.6.8/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jetifier@1.6.8/node_modules/jetifier/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jetifier@1.6.8/node_modules/jetifier/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jetifier@1.6.8/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../jetifier@1.6.8/node_modules/jetifier/bin/jetify" "$@"
else
  exec node  "$basedir/../../../../../../jetifier@1.6.8/node_modules/jetifier/bin/jetify" "$@"
fi
