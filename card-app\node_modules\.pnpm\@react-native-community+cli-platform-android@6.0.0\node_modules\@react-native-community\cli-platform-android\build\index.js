"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "linkConfig", {
  enumerable: true,
  get: function () {
    return _link.default;
  }
});
Object.defineProperty(exports, "commands", {
  enumerable: true,
  get: function () {
    return _commands.default;
  }
});
Object.defineProperty(exports, "projectConfig", {
  enumerable: true,
  get: function () {
    return _config.projectConfig;
  }
});
Object.defineProperty(exports, "dependencyConfig", {
  enumerable: true,
  get: function () {
    return _config.dependencyConfig;
  }
});
Object.defineProperty(exports, "getAndroidProject", {
  enumerable: true,
  get: function () {
    return _getAndroidProject.getAndroidProject;
  }
});
Object.defineProperty(exports, "getPackageName", {
  enumerable: true,
  get: function () {
    return _getAndroidProject.getPackageName;
  }
});

var _link = _interopRequireDefault(require("./link"));

var _commands = _interopRequireDefault(require("./commands"));

var _config = require("./config");

var _getAndroidProject = require("./utils/getAndroidProject");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

//# sourceMappingURL=index.js.map