{"version": 3, "names": ["MAIN_ACTION", "LAUNCHER", "getMainActivity", "manifestPath", "xmlParser", "XMLParser", "ignoreAttributes", "manifestContent", "fs", "readFileSync", "encoding", "XMLValidator", "validate", "manifest", "parse", "application", "activity", "activities", "Array", "isArray", "mainActivity", "find", "act", "intentFilters", "<PERSON><PERSON><PERSON><PERSON>", "action", "category", "actions", "categories", "parsedActions", "map", "name", "parsedCategories", "includes"], "sources": ["../../src/config/getMainActivity.ts"], "sourcesContent": ["import fs from 'fs';\nimport {XMLParser, XMLValidator} from 'fast-xml-parser';\n\nconst MAIN_ACTION = 'android.intent.action.MAIN';\nconst LAUNCHER = 'android.intent.category.LAUNCHER';\n\ninterface Activity {\n  [x: string]: any;\n}\n\ninterface AndroidNameProperty {\n  '@_android:name': string;\n}\n\ninterface IntentFilter {\n  action: AndroidNameProperty | AndroidNameProperty[];\n  category: AndroidNameProperty | AndroidNameProperty[];\n}\n\n/**\n * Reads the AndroidManifest.xml file and returns the name of the main activity.\n */\n\nexport default function getMainActivity(manifestPath: string): string | null {\n  try {\n    const xmlParser = new XMLParser({ignoreAttributes: false});\n    const manifestContent = fs.readFileSync(manifestPath, {encoding: 'utf8'});\n\n    if (XMLValidator.validate(manifestContent)) {\n      const {manifest} = xmlParser.parse(manifestContent);\n\n      const application = manifest.application || {};\n      const activity = application.activity || {};\n\n      let activities: Activity[] = [];\n\n      if (!Array.isArray(activity)) {\n        activities = [activity];\n      } else {\n        activities = activity;\n      }\n\n      const mainActivity = activities.find((act: Activity) => {\n        let intentFilters = act['intent-filter'];\n\n        if (!intentFilters) {\n          return false;\n        }\n\n        if (!Array.isArray(intentFilters)) {\n          intentFilters = [intentFilters];\n        }\n\n        return intentFilters.find((intentFilter: IntentFilter) => {\n          const {action, category} = intentFilter;\n\n          if (!action || !category) {\n            return false;\n          }\n\n          let actions;\n          let categories;\n\n          if (!Array.isArray(action)) {\n            actions = [action];\n          } else {\n            actions = action;\n          }\n\n          if (!Array.isArray(category)) {\n            categories = [category];\n          } else {\n            categories = category;\n          }\n\n          if (actions && categories) {\n            const parsedActions: string[] = actions.map(\n              ({'@_android:name': name}) => name,\n            );\n\n            const parsedCategories: string[] = categories.map(\n              ({'@_android:name': name}) => name,\n            );\n\n            return (\n              parsedActions.includes(MAIN_ACTION) &&\n              parsedCategories.includes(LAUNCHER)\n            );\n          }\n\n          return false;\n        });\n      });\n\n      return mainActivity ? mainActivity['@_android:name'] : null;\n    } else {\n      return null;\n    }\n  } catch {\n    return null;\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwD;AAExD,MAAMA,WAAW,GAAG,4BAA4B;AAChD,MAAMC,QAAQ,GAAG,kCAAkC;AAenD;AACA;AACA;;AAEe,SAASC,eAAe,CAACC,YAAoB,EAAiB;EAC3E,IAAI;IACF,MAAMC,SAAS,GAAG,KAAIC,0BAAS,EAAC;MAACC,gBAAgB,EAAE;IAAK,CAAC,CAAC;IAC1D,MAAMC,eAAe,GAAGC,aAAE,CAACC,YAAY,CAACN,YAAY,EAAE;MAACO,QAAQ,EAAE;IAAM,CAAC,CAAC;IAEzE,IAAIC,6BAAY,CAACC,QAAQ,CAACL,eAAe,CAAC,EAAE;MAC1C,MAAM;QAACM;MAAQ,CAAC,GAAGT,SAAS,CAACU,KAAK,CAACP,eAAe,CAAC;MAEnD,MAAMQ,WAAW,GAAGF,QAAQ,CAACE,WAAW,IAAI,CAAC,CAAC;MAC9C,MAAMC,QAAQ,GAAGD,WAAW,CAACC,QAAQ,IAAI,CAAC,CAAC;MAE3C,IAAIC,UAAsB,GAAG,EAAE;MAE/B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;QAC5BC,UAAU,GAAG,CAACD,QAAQ,CAAC;MACzB,CAAC,MAAM;QACLC,UAAU,GAAGD,QAAQ;MACvB;MAEA,MAAMI,YAAY,GAAGH,UAAU,CAACI,IAAI,CAAEC,GAAa,IAAK;QACtD,IAAIC,aAAa,GAAGD,GAAG,CAAC,eAAe,CAAC;QAExC,IAAI,CAACC,aAAa,EAAE;UAClB,OAAO,KAAK;QACd;QAEA,IAAI,CAACL,KAAK,CAACC,OAAO,CAACI,aAAa,CAAC,EAAE;UACjCA,aAAa,GAAG,CAACA,aAAa,CAAC;QACjC;QAEA,OAAOA,aAAa,CAACF,IAAI,CAAEG,YAA0B,IAAK;UACxD,MAAM;YAACC,MAAM;YAAEC;UAAQ,CAAC,GAAGF,YAAY;UAEvC,IAAI,CAACC,MAAM,IAAI,CAACC,QAAQ,EAAE;YACxB,OAAO,KAAK;UACd;UAEA,IAAIC,OAAO;UACX,IAAIC,UAAU;UAEd,IAAI,CAACV,KAAK,CAACC,OAAO,CAACM,MAAM,CAAC,EAAE;YAC1BE,OAAO,GAAG,CAACF,MAAM,CAAC;UACpB,CAAC,MAAM;YACLE,OAAO,GAAGF,MAAM;UAClB;UAEA,IAAI,CAACP,KAAK,CAACC,OAAO,CAACO,QAAQ,CAAC,EAAE;YAC5BE,UAAU,GAAG,CAACF,QAAQ,CAAC;UACzB,CAAC,MAAM;YACLE,UAAU,GAAGF,QAAQ;UACvB;UAEA,IAAIC,OAAO,IAAIC,UAAU,EAAE;YACzB,MAAMC,aAAuB,GAAGF,OAAO,CAACG,GAAG,CACzC,CAAC;cAAC,gBAAgB,EAAEC;YAAI,CAAC,KAAKA,IAAI,CACnC;YAED,MAAMC,gBAA0B,GAAGJ,UAAU,CAACE,GAAG,CAC/C,CAAC;cAAC,gBAAgB,EAAEC;YAAI,CAAC,KAAKA,IAAI,CACnC;YAED,OACEF,aAAa,CAACI,QAAQ,CAACjC,WAAW,CAAC,IACnCgC,gBAAgB,CAACC,QAAQ,CAAChC,QAAQ,CAAC;UAEvC;UAEA,OAAO,KAAK;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAOmB,YAAY,GAAGA,YAAY,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAC7D,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;AACF"}