def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

buildscript {
  // The Android Gradle plugin is only required when opening the android folder stand-alone.
  // This avoids unnecessary downloads and potential conflicts when the library is included as a
  // module dependency in an application project.
  if (project == rootProject) {
    repositories {
      google()
      mavenCentral()
      jcenter()
    }

    dependencies {
      //noinspection GradleDependency
      classpath("com.android.tools.build:gradle:4.1.0")
    }
  }
}

apply plugin: 'com.android.library'

android {
  compileSdkVersion safeExtGet('compileSdkVersion', 29)
  buildToolsVersion safeExtGet('buildToolsVersion', '29.0.2')

  defaultConfig {
    multiDexEnabled true
    minSdkVersion safeExtGet('minSdkVersion', 16)
    targetSdkVersion safeExtGet('targetSdkVersion', 29)
  }

  flavorDimensions "react-native-camera"

  productFlavors {
    general {
      dimension "react-native-camera"
    }
    mlkit {
      dimension "react-native-camera"
    }
  }

  lintOptions {
    abortOnError false
    warning 'InvalidPackage'
  }

  packagingOptions {
    exclude 'META-INF/androidx.exifinterface_exifinterface.version'
    exclude 'META-INF/proguard/androidx-annotations.pro'
  }
}

repositories {
  google()
  jcenter()
  mavenCentral()
  maven { url "https://jitpack.io" }
  maven {
    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
    url "$rootDir/../node_modules/react-native/android"
  }
}

dependencies {
  //noinspection GradleDynamicVersion
  implementation 'com.facebook.react:react-native:+'  // From node_modules
  implementation "com.google.zxing:core:3.3.3"
  implementation "com.drewnoakes:metadata-extractor:2.11.0"
  implementation "androidx.exifinterface:exifinterface:1.3.2"
  implementation "androidx.annotation:annotation:1.0.0"
  implementation "androidx.legacy:legacy-support-v4:1.0.0"
  implementation "com.google.android.gms:play-services-mlkit-text-recognition:16.3.0"
  generalImplementation "com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0"
  generalImplementation "com.google.android.gms:play-services-mlkit-face-detection:16.2.0"
  mlkitImplementation "com.google.mlkit:barcode-scanning:16.2.0"
  mlkitImplementation "com.google.mlkit:face-detection:16.1.2"
}
