{"version": 3, "sources": ["../../src/config/index.ts"], "names": ["memoizedFindProject", "findProject", "mapSharedLibaries", "libraries", "map", "name", "path", "extname", "indexOf", "projectConfig", "folder", "userConfig", "project", "projectPath", "join", "podfile", "sourceDir", "dirname", "pbxproj<PERSON><PERSON>", "podspecPath", "projectName", "basename", "libraryFolder", "sharedLibraries", "plist", "scriptPhases", "dependencyConfig", "configurations", "baseConfig"], "mappings": ";;;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;;;AAZA;AACA;AACA;AACA;AACA;AACA;AACA;AAYA,MAAMA,mBAAmB,GAAG,uBAAQC,oBAAR,CAA5B;AAEA;AACA;AACA;AACA;;AACA,MAAMC,iBAAiB,GAAIC,SAAD,IACxBA,SAAS,CAACC,GAAV,CAAeC,IAAD,IAAU;AACtB,MAAIC,gBAAKC,OAAL,CAAaF,IAAb,CAAJ,EAAwB;AACtB,WAAOA,IAAP;AACD;;AACD,SAAOA,IAAI,IAAIA,IAAI,CAACG,OAAL,CAAa,KAAb,MAAwB,CAAxB,GAA4B,MAA5B,GAAqC,YAAzC,CAAX;AACD,CALD,CADF;AAQA;AACA;AACA;AACA;;;AACO,SAASC,aAAT,CAAuBC,MAAvB,EAAuCC,UAAvC,EAAqE;AAC1E,MAAI,CAACA,UAAL,EAAiB;AACf;AACD;;AACD,QAAMC,OAAO,GAAGD,UAAU,CAACC,OAAX,IAAsBZ,mBAAmB,CAACU,MAAD,CAAzD;AAEA;AACF;AACA;;AACE,MAAI,CAACE,OAAL,EAAc;AACZ,WAAO,IAAP;AACD;;AAED,QAAMC,WAAW,GAAGP,gBAAKQ,IAAL,CAAUJ,MAAV,EAAkBE,OAAlB,CAApB;;AACA,QAAMG,OAAO,GAAG,8BAAgBL,MAAhB,EAAwBG,WAAxB,CAAhB,CAd0E,CAgB1E;AACA;AACA;;AACA,QAAMG,SAAS,GAAGD,OAAO,GAAGT,gBAAKW,OAAL,CAAaF,OAAb,CAAH,GAA2BT,gBAAKW,OAAL,CAAaJ,WAAb,CAApD;AAEA,SAAO;AACLG,IAAAA,SADK;AAELN,IAAAA,MAFK;AAGLQ,IAAAA,WAAW,EAAEZ,gBAAKQ,IAAL,CAAUD,WAAV,EAAuB,iBAAvB,CAHR;AAILE,IAAAA,OAJK;AAKLI,IAAAA,WAAW,EACTR,UAAU,CAACQ,WAAX,IACA;AACA;AACA,8BAAYT,MAAZ,CAHA,IAIA,0BAAYM,SAAZ,CAVG;AAWLH,IAAAA,WAXK;AAYLO,IAAAA,WAAW,EAAEd,gBAAKe,QAAL,CAAcR,WAAd,CAZR;AAaLS,IAAAA,aAAa,EAAEX,UAAU,CAACW,aAAX,IAA4B,WAbtC;AAcLC,IAAAA,eAAe,EAAErB,iBAAiB,CAACS,UAAU,CAACY,eAAX,IAA8B,EAA/B,CAd7B;AAeLC,IAAAA,KAAK,EAAEb,UAAU,CAACa,KAAX,IAAoB,EAftB;AAgBLC,IAAAA,YAAY,EAAEd,UAAU,CAACc,YAAX,IAA2B;AAhBpC,GAAP;AAkBD;;AAEM,SAASC,gBAAT,CACLhB,MADK,EAELC,UAFK,EAGL;AACA,QAAMgB,cAAc,GAAGhB,UAAU,CAACgB,cAAX,IAA6B,EAApD;AAEA,QAAMC,UAAU,GAAGnB,aAAa,CAACC,MAAD,EAASC,UAAT,CAAhC;;AACA,MAAI,CAACiB,UAAL,EAAiB;AACf,WAAO,IAAP;AACD;;AAED,SAAO,EAAC,GAAGA,UAAJ;AAAgBD,IAAAA;AAAhB,GAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport {memoize} from 'lodash';\nimport findProject from './findProject';\nimport findPodfilePath from './findPodfilePath';\nimport findPodspec from './findPodspec';\nimport {\n  IOSProjectParams,\n  IOSDependencyParams,\n} from '@react-native-community/cli-types';\n\nconst memoizedFindProject = memoize(findProject);\n\n/**\n * For libraries specified without an extension, add '.tbd' for those that\n * start with 'lib' and '.framework' to the rest.\n */\nconst mapSharedLibaries = (libraries: Array<string>) =>\n  libraries.map((name) => {\n    if (path.extname(name)) {\n      return name;\n    }\n    return name + (name.indexOf('lib') === 0 ? '.tbd' : '.framework');\n  });\n\n/**\n * Returns project config by analyzing given folder and applying some user defaults\n * when constructing final object\n */\nexport function projectConfig(folder: string, userConfig: IOSProjectParams) {\n  if (!userConfig) {\n    return;\n  }\n  const project = userConfig.project || memoizedFindProject(folder);\n\n  /**\n   * No iOS config found here\n   */\n  if (!project) {\n    return null;\n  }\n\n  const projectPath = path.join(folder, project);\n  const podfile = findPodfilePath(folder, projectPath);\n\n  // This is a temporary fix for #1435. In certain repos, the Xcode project can\n  // be generated by a tool. The only file that we can assume to exist on disk\n  // is `Podfile`.\n  const sourceDir = podfile ? path.dirname(podfile) : path.dirname(projectPath);\n\n  return {\n    sourceDir,\n    folder,\n    pbxprojPath: path.join(projectPath, 'project.pbxproj'),\n    podfile,\n    podspecPath:\n      userConfig.podspecPath ||\n      // podspecs are usually placed in the root dir of the library or in the\n      // iOS project path\n      findPodspec(folder) ||\n      findPodspec(sourceDir),\n    projectPath,\n    projectName: path.basename(projectPath),\n    libraryFolder: userConfig.libraryFolder || 'Libraries',\n    sharedLibraries: mapSharedLibaries(userConfig.sharedLibraries || []),\n    plist: userConfig.plist || [],\n    scriptPhases: userConfig.scriptPhases || [],\n  };\n}\n\nexport function dependencyConfig(\n  folder: string,\n  userConfig: IOSDependencyParams,\n) {\n  const configurations = userConfig.configurations || [];\n\n  const baseConfig = projectConfig(folder, userConfig);\n  if (!baseConfig) {\n    return null;\n  }\n\n  return {...baseConfig, configurations};\n}\n"]}