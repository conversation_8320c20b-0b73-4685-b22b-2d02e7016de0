load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "util",
    srcs = glob(["**/*.java"]),
    autoglob = False,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
    ],
)
