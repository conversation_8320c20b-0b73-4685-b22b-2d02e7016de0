{"name": "metro-source-map", "version": "0.66.2", "description": "🚇 Source map generator for Metro.", "main": "src/source-map.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"@babel/traverse": "^7.14.0", "@babel/types": "^7.0.0", "invariant": "^2.2.4", "metro-symbolicate": "0.66.2", "nullthrows": "^1.1.1", "ob1": "0.66.2", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "license": "MIT", "devDependencies": {"@babel/parser": "^7.14.0", "uglify-es": "^3.1.9"}}