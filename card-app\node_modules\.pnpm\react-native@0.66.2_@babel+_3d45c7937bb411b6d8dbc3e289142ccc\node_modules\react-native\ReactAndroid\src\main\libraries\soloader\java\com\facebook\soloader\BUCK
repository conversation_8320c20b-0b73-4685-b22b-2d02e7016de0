load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")

fb_native.android_library(
    name = "soloader",
    exported_deps = [
        ":annotation-binary",
        ":nativeloader-binary",
        ":soloader-binary",
    ],
    visibility = ["PUBLIC"],
)

fb_native.prebuilt_jar(
    name = "annotation-binary",
    binary_jar = ":annotation-binary.jar",
)

fb_native.prebuilt_jar(
    name = "nativeloader-binary",
    binary_jar = ":nativeloader-binary.jar",
)

fb_native.android_prebuilt_aar(
    name = "soloader-binary",
    aar = ":soloader-binary-aar",
)

fb_native.remote_file(
    name = "annotation-binary.jar",
    sha1 = "dc58463712cb3e5f03d8ee5ac9743b9ced9afa77",
    url = "mvn:com.facebook.soloader:annotation:jar:0.10.1",
)

fb_native.remote_file(
    name = "nativeloader-binary.jar",
    sha1 = "ba1b31b4b9f65494a90de7c2728b57155344a858",
    url = "mvn:com.facebook.soloader:nativeloader:jar:0.10.1",
)

fb_native.remote_file(
    name = "soloader-binary-aar",
    sha1 = "78e5537c0cdf7c190f6cad9a2490f9f84ee8f041",
    url = "mvn:com.facebook.soloader:soloader:aar:0.10.1",
)
