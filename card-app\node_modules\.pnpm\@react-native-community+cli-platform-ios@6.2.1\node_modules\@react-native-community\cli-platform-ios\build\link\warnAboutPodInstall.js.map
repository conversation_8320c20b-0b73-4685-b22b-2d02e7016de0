{"version": 3, "sources": ["../../src/link/warnAboutPodInstall.ts"], "names": ["warnAboutPodInstall", "config", "podLockDeps", "project", "ios", "podfile", "podDeps", "Object", "keys", "dependencies", "map", "depName", "dependency", "platforms", "podspecPath", "path", "basename", "replace", "filter", "Boolean", "missingPods", "podDep", "includes", "length", "logger", "error", "pod", "chalk", "bold", "join"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;;;AAEe,SAASA,mBAAT,CAA6BC,MAA7B,EAA6C;AAC1D,QAAMC,WAAW,GAAG,6CACjB,GAAED,MAAM,CAACE,OAAP,CAAeC,GAAf,CAAoBC,OAAQ,OADb,CAApB;AAGA,QAAMC,OAAO,GAAGC,MAAM,CAACC,IAAP,CAAYP,MAAM,CAACQ,YAAnB,EACbC,GADa,CACRC,OAAD,IAAa;AAChB,UAAMC,UAAU,GAAGX,MAAM,CAACQ,YAAP,CAAoBE,OAApB,EAA6BE,SAA7B,CAAuCT,GAA1D;AACA,WAAOQ,UAAU,IAAIA,UAAU,CAACE,WAAzB,GACHC,gBAAKC,QAAL,CAAcJ,UAAU,CAACE,WAAzB,EAAsCG,OAAtC,CAA8C,WAA9C,EAA2D,EAA3D,CADG,GAEH,EAFJ;AAGD,GANa,EAObC,MAPa,CAONC,OAPM,CAAhB;AASA,QAAMC,WAAW,GAAGd,OAAO,CAACY,MAAR,CAAgBG,MAAD,IAAY,CAACnB,WAAW,CAACoB,QAAZ,CAAqBD,MAArB,CAA5B,CAApB;;AAEA,MAAID,WAAW,CAACG,MAAhB,EAAwB;AACtBC,uBAAOC,KAAP,CACG,gDAA+CL,WAAW,CACxDV,GAD6C,CACxCgB,GAAD,IAASC,iBAAMC,IAAN,CAAWF,GAAX,CADgC,EAE7CG,IAF6C,CAExC,IAFwC,CAElC,4BAA2BF,iBAAMC,IAAN,CAAW,aAAX,CAA0B,KAHrE;AAKD;AACF", "sourcesContent": ["import path from 'path';\nimport chalk from 'chalk';\nimport {logger} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport getDependenciesFromPodfileLock from '../link-pods/getDependenciesFromPodfileLock';\n\nexport default function warnAboutPodInstall(config: Config) {\n  const podLockDeps = getDependenciesFromPodfileLock(\n    `${config.project.ios!.podfile}.lock`,\n  );\n  const podDeps = Object.keys(config.dependencies)\n    .map((depName) => {\n      const dependency = config.dependencies[depName].platforms.ios;\n      return dependency && dependency.podspecPath\n        ? path.basename(dependency.podspecPath).replace(/\\.podspec/, '')\n        : '';\n    })\n    .filter(Boolean);\n\n  const missingPods = podDeps.filter((podDep) => !podLockDeps.includes(podDep));\n\n  if (missingPods.length) {\n    logger.error(\n      `Could not find the following native modules: ${missingPods\n        .map((pod) => chalk.bold(pod))\n        .join(', ')}. Did you forget to run \"${chalk.bold('pod install')}\" ?`,\n    );\n  }\n}\n"]}