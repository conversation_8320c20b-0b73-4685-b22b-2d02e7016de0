{"version": 3, "sources": ["../../../src/commands/logIOS/index.ts"], "names": ["findAvailableDevice", "devices", "key", "Object", "keys", "device", "availability", "state", "logIOS", "rawDevices", "encoding", "JSON", "parse", "logger", "error", "tailDeviceLogs", "udid", "logDir", "path", "join", "os", "homedir", "log", "stdio", "name", "description", "func"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA,SAASA,mBAAT,CAA6BC,OAA7B,EAAwE;AACtE,OAAK,MAAMC,GAAX,IAAkBC,MAAM,CAACC,IAAP,CAAYH,OAAZ,CAAlB,EAAwC;AACtC,SAAK,MAAMI,MAAX,IAAqBJ,OAAO,CAACC,GAAD,CAA5B,EAAmC;AACjC,UAAIG,MAAM,CAACC,YAAP,KAAwB,aAAxB,IAAyCD,MAAM,CAACE,KAAP,KAAiB,QAA9D,EAAwE;AACtE,eAAOF,MAAP;AACD;AACF;AACF;;AACD,SAAO,IAAP;AACD;AAED;AACA;AACA;;;AACA,eAAeG,MAAf,GAAwB;AACtB,QAAMC,UAAU,GAAG,mCACjB,OADiB,EAEjB,CAAC,QAAD,EAAW,MAAX,EAAmB,SAAnB,EAA8B,QAA9B,CAFiB,EAGjB;AAACC,IAAAA,QAAQ,EAAE;AAAX,GAHiB,CAAnB;AAMA,QAAM;AAACT,IAAAA;AAAD,MAAYU,IAAI,CAACC,KAAL,CAAWH,UAAX,CAAlB;AAIA,QAAMJ,MAAM,GAAGL,mBAAmB,CAACC,OAAD,CAAlC;;AACA,MAAII,MAAM,KAAK,IAAf,EAAqB;AACnBQ,uBAAOC,KAAP,CAAa,4BAAb;;AACA;AACD;;AAEDC,EAAAA,cAAc,CAACV,MAAM,CAACW,IAAR,CAAd;AACD;;AAED,SAASD,cAAT,CAAwBC,IAAxB,EAAsC;AACpC,QAAMC,MAAM,GAAGC,gBAAKC,IAAL,CACbC,cAAGC,OAAH,EADa,EAEb,SAFa,EAGb,MAHa,EAIb,eAJa,EAKbL,IALa,EAMb,KANa,CAAf;;AASA,QAAMM,GAAG,GAAG,gCAAU,QAAV,EAAoB,CAAC,IAAD,EAAO,IAAP,EAAa,KAAb,EAAoB,IAApB,EAA0BL,MAA1B,CAApB,EAAuD;AACjEM,IAAAA,KAAK,EAAE;AAD0D,GAAvD,CAAZ;;AAIA,MAAID,GAAG,CAACR,KAAJ,KAAc,IAAlB,EAAwB;AACtB,UAAMQ,GAAG,CAACR,KAAV;AACD;AACF;;eAEc;AACbU,EAAAA,IAAI,EAAE,SADO;AAEbC,EAAAA,WAAW,EAAE,+BAFA;AAGbC,EAAAA,IAAI,EAAElB;AAHO,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execFileSync, spawnSync} from 'child_process';\nimport os from 'os';\nimport path from 'path';\nimport {logger} from '@react-native-community/cli-tools';\nimport {Device} from '../../types';\n\nfunction findAvailableDevice(devices: {[index: string]: Array<Device>}) {\n  for (const key of Object.keys(devices)) {\n    for (const device of devices[key]) {\n      if (device.availability === '(available)' && device.state === 'Booted') {\n        return device;\n      }\n    }\n  }\n  return null;\n}\n\n/**\n * Starts iOS device syslog tail\n */\nasync function logIOS() {\n  const rawDevices = execFileSync(\n    'xcrun',\n    ['simctl', 'list', 'devices', '--json'],\n    {encoding: 'utf8'},\n  );\n\n  const {devices} = JSON.parse(rawDevices) as {\n    devices: {[index: string]: Array<Device>};\n  };\n\n  const device = findAvailableDevice(devices);\n  if (device === null) {\n    logger.error('No active iOS device found');\n    return;\n  }\n\n  tailDeviceLogs(device.udid);\n}\n\nfunction tailDeviceLogs(udid: string) {\n  const logDir = path.join(\n    os.homedir(),\n    'Library',\n    'Logs',\n    'CoreSimulator',\n    udid,\n    'asl',\n  );\n\n  const log = spawnSync('syslog', ['-w', '-F', 'std', '-d', logDir], {\n    stdio: 'inherit',\n  });\n\n  if (log.error !== null) {\n    throw log.error;\n  }\n}\n\nexport default {\n  name: 'log-ios',\n  description: 'starts iOS device syslog tail',\n  func: logIOS,\n};\n"]}