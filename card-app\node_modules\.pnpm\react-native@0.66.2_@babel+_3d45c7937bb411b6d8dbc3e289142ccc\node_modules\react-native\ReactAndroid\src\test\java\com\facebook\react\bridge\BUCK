load("//tools/build_defs/oss:rn_defs.bzl", "react_native_android_toplevel_dep", "react_native_dep", "react_native_target", "react_native_tests_target", "rn_android_library", "rn_robolectric_test")

STANDARD_TEST_SRCS = [
    "*Test.java",
]

rn_android_library(
    name = "testhelpers",
    srcs = glob(
        ["*.java"],
        exclude = STANDARD_TEST_SRCS,
    ),
    autoglob = False,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_android_toplevel_dep("third-party/java/mockito2:mockito2"),
        react_native_dep("third-party/java/robolectric:robolectric"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_tests_target("java/org/mockito/configuration:configuration"),
    ],
)

rn_robolectric_test(
    name = "bridge",
    srcs = glob(STANDARD_TEST_SRCS),
    contacts = ["<EMAIL>"],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        ":testhelpers",
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/java/fest:fest"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_tests_target("java/com/facebook/common/logging:logging"),
    ],
)
