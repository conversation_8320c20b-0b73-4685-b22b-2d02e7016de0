hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/eslint-parser@7.28.0(@babel/core@7.28.0)(eslint@8.57.1)':
    '@babel/eslint-parser': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.0)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-class-properties': private
  '@babel/plugin-proposal-export-default-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-proposal-export-default-from': private
  '@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-nullish-coalescing-operator': private
  '@babel/plugin-proposal-object-rest-spread@7.20.7(@babel/core@7.28.0)':
    '@babel/plugin-proposal-object-rest-spread': private
  '@babel/plugin-proposal-optional-catch-binding@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-optional-catch-binding': private
  '@babel/plugin-proposal-optional-chaining@7.21.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-optional-chaining': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.0)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-dynamic-import': private
  '@babel/plugin-syntax-export-default-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-export-default-from': private
  '@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-flow': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-explicit-resource-management': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-flow-strip-types@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-flow-strip-types': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-assign@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-assign': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.0)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-regenerator@7.28.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-runtime@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-runtime': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.28.0(@babel/core@7.28.0)':
    '@babel/preset-env': private
  '@babel/preset-flow@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-flow': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.0)':
    '@babel/preset-modules': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-typescript': private
  '@babel/register@7.27.1(@babel/core@7.28.0)':
    '@babel/register': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bang88/react-native-ultimate-listview@4.1.1':
    '@bang88/react-native-ultimate-listview': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cnakazawa/watch@1.0.4':
    '@cnakazawa/watch': private
  '@egjs/hammerjs@2.0.17':
    '@egjs/hammerjs': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@27.5.1':
    '@jest/console': private
  '@jest/core@27.5.1':
    '@jest/core': private
  '@jest/create-cache-key-function@27.5.1':
    '@jest/create-cache-key-function': private
  '@jest/environment@27.5.1':
    '@jest/environment': private
  '@jest/fake-timers@27.5.1':
    '@jest/fake-timers': private
  '@jest/globals@27.5.1':
    '@jest/globals': private
  '@jest/reporters@27.5.1':
    '@jest/reporters': private
  '@jest/source-map@27.5.1':
    '@jest/source-map': private
  '@jest/test-result@27.5.1':
    '@jest/test-result': private
  '@jest/test-sequencer@27.5.1':
    '@jest/test-sequencer': private
  '@jest/transform@27.5.1':
    '@jest/transform': private
  '@jest/types@27.5.1':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    '@nicolo-ribaudo/eslint-scope-5-internals': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@react-native-community/cli-debugger-ui@6.0.0':
    '@react-native-community/cli-debugger-ui': private
  '@react-native-community/cli-hermes@6.3.1':
    '@react-native-community/cli-hermes': private
  '@react-native-community/cli-platform-ios@6.2.1':
    '@react-native-community/cli-platform-ios': private
  '@react-native-community/cli-plugin-metro@6.4.0(@babel/core@7.28.0)':
    '@react-native-community/cli-plugin-metro': private
  '@react-native-community/cli-server-api@6.4.3':
    '@react-native-community/cli-server-api': private
  '@react-native-community/cli-tools@6.2.1':
    '@react-native-community/cli-tools': private
  '@react-native-community/cli-types@6.0.0':
    '@react-native-community/cli-types': private
  '@react-native-community/cli@6.4.0(@babel/core@7.28.0)(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))':
    '@react-native-community/cli': private
  '@react-native-community/eslint-plugin@1.3.0':
    '@react-native-community/eslint-plugin': private
  '@react-native/assets@1.0.0':
    '@react-native/assets': private
  '@react-native/normalize-color@1.0.0':
    '@react-native/normalize-color': private
  '@react-native/polyfills@2.0.0':
    '@react-native/polyfills': private
  '@react-navigation/core@3.7.9(react@17.0.2)':
    '@react-navigation/core': private
  '@react-navigation/native@3.8.4(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))(react@17.0.2)':
    '@react-navigation/native': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@sinonjs/commons@1.8.6':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@8.1.0':
    '@sinonjs/fake-timers': private
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/hammerjs@2.0.46':
    '@types/hammerjs': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/node@24.2.1':
    '@types/node': private
  '@types/prettier@2.7.3':
    '@types/prettier': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/shallowequal@1.1.5':
    '@types/shallowequal': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@16.0.9':
    '@types/yargs': private
  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@5.62.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@5.62.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@5.62.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': private
  abab@2.0.6:
    abab: private
  abort-controller@3.0.0:
    abort-controller: private
  absolute-path@0.0.0:
    absolute-path: private
  accepts@1.3.8:
    accepts: private
  acorn-globals@6.0.0:
    acorn-globals: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@7.2.0:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  anser@1.4.10:
    anser: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-fragments@0.2.1:
    ansi-fragments: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  appdirsjs@1.2.7:
    appdirsjs: private
  argparse@2.0.1:
    argparse: private
  arr-diff@4.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array-tree-filter@2.1.0:
    array-tree-filter: private
  array-union@2.1.0:
    array-union: private
  array-unique@0.3.2:
    array-unique: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  assign-symbols@1.0.0:
    assign-symbols: private
  ast-types@0.14.2:
    ast-types: private
  astral-regex@1.0.0:
    astral-regex: private
  async-function@1.0.0:
    async-function: private
  async-limiter@1.0.1:
    async-limiter: private
  async@2.6.4:
    async: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  babel-core@7.0.0-bridge.0(@babel/core@7.28.0):
    babel-core: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@27.5.1:
    babel-plugin-jest-hoist: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.0):
    babel-plugin-polyfill-regenerator: private
  babel-plugin-syntax-trailing-function-commas@7.0.0-beta.0:
    babel-plugin-syntax-trailing-function-commas: private
  babel-preset-current-node-syntax@1.2.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-fbjs@3.4.0(@babel/core@7.28.0):
    babel-preset-fbjs: private
  babel-preset-jest@27.5.1(@babel/core@7.28.0):
    babel-preset-jest: private
  babel-runtime@6.26.0:
    babel-runtime: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  base@0.11.2:
    base: private
  big-integer@1.6.52:
    big-integer: private
  bplist-creator@0.1.0:
    bplist-creator: private
  bplist-parser@0.3.1:
    bplist-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browser-process-hrtime@1.0.0:
    browser-process-hrtime: private
  browserslist@4.25.2:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer-from@1.1.2:
    buffer-from: private
  builtin-modules@1.1.1:
    builtin-modules: private
  bytes@3.1.2:
    bytes: private
  cache-base@1.0.1:
    cache-base: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  caller-callsite@2.0.0:
    caller-callsite: private
  caller-path@2.0.0:
    caller-path: private
  callsites@3.1.0:
    callsites: private
  camelcase@5.3.1:
    camelcase: private
  caniuse-lite@1.0.30001734:
    caniuse-lite: private
  capture-exit@2.0.0:
    capture-exit: private
  chalk@3.0.0:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-utils@0.3.6:
    class-utils: private
  cli-cursor@2.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cliui@7.0.4:
    cliui: private
  clone-deep@4.0.1:
    clone-deep: private
  clone@1.0.4:
    clone: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  collection-visit@1.0.0:
    collection-visit: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@3.2.1:
    color: private
  colorette@1.4.0:
    colorette: private
  colors@1.4.0:
    colors: private
  combined-stream@1.0.8:
    combined-stream: private
  command-exists@1.2.9:
    command-exists: private
  commander@2.20.3:
    commander: private
  commondir@1.0.1:
    commondir: private
  component-emitter@1.3.1:
    component-emitter: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.1:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  connect@3.7.0:
    connect: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  core-js-compat@3.45.0:
    core-js-compat: private
  core-js@2.6.12:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@5.2.1:
    cosmiconfig: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssom@0.4.4:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  data-urls@2.0.0:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decimal.js@10.6.0:
    decimal.js: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  dedent@0.7.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  define-property@2.0.2:
    define-property: private
  delayed-stream@1.0.0:
    delayed-stream: private
  denodeify@1.2.1:
    denodeify: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-newline@3.1.0:
    detect-newline: private
  diff-sequences@27.5.1:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  domexception@2.0.1:
    domexception: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.199:
    electron-to-chromium: private
  emittery@0.8.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  envinfo@7.14.0:
    envinfo: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  errorhandler@1.5.1:
    errorhandler: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-prettier@8.10.2(eslint@8.57.1):
    eslint-config-prettier: private
  eslint-plugin-eslint-comments@3.2.0(eslint@8.57.1):
    eslint-plugin-eslint-comments: private
  eslint-plugin-ft-flow@2.0.3(@babel/eslint-parser@7.28.0(@babel/core@7.28.0)(eslint@8.57.1))(eslint@8.57.1):
    eslint-plugin-ft-flow: private
  eslint-plugin-jest@26.9.0(@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1)(jest@27.5.1)(typescript@5.9.2):
    eslint-plugin-jest: private
  eslint-plugin-prettier@4.2.5(eslint-config-prettier@8.10.2(eslint@8.57.1))(eslint@8.57.1)(prettier@3.6.2):
    eslint-plugin-prettier: private
  eslint-plugin-react-hooks@4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react-native-globals@0.1.2:
    eslint-plugin-react-native-globals: private
  eslint-plugin-react-native@4.1.0(eslint@8.57.1):
    eslint-plugin-react-native: private
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  exec-sh@0.3.6:
    exec-sh: private
  execa@1.0.0:
    execa: private
  exit@0.1.2:
    exit: private
  expand-brackets@2.1.4:
    expand-brackets: private
  expect@27.5.1:
    expect: private
  extend-shallow@3.0.2:
    extend-shallow: private
  extglob@2.0.4:
    extglob: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fbjs-css-vars@1.0.2:
    fbjs-css-vars: private
  fbjs@3.0.5:
    fbjs: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@1.1.0:
    filter-obj: private
  finalhandler@1.1.2:
    finalhandler: private
  find-babel-config@1.2.2:
    find-babel-config: private
  find-cache-dir@2.1.0:
    find-cache-dir: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  flow-parser@0.121.0:
    flow-parser: private
  for-each@0.3.5:
    for-each: private
  for-in@1.0.2:
    for-in: private
  form-data@3.0.4:
    form-data: private
  fragment-cache@0.2.1:
    fragment-cache: private
  fresh@0.5.2:
    fresh: private
  fs-extra@8.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@4.1.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-value@2.0.6:
    get-value: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  hasown@2.0.2:
    hasown: private
  hermes-engine@0.9.0:
    hermes-engine: private
  hermes-parser@0.4.7:
    hermes-parser: private
  hermes-profile-transformer@0.0.6:
    hermes-profile-transformer: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  html-encoding-sniffer@2.0.1:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@4.0.1:
    http-proxy-agent: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  image-size@0.6.3:
    image-size: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  ip@1.1.9:
    ip: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-ci@2.0.0:
    is-ci: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-descriptor@1.0.3:
    is-descriptor: private
  is-directory@0.3.1:
    is-directory: private
  is-extendable@1.0.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@2.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@3.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@2.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@1.1.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@1.1.0:
    is-wsl: private
  isarray@0.0.1:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@5.2.1:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jest-changed-files@27.5.1:
    jest-changed-files: private
  jest-circus@27.5.1:
    jest-circus: private
  jest-cli@27.5.1:
    jest-cli: private
  jest-config@27.5.1:
    jest-config: private
  jest-diff@27.5.1:
    jest-diff: private
  jest-docblock@27.5.1:
    jest-docblock: private
  jest-each@27.5.1:
    jest-each: private
  jest-environment-jsdom@27.5.1:
    jest-environment-jsdom: private
  jest-environment-node@27.5.1:
    jest-environment-node: private
  jest-get-type@27.5.1:
    jest-get-type: private
  jest-haste-map@27.5.1:
    jest-haste-map: private
  jest-jasmine2@27.5.1:
    jest-jasmine2: private
  jest-leak-detector@27.5.1:
    jest-leak-detector: private
  jest-matcher-utils@27.5.1:
    jest-matcher-utils: private
  jest-message-util@27.5.1:
    jest-message-util: private
  jest-mock@27.5.1:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@27.5.1):
    jest-pnp-resolver: private
  jest-regex-util@27.5.1:
    jest-regex-util: private
  jest-resolve-dependencies@27.5.1:
    jest-resolve-dependencies: private
  jest-resolve@27.5.1:
    jest-resolve: private
  jest-runner@27.5.1:
    jest-runner: private
  jest-runtime@27.5.1:
    jest-runtime: private
  jest-serializer@27.5.1:
    jest-serializer: private
  jest-snapshot@27.5.1:
    jest-snapshot: private
  jest-util@27.5.1:
    jest-util: private
  jest-validate@27.5.1:
    jest-validate: private
  jest-watcher@27.5.1:
    jest-watcher: private
  jest-worker@27.5.1:
    jest-worker: private
  jetifier@1.6.8:
    jetifier: private
  joi@17.13.3:
    joi: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsc-android@250230.2.1:
    jsc-android: private
  jscodeshift@0.11.0(@babel/preset-env@7.28.0(@babel/core@7.28.0)):
    jscodeshift: private
  jsdom@16.7.0:
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@4.0.0:
    jsonfile: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  klaw@1.3.1:
    klaw: private
  kleur@3.0.3:
    kleur: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash@4.17.21:
    lodash: private
  log-symbols@2.2.0:
    log-symbols: private
  logkitty@0.7.1:
    logkitty: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  make-dir@2.1.0:
    make-dir: private
  makeerror@1.0.12:
    makeerror: private
  map-cache@0.2.2:
    map-cache: private
  map-visit@1.0.0:
    map-visit: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-options@3.0.4:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  metro-babel-register@0.66.2:
    metro-babel-register: private
  metro-babel-transformer@0.66.2:
    metro-babel-transformer: private
  metro-cache-key@0.66.2:
    metro-cache-key: private
  metro-cache@0.66.2:
    metro-cache: private
  metro-config@0.66.2:
    metro-config: private
  metro-core@0.66.2:
    metro-core: private
  metro-hermes-compiler@0.66.2:
    metro-hermes-compiler: private
  metro-inspector-proxy@0.66.2:
    metro-inspector-proxy: private
  metro-minify-uglify@0.66.2:
    metro-minify-uglify: private
  metro-react-native-babel-transformer@0.66.2(@babel/core@7.28.0):
    metro-react-native-babel-transformer: private
  metro-resolver@0.66.2:
    metro-resolver: private
  metro-runtime@0.66.2:
    metro-runtime: private
  metro-source-map@0.66.2:
    metro-source-map: private
  metro-symbolicate@0.66.2:
    metro-symbolicate: private
  metro-transform-plugins@0.66.2:
    metro-transform-plugins: private
  metro-transform-worker@0.66.2:
    metro-transform-worker: private
  metro@0.66.2:
    metro: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mkdirp@0.5.6:
    mkdirp: private
  nanomatch@1.2.13:
    nanomatch: private
  natural-compare-lite@1.4.0:
    natural-compare-lite: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  nice-try@1.0.5:
    nice-try: private
  nocache@2.1.0:
    nocache: private
  node-dir@0.1.17:
    node-dir: private
  node-fetch@2.7.0:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  node-stream-zip@1.15.0:
    node-stream-zip: private
  normalize-css-color@1.0.2:
    normalize-css-color: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@2.0.2:
    npm-run-path: private
  nullthrows@1.1.1:
    nullthrows: private
  nwsapi@2.2.21:
    nwsapi: private
  ob1@0.66.2:
    ob1: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-visit@1.0.1:
    object-visit: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.pick@1.3.0:
    object.pick: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.1.0:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@6.4.0:
    open: private
  optionator@0.9.4:
    optionator: private
  options@0.0.6:
    options: private
  ora@3.4.0:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@4.0.0:
    parse-json: private
  parse5@6.0.1:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  pascalcase@0.1.1:
    pascalcase: private
  path-exists@3.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@1.9.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@4.0.1:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-up@3.1.0:
    pkg-up: private
  plist@3.1.0:
    plist: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prettier@3.6.2:
    prettier: private
  pretty-format@26.6.2:
    pretty-format: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  promise@8.3.0:
    promise: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  psl@1.15.0:
    psl: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  query-string@6.14.1:
    query-string: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  react-devtools-core@4.28.5:
    react-devtools-core: private
  react-freeze@1.0.4(react@17.0.2):
    react-freeze: private
  react-is@17.0.2:
    react-is: private
  react-native-codegen@0.0.7(@babel/preset-env@7.28.0(@babel/core@7.28.0)):
    react-native-codegen: private
  react-native-collapsible@1.6.2(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))(react@17.0.2):
    react-native-collapsible: private
  react-native-iphone-x-helper@1.3.1(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2)):
    react-native-iphone-x-helper: private
  react-native-is-edge-to-edge@1.2.1(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))(react@17.0.2):
    react-native-is-edge-to-edge: private
  react-native-modal-popover@2.1.3(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))(react@17.0.2):
    react-native-modal-popover: private
  react-native-safe-area-view@0.14.9(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))(react@17.0.2):
    react-native-safe-area-view: private
  react-native-screens@4.13.1(react-native@0.66.2(@babel/core@7.28.0)(@babel/preset-env@7.28.0(@babel/core@7.28.0))(react@17.0.2))(react@17.0.2):
    react-native-screens: private
  react-refresh@0.4.3:
    react-refresh: private
  react-shallow-renderer@16.15.0(react@17.0.2):
    react-shallow-renderer: private
  readable-stream@2.3.8:
    readable-stream: private
  readline@1.3.0:
    readline: private
  recast@0.20.5:
    recast: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  regex-not@1.0.2:
    regex-not: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  require-main-filename@2.0.0:
    require-main-filename: private
  requires-port@1.0.0:
    requires-port: private
  reselect@4.1.8:
    reselect: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-url@0.2.1:
    resolve-url: private
  resolve.exports@1.1.1:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@2.0.0:
    restore-cursor: private
  ret@0.1.15:
    ret: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rsvp@4.8.5:
    rsvp: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sane@4.1.0:
    sane: private
  sax@1.4.1:
    sax: private
  saxes@5.0.1:
    saxes: private
  scheduler@0.20.2:
    scheduler: private
  semver@6.3.1:
    semver: private
  send@0.19.0:
    send: private
  serialize-error@2.1.0:
    serialize-error: private
  serve-static@1.16.2:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  set-value@2.0.1:
    set-value: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-plist@1.3.1:
    simple-plist: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@2.1.0:
    slice-ansi: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2:
    snapdragon: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.6.1:
    source-map: private
  split-on-first@1.1.0:
    split-on-first: private
  split-string@3.1.0:
    split-string: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  stackframe@1.3.4:
    stackframe: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  static-extend@0.1.2:
    static-extend: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-buffers@2.2.0:
    stream-buffers: private
  strict-uri-encode@2.0.0:
    strict-uri-encode: private
  string-length@4.0.2:
    string-length: private
  string-natural-compare@3.0.1:
    string-natural-compare: private
  string-width@4.2.3:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-eof@1.0.0:
    strip-eof: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  sudo-prompt@9.2.1:
    sudo-prompt: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@2.3.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  symbol-tree@3.2.4:
    symbol-tree: private
  temp@0.8.4:
    temp: private
  terminal-link@2.1.1:
    terminal-link: private
  test-exclude@6.0.0:
    test-exclude: private
  text-table@0.2.0:
    text-table: private
  throat@6.0.2:
    throat: private
  through2@2.0.5:
    through2: private
  tmpl@1.0.5:
    tmpl: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@5.0.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  toidentifier@1.0.1:
    toidentifier: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@0.0.3:
    tr46: private
  tslib@1.14.1:
    tslib: private
  tslint@6.1.3(typescript@5.9.2):
    tslint: private
  tsutils@3.21.0(typescript@5.9.2):
    tsutils: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typescript@5.9.2:
    typescript: private
  ua-parser-js@1.0.40:
    ua-parser-js: private
  uglify-es@3.3.9:
    uglify-es: private
  ultron@1.0.2:
    ultron: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@7.10.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  union-value@1.0.1:
    union-value: private
  universalify@0.1.2:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unset-value@1.0.0:
    unset-value: private
  update-browserslist-db@1.1.3(browserslist@4.25.2):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  urix@0.1.0:
    urix: private
  url-parse@1.5.10:
    url-parse: private
  use-subscription@1.11.0(react@17.0.2):
    use-subscription: private
  use-sync-external-store@1.5.0(react@17.0.2):
    use-sync-external-store: private
  use@3.1.1:
    use: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utility-types@3.11.0:
    utility-types: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@3.4.0:
    uuid: private
  v8-to-istanbul@8.1.1:
    v8-to-istanbul: private
  vary@1.1.2:
    vary: private
  vlq@1.0.1:
    vlq: private
  w3c-hr-time@1.0.2:
    w3c-hr-time: private
  w3c-xmlserializer@2.0.0:
    w3c-xmlserializer: private
  walker@1.0.8:
    walker: private
  warn-once@0.1.1:
    warn-once: private
  wcwidth@1.0.1:
    wcwidth: private
  webidl-conversions@6.1.0:
    webidl-conversions: private
  whatwg-encoding@1.0.5:
    whatwg-encoding: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-mimetype@2.3.0:
    whatwg-mimetype: private
  whatwg-url@8.7.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@6.2.3:
    ws: private
  xcode@2.1.0:
    xcode: private
  xml-name-validator@3.0.0:
    xml-name-validator: private
  xmlbuilder@15.1.1:
    xmlbuilder: private
  xmlchars@2.2.0:
    xmlchars: private
  xmldoc@1.3.0:
    xmldoc: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@16.2.0:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.5.2
pendingBuilds: []
prunedAt: Mon, 11 Aug 2025 08:47:33 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\Sites\card-all\card-app\node_modules\.pnpm
virtualStoreDirMaxLength: 60
