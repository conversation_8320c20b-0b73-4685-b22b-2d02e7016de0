/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { IOSProjectConfig } from '@react-native-community/cli-types';
/**
 * Unlinks assets from iOS project. Removes references for fonts from `Info.plist`
 * fonts provided by application and from `Resources` group
 */
export default function unlinkAssetsIOS(files: any, projectConfig: IOSProjectConfig): void;
//# sourceMappingURL=unlinkAssets.d.ts.map