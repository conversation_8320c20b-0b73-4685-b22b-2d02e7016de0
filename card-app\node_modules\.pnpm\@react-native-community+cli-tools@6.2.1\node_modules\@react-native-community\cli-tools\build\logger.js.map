{"version": 3, "sources": ["../src/logger.ts"], "names": ["SEPARATOR", "verbose", "disabled", "formatMessages", "messages", "chalk", "reset", "join", "success", "console", "log", "green", "bold", "info", "cyan", "warn", "yellow", "error", "red", "debug", "gray", "setVerbose", "level", "isVerbose", "disable", "enable"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEA,MAAMA,SAAS,GAAG,IAAlB;AAEA,IAAIC,OAAO,GAAG,KAAd;AACA,IAAIC,QAAQ,GAAG,KAAf;;AAEA,MAAMC,cAAc,GAAIC,QAAD,IACrBC,iBAAMC,KAAN,CAAYF,QAAQ,CAACG,IAAT,CAAcP,SAAd,CAAZ,CADF;;AAGA,MAAMQ,OAAO,GAAG,CAAC,GAAGJ,QAAJ,KAAgC;AAC9C,MAAI,CAACF,QAAL,EAAe;AACbO,IAAAA,OAAO,CAACC,GAAR,CAAa,GAAEL,iBAAMM,KAAN,CAAYC,IAAZ,CAAiB,SAAjB,CAA4B,IAAGT,cAAc,CAACC,QAAD,CAAW,EAAvE;AACD;AACF,CAJD;;AAMA,MAAMS,IAAI,GAAG,CAAC,GAAGT,QAAJ,KAAgC;AAC3C,MAAI,CAACF,QAAL,EAAe;AACbO,IAAAA,OAAO,CAACC,GAAR,CAAa,GAAEL,iBAAMS,IAAN,CAAWF,IAAX,CAAgB,MAAhB,CAAwB,IAAGT,cAAc,CAACC,QAAD,CAAW,EAAnE;AACD;AACF,CAJD;;AAMA,MAAMW,IAAI,GAAG,CAAC,GAAGX,QAAJ,KAAgC;AAC3C,MAAI,CAACF,QAAL,EAAe;AACbO,IAAAA,OAAO,CAACM,IAAR,CAAc,GAAEV,iBAAMW,MAAN,CAAaJ,IAAb,CAAkB,MAAlB,CAA0B,IAAGT,cAAc,CAACC,QAAD,CAAW,EAAtE;AACD;AACF,CAJD;;AAMA,MAAMa,KAAK,GAAG,CAAC,GAAGb,QAAJ,KAAgC;AAC5C,MAAI,CAACF,QAAL,EAAe;AACbO,IAAAA,OAAO,CAACQ,KAAR,CAAe,GAAEZ,iBAAMa,GAAN,CAAUN,IAAV,CAAe,OAAf,CAAwB,IAAGT,cAAc,CAACC,QAAD,CAAW,EAArE;AACD;AACF,CAJD;;AAMA,MAAMe,KAAK,GAAG,CAAC,GAAGf,QAAJ,KAAgC;AAC5C,MAAIH,OAAO,IAAI,CAACC,QAAhB,EAA0B;AACxBO,IAAAA,OAAO,CAACC,GAAR,CAAa,GAAEL,iBAAMe,IAAN,CAAWR,IAAX,CAAgB,OAAhB,CAAyB,IAAGT,cAAc,CAACC,QAAD,CAAW,EAApE;AACD;AACF,CAJD;;AAMA,MAAMM,GAAG,GAAG,CAAC,GAAGN,QAAJ,KAAgC;AAC1C,MAAI,CAACF,QAAL,EAAe;AACbO,IAAAA,OAAO,CAACC,GAAR,CAAa,GAAEP,cAAc,CAACC,QAAD,CAAW,EAAxC;AACD;AACF,CAJD;;AAMA,MAAMiB,UAAU,GAAIC,KAAD,IAAoB;AACrCrB,EAAAA,OAAO,GAAGqB,KAAV;AACD,CAFD;;AAIA,MAAMC,SAAS,GAAG,MAAMtB,OAAxB;;AAEA,MAAMuB,OAAO,GAAG,MAAM;AACpBtB,EAAAA,QAAQ,GAAG,IAAX;AACD,CAFD;;AAIA,MAAMuB,MAAM,GAAG,MAAM;AACnBvB,EAAAA,QAAQ,GAAG,KAAX;AACD,CAFD;;eAIe;AACbM,EAAAA,OADa;AAEbK,EAAAA,IAFa;AAGbE,EAAAA,IAHa;AAIbE,EAAAA,KAJa;AAKbE,EAAAA,KALa;AAMbT,EAAAA,GANa;AAObW,EAAAA,UAPa;AAQbE,EAAAA,SARa;AASbC,EAAAA,OATa;AAUbC,EAAAA;AAVa,C", "sourcesContent": ["import chalk from 'chalk';\n\nconst SEPARATOR = ', ';\n\nlet verbose = false;\nlet disabled = false;\n\nconst formatMessages = (messages: Array<string>) =>\n  chalk.reset(messages.join(SEPARATOR));\n\nconst success = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.log(`${chalk.green.bold('success')} ${formatMessages(messages)}`);\n  }\n};\n\nconst info = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.log(`${chalk.cyan.bold('info')} ${formatMessages(messages)}`);\n  }\n};\n\nconst warn = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.warn(`${chalk.yellow.bold('warn')} ${formatMessages(messages)}`);\n  }\n};\n\nconst error = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.error(`${chalk.red.bold('error')} ${formatMessages(messages)}`);\n  }\n};\n\nconst debug = (...messages: Array<string>) => {\n  if (verbose && !disabled) {\n    console.log(`${chalk.gray.bold('debug')} ${formatMessages(messages)}`);\n  }\n};\n\nconst log = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.log(`${formatMessages(messages)}`);\n  }\n};\n\nconst setVerbose = (level: boolean) => {\n  verbose = level;\n};\n\nconst isVerbose = () => verbose;\n\nconst disable = () => {\n  disabled = true;\n};\n\nconst enable = () => {\n  disabled = false;\n};\n\nexport default {\n  success,\n  info,\n  warn,\n  error,\n  debug,\n  log,\n  setVerbose,\n  isVerbose,\n  disable,\n  enable,\n};\n"]}