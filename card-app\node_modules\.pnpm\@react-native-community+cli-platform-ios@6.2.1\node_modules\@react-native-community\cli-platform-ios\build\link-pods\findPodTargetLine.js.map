{"version": 3, "sources": ["../../src/link-pods/findPodTargetLine.ts"], "names": ["findPodTargetLine", "podLines", "projectName", "targetName", "replace", "targetRegex", "RegExp", "i", "len", "length", "match"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEe,SAASA,iBAAT,CACbC,QADa,EAEbC,WAFa,EAGb;AACA,QAAMC,UAAU,GAAGD,WAAW,CAACE,OAAZ,CAAoB,YAApB,EAAkC,EAAlC,CAAnB,CADA,CAEA;;AACA,QAAMC,WAAW,GAAG,IAAIC,MAAJ,CAAY,eAAcH,UAAW,UAArC,EAAgD,GAAhD,CAApB;;AACA,OAAK,IAAII,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGP,QAAQ,CAACQ,MAA/B,EAAuCF,CAAC,GAAGC,GAA3C,EAAgDD,CAAC,EAAjD,EAAqD;AACnD,UAAMG,KAAK,GAAGT,QAAQ,CAACM,CAAD,CAAR,CAAYG,KAAZ,CAAkBL,WAAlB,CAAd;;AACA,QAAIK,KAAJ,EAAW;AACT,aAAOH,CAAC,GAAG,CAAX;AACD;AACF;;AACD,SAAO,IAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport default function findPodTargetLine(\n  podLines: Array<string>,\n  projectName: string,\n) {\n  const targetName = projectName.replace('.xcodeproj', '');\n  // match first target definition in file: target 'target_name' do\n  const targetRegex = new RegExp(`target ('|\")${targetName}('|\") do`, 'g');\n  for (let i = 0, len = podLines.length; i < len; i++) {\n    const match = podLines[i].match(targetRegex);\n    if (match) {\n      return i + 1;\n    }\n  }\n  return null;\n}\n"]}