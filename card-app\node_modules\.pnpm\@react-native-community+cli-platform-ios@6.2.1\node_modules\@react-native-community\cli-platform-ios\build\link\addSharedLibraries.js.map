{"version": 3, "sources": ["../../src/link/addSharedLibraries.ts"], "names": ["addSharedLibraries", "project", "libraries", "length", "target", "get<PERSON><PERSON>t<PERSON>arget", "uuid", "name", "addFramework"], "mappings": ";;;;;;;AAQA;;;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AAIe,SAASA,kBAAT,CACbC,OADa,EAEbC,SAFa,EAGb;AACA,MAAI,CAACA,SAAS,CAACC,MAAf,EAAuB;AACrB;AACD,GAHD,CAKA;;;AACA,uCAAuBF,OAAvB,EAAgC,YAAhC;AAEA,QAAMG,MAAM,GAAGH,OAAO,CAACI,cAAR,GAAyBC,IAAxC;;AAEA,OAAK,MAAMC,IAAX,IAAmBL,SAAnB,EAA8B;AAC5BD,IAAAA,OAAO,CAACO,YAAR,CAAqBD,IAArB,EAA2B;AAACH,MAAAA;AAAD,KAA3B;AACD;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport createGroupWithMessage from './createGroupWithMessage';\n\nexport default function addSharedLibraries(\n  project: any,\n  libraries: Array<string>,\n) {\n  if (!libraries.length) {\n    return;\n  }\n\n  // Create a Frameworks group if necessary.\n  createGroupWithMessage(project, 'Frameworks');\n\n  const target = project.getFirstTarget().uuid;\n\n  for (const name of libraries) {\n    project.addFramework(name, {target});\n  }\n}\n"]}