{"version": 3, "sources": ["../../src/link/getHeadersInFolder.ts"], "names": ["GLOB_EXCLUDE_PATTERN", "getHeadersInFolder", "folder", "glob", "sync", "cwd", "nodir", "ignore", "map", "file", "path", "join"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA,MAAMA,oBAAoB,GAAG,CAC3B,iBAD2B,EAE3B,SAF2B,EAG3B,aAH2B,EAI3B,aAJ2B,CAA7B;AAOA;AACA;AACA;AACA;;AACe,SAASC,kBAAT,CAA4BC,MAA5B,EAA4C;AACzD,SAAOC,gBACJC,IADI,CACC,QADD,EACW;AACdC,IAAAA,GAAG,EAAEH,MADS;AAEdI,IAAAA,KAAK,EAAE,IAFO;AAGdC,IAAAA,MAAM,EAAEP;AAHM,GADX,EAMJQ,GANI,CAMCC,IAAD,IAAUC,gBAAKC,IAAL,CAAUT,MAAV,EAAkBO,IAAlB,CANV,CAAP;AAOD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport glob from 'glob';\nimport path from 'path';\n\nconst GLOB_EXCLUDE_PATTERN = [\n  'node_modules/**',\n  'Pods/**',\n  'Examples/**',\n  'examples/**',\n];\n\n/**\n * Given folder, it returns an array of all header files\n * inside it, ignoring node_modules and examples\n */\nexport default function getHeadersInFolder(folder: string) {\n  return glob\n    .sync('**/*.h', {\n      cwd: folder,\n      nodir: true,\n      ignore: GLOB_EXCLUDE_PATTERN,\n    })\n    .map((file) => path.join(folder, file));\n}\n"]}