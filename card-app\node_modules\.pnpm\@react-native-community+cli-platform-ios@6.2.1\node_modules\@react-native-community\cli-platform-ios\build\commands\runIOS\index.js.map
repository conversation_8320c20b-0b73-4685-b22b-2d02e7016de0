{"version": 3, "sources": ["../../../src/commands/runIOS/index.ts"], "names": ["runIOS", "_", "ctx", "args", "fs", "existsSync", "projectPath", "CLIError", "process", "chdir", "xcodeProject", "readdirSync", "inferredSchemeName", "path", "basename", "name", "extname", "scheme", "logger", "info", "isWorkspace", "chalk", "bold", "device", "udid", "runOnSimulator", "error", "devices", "out", "execa", "sync", "stderr", "stdout", "e", "warn", "find", "d", "printFoundDevices", "type", "runOnDevice", "physicalDevices", "filter", "matchingDevice", "simulators", "JSON", "parse", "child_process", "execFileSync", "encoding", "fallbackSimulators", "selectedSimulator", "reduce", "simulator", "fallback", "activeDeveloperDir", "trim", "booted", "bootSimulator", "buildOutput", "buildProject", "appPath", "getBuildPath", "configuration", "spawnSync", "stdio", "bundleID", "join", "result", "status", "success", "selected<PERSON><PERSON><PERSON>", "isIOSDeployInstalled", "appProcess", "spawn", "detached", "unref", "iosDeployInstallArgs", "iosDeployOutput", "message", "Promise", "resolve", "reject", "xcodebuildArgs", "loader", "dim", "xcodebuildOutputFormatter", "verbose", "xcbeautifyAvailable", "xcprettyAvailable", "buildProcess", "getProcessOptions", "errorOutput", "on", "data", "stringData", "toString", "stdin", "write", "isVerbose", "debug", "start", "repeat", "length", "code", "end", "stop", "undefined", "simulatorFullName", "formattedDeviceName", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buildSettings", "settings", "i", "wrapperExtension", "WRAPPER_EXTENSION", "targetBuildDir", "TARGET_BUILD_DIR", "executableFolderPath", "EXECUTABLE_FOLDER_PATH", "isCatalyst", "getPlatformName", "platformNameMatch", "exec", "execSync", "deviceName", "firstIOSDevice", "deviceByName", "String", "version", "map", "packager", "terminal", "port", "env", "RCT_TERMINAL", "RCT_METRO_PORT", "RCT_NO_LAUNCH_PACKAGER", "description", "func", "examples", "desc", "cmd", "options", "default", "Number", "getDefaultUserTerminal"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAKA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAMA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AA9BA;AACA;AACA;AACA;AACA;AACA;AACA;AAuCA,SAASA,MAAT,CAAgBC,CAAhB,EAAkCC,GAAlC,EAA+CC,IAA/C,EAA6D;AAC3D,MAAI,CAACC,cAAGC,UAAH,CAAcF,IAAI,CAACG,WAAnB,CAAL,EAAsC;AACpC,UAAM,KAAIC,oBAAJ,EACJ,4EADI,CAAN;AAGD;;AAED,4CAA4BL,GAA5B;AACA,oCAAoBA,GAApB;AAEAM,EAAAA,OAAO,CAACC,KAAR,CAAcN,IAAI,CAACG,WAAnB;AAEA,QAAMI,YAAY,GAAG,+BAAiBN,cAAGO,WAAH,CAAe,GAAf,CAAjB,CAArB;;AACA,MAAI,CAACD,YAAL,EAAmB;AACjB,UAAM,KAAIH,oBAAJ,EACH,0CAAyCJ,IAAI,CAACG,WAAY,UADvD,CAAN;AAGD;;AAED,QAAMM,kBAAkB,GAAGC,gBAAKC,QAAL,CACzBJ,YAAY,CAACK,IADY,EAEzBF,gBAAKG,OAAL,CAAaN,YAAY,CAACK,IAA1B,CAFyB,CAA3B;;AAIA,QAAME,MAAM,GAAGd,IAAI,CAACc,MAAL,IAAeL,kBAA9B;;AAEAM,qBAAOC,IAAP,CACG,eACCT,YAAY,CAACU,WAAb,GAA2B,WAA3B,GAAyC,SAC1C,KAAIC,iBAAMC,IAAN,CAAWZ,YAAY,CAACK,IAAxB,CAA8B,GAHrC,EAzB2D,CA+B3D;;;AACA,MAAI,CAACZ,IAAI,CAACoB,MAAN,IAAgB,CAACpB,IAAI,CAACqB,IAA1B,EAAgC;AAC9B,WAAOC,cAAc,CAACf,YAAD,EAAeO,MAAf,EAAuBd,IAAvB,CAArB;AACD;;AAED,MAAIA,IAAI,CAACoB,MAAL,IAAepB,IAAI,CAACqB,IAAxB,EAA8B;AAC5B,WAAON,mBAAOQ,KAAP,CACL,yDADK,CAAP;AAGD;;AAED,MAAIC,OAAJ;;AACA,MAAI;AACF,UAAMC,GAAG,GAAGC,iBAAMC,IAAN,CAAW,OAAX,EAAoB,CAAC,SAAD,EAAY,MAAZ,EAAoB,SAApB,CAApB,CAAZ;;AACAH,IAAAA,OAAO,GAAG,0CACR;AACAC,IAAAA,GAAG,CAACG,MAAJ,KAAe,EAAf,GAAoBH,GAAG,CAACI,MAAxB,GAAiCJ,GAAG,CAACG,MAF7B,CAAV;AAID,GAND,CAME,OAAOE,CAAP,EAAU;AACVf,uBAAOgB,IAAP,CACE,2EADF;;AAGAP,IAAAA,OAAO,GAAG,kCACRE,iBAAMC,IAAN,CAAW,OAAX,EAAoB,CAAC,aAAD,EAAgB,IAAhB,CAApB,EAA2CE,MADnC,CAAV;AAGD;;AAED,MAAI7B,IAAI,CAACqB,IAAT,EAAe;AACb,UAAMD,MAAM,GAAGI,OAAO,CAACQ,IAAR,CAAcC,CAAD,IAAOA,CAAC,CAACZ,IAAF,KAAWrB,IAAI,CAACqB,IAApC,CAAf;;AACA,QAAI,CAACD,MAAL,EAAa;AACX,aAAOL,mBAAOQ,KAAP,CACJ,uCAAsCL,iBAAMC,IAAN,CACrCnB,IAAI,CAACqB,IADgC,CAErC,MAAKa,iBAAiB,CAACV,OAAD,CAAU,EAH7B,CAAP;AAKD;;AACD,QAAIJ,MAAM,CAACe,IAAP,KAAgB,WAApB,EAAiC;AAC/B,aAAOb,cAAc,CAACf,YAAD,EAAeO,MAAf,EAAuBd,IAAvB,CAArB;AACD,KAFD,MAEO;AACL,aAAOoC,WAAW,CAAChB,MAAD,EAASN,MAAT,EAAiBP,YAAjB,EAA+BP,IAA/B,CAAlB;AACD;AACF,GAdD,MAcO;AACL,UAAMqC,eAAe,GAAGb,OAAO,CAACc,MAAR,CAAgBL,CAAD,IAAOA,CAAC,CAACE,IAAF,KAAW,WAAjC,CAAxB;AACA,UAAMf,MAAM,GAAGmB,cAAc,CAACF,eAAD,EAAkBrC,IAAI,CAACoB,MAAvB,CAA7B;;AACA,QAAIA,MAAJ,EAAY;AACV,aAAOgB,WAAW,CAAChB,MAAD,EAASN,MAAT,EAAiBP,YAAjB,EAA+BP,IAA/B,CAAlB;AACD;AACF;AACF;;AAED,eAAesB,cAAf,CACEf,YADF,EAEEO,MAFF,EAGEd,IAHF,EAIE;AACA,MAAIwC,UAAJ;;AACA,MAAI;AACFA,IAAAA,UAAU,GAAGC,IAAI,CAACC,KAAL,CACXC,yBAAcC,YAAd,CACE,OADF,EAEE,CAAC,QAAD,EAAW,MAAX,EAAmB,QAAnB,EAA6B,SAA7B,CAFF,EAGE;AAACC,MAAAA,QAAQ,EAAE;AAAX,KAHF,CADW,CAAb;AAOD,GARD,CAQE,OAAOtB,KAAP,EAAc;AACd,UAAM,KAAInB,oBAAJ,EACJ,6IADI,EAEJmB,KAFI,CAAN;AAID;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE,QAAMuB,kBAAkB,GAAG,CAAC,WAAD,EAAc,WAAd,EAA2B,WAA3B,CAA3B;AACA,QAAMC,iBAAiB,GAAGD,kBAAkB,CAACE,MAAnB,CAA0B,CAACC,SAAD,EAAYC,QAAZ,KAAyB;AAC3E,WACED,SAAS,IAAI,oCAAsBT,UAAtB,EAAkC;AAACS,MAAAA,SAAS,EAAEC;AAAZ,KAAlC,CADf;AAGD,GAJyB,EAIvB,oCAAsBV,UAAtB,EAAkCxC,IAAlC,CAJuB,CAA1B;;AAMA,MAAI,CAAC+C,iBAAL,EAAwB;AACtB,UAAM,KAAI3C,oBAAJ,EACH,+BACCJ,IAAI,CAACiD,SAAL,GAAkB,SAAQjD,IAAI,CAACiD,SAAU,GAAzC,GAA+C,SAAQjD,IAAI,CAACqB,IAAK,GAClE,EAHG,CAAN;AAKD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACE,QAAM8B,kBAAkB,GAAGR,yBACxBC,YADwB,CACX,cADW,EACK,CAAC,IAAD,CADL,EACa;AAACC,IAAAA,QAAQ,EAAE;AAAX,GADb,EAExBO,IAFwB,EAA3B;;AAIAT,2BAAcC,YAAd,CAA2B,MAA3B,EAAmC,CAChC,GAAEO,kBAAmB,6BADW,EAEjC,QAFiC,EAGjC,oBAHiC,EAIjCJ,iBAAiB,CAAC1B,IAJe,CAAnC;;AAOA,MAAI,CAAC0B,iBAAiB,CAACM,MAAvB,EAA+B;AAC7BC,IAAAA,aAAa,CAACP,iBAAD,CAAb;AACD;;AAED,QAAMQ,WAAW,GAAG,MAAMC,YAAY,CACpCjD,YADoC,EAEpCwC,iBAAiB,CAAC1B,IAFkB,EAGpCP,MAHoC,EAIpCd,IAJoC,CAAtC;AAOA,QAAMyD,OAAO,GAAGC,YAAY,CAC1BnD,YAD0B,EAE1BP,IAAI,CAAC2D,aAFqB,EAG1BJ,WAH0B,EAI1BzC,MAJ0B,CAA5B;;AAOAC,qBAAOC,IAAP,CAAa,eAAcE,iBAAMC,IAAN,CAAWsC,OAAX,CAAoB,GAA/C;;AAEAd,2BAAciB,SAAd,CACE,OADF,EAEE,CAAC,QAAD,EAAW,SAAX,EAAsBb,iBAAiB,CAAC1B,IAAxC,EAA8CoC,OAA9C,CAFF,EAGE;AAACI,IAAAA,KAAK,EAAE;AAAR,GAHF;;AAMA,QAAMC,QAAQ,GAAGnB,yBACdC,YADc,CAEb,yBAFa,EAGb,CAAC,IAAD,EAAO,0BAAP,EAAmClC,gBAAKqD,IAAL,CAAUN,OAAV,EAAmB,YAAnB,CAAnC,CAHa,EAIb;AAACZ,IAAAA,QAAQ,EAAE;AAAX,GAJa,EAMdO,IANc,EAAjB;;AAQArC,qBAAOC,IAAP,CAAa,cAAaE,iBAAMC,IAAN,CAAW2C,QAAX,CAAqB,GAA/C;;AAEA,QAAME,MAAM,GAAGrB,yBAAciB,SAAd,CAAwB,OAAxB,EAAiC,CAC9C,QAD8C,EAE9C,QAF8C,EAG9Cb,iBAAiB,CAAC1B,IAH4B,EAI9CyC,QAJ8C,CAAjC,CAAf;;AAOA,MAAIE,MAAM,CAACC,MAAP,KAAkB,CAAtB,EAAyB;AACvBlD,uBAAOmD,OAAP,CAAe,gDAAf;AACD,GAFD,MAEO;AACLnD,uBAAOQ,KAAP,CAAa,uCAAb,EAAsDyC,MAAM,CAACpC,MAA7D;AACD;AACF;;AAED,eAAeQ,WAAf,CACE+B,cADF,EAEErD,MAFF,EAGEP,YAHF,EAIEP,IAJF,EAKE;AACA,QAAMoE,oBAAoB,GAAGzB,yBAAciB,SAAd,CAC3B,YAD2B,EAE3B,CAAC,WAAD,CAF2B,EAG3B;AAACf,IAAAA,QAAQ,EAAE;AAAX,GAH2B,CAA7B;;AAMA,MAAIuB,oBAAoB,CAAC7C,KAAzB,EAAgC;AAC9B,UAAM,KAAInB,oBAAJ,EACH,+HAA8Hc,iBAAMC,IAAN,CAC7H,2BAD6H,CAE7H,kBAHE,CAAN;AAKD;;AAED,QAAMoC,WAAW,GAAG,MAAMC,YAAY,CACpCjD,YADoC,EAEpC4D,cAAc,CAAC9C,IAFqB,EAGpCP,MAHoC,EAIpCd,IAJoC,CAAtC;;AAOA,MAAImE,cAAc,CAAChC,IAAf,KAAwB,UAA5B,EAAwC;AACtC,UAAMsB,OAAO,GAAGC,YAAY,CAC1BnD,YAD0B,EAE1BP,IAAI,CAAC2D,aAFqB,EAG1BJ,WAH0B,EAI1BzC,MAJ0B,EAK1B,IAL0B,CAA5B;;AAOA,UAAMuD,UAAU,GAAG1B,yBAAc2B,KAAd,CAAqB,GAAEb,OAAQ,IAAG3C,MAAO,EAAzC,EAA4C,EAA5C,EAAgD;AACjEyD,MAAAA,QAAQ,EAAE,IADuD;AAEjEV,MAAAA,KAAK,EAAE;AAF0D,KAAhD,CAAnB;;AAIAQ,IAAAA,UAAU,CAACG,KAAX;AACD,GAbD,MAaO;AACL,UAAMC,oBAAoB,GAAG,CAC3B,UAD2B,EAE3Bf,YAAY,CAACnD,YAAD,EAAeP,IAAI,CAAC2D,aAApB,EAAmCJ,WAAnC,EAAgDzC,MAAhD,CAFe,EAG3B,MAH2B,EAI3BqD,cAAc,CAAC9C,IAJY,EAK3B,cAL2B,CAA7B;;AAQAN,uBAAOC,IAAP,CAAa,wCAAuCmD,cAAc,CAACvD,IAAK,EAAxE;;AAEA,UAAM8D,eAAe,GAAG/B,yBAAciB,SAAd,CACtB,YADsB,EAEtBa,oBAFsB,EAGtB;AAAC5B,MAAAA,QAAQ,EAAE;AAAX,KAHsB,CAAxB;;AAMA,QAAI6B,eAAe,CAACnD,KAApB,EAA2B;AACzB,YAAM,KAAInB,oBAAJ,EACH,gGAA+FsE,eAAe,CAACnD,KAAhB,CAAsBoD,OAAQ,EAD1H,CAAN;AAGD;AACF;;AAED,SAAO5D,mBAAOmD,OAAP,CAAe,kCAAf,CAAP;AACD;;AAED,SAASV,YAAT,CACEjD,YADF,EAEEc,IAFF,EAGEP,MAHF,EAIEd,IAJF,EAKmB;AACjB,SAAO,IAAI4E,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtC,UAAMC,cAAc,GAAG,CACrBxE,YAAY,CAACU,WAAb,GAA2B,YAA3B,GAA0C,UADrB,EAErBV,YAAY,CAACK,IAFQ,EAGrB,gBAHqB,EAIrBZ,IAAI,CAAC2D,aAJgB,EAKrB,SALqB,EAMrB7C,MANqB,EAOrB,cAPqB,EAQpB,MAAKO,IAAK,EARU,CAAvB;AAUA,UAAM2D,MAAM,GAAG,qBAAf;;AACAjE,uBAAOC,IAAP,CACG,YAAWE,iBAAM+D,GAAN,CACT,sBAAqBF,cAAc,CAAChB,IAAf,CAAoB,GAApB,CAAyB,IADrC,CAEV,EAHJ;;AAKA,QAAImB,yBAAJ;;AACA,QAAI,CAAClF,IAAI,CAACmF,OAAV,EAAmB;AACjB,UAAIC,mBAAmB,EAAvB,EAA2B;AACzBF,QAAAA,yBAAyB,GAAGvC,yBAAc2B,KAAd,CAAoB,YAApB,EAAkC,EAAlC,EAAsC;AAChET,UAAAA,KAAK,EAAE,CAAC,MAAD,EAASxD,OAAO,CAACwB,MAAjB,EAAyBxB,OAAO,CAACuB,MAAjC;AADyD,SAAtC,CAA5B;AAGD,OAJD,MAIO,IAAIyD,iBAAiB,EAArB,EAAyB;AAC9BH,QAAAA,yBAAyB,GAAGvC,yBAAc2B,KAAd,CAAoB,UAApB,EAAgC,EAAhC,EAAoC;AAC9DT,UAAAA,KAAK,EAAE,CAAC,MAAD,EAASxD,OAAO,CAACwB,MAAjB,EAAyBxB,OAAO,CAACuB,MAAjC;AADuD,SAApC,CAA5B;AAGD;AACF;;AACD,UAAM0D,YAAY,GAAG3C,yBAAc2B,KAAd,CACnB,YADmB,EAEnBS,cAFmB,EAGnBQ,iBAAiB,CAACvF,IAAD,CAHE,CAArB;;AAKA,QAAIuD,WAAW,GAAG,EAAlB;AACA,QAAIiC,WAAW,GAAG,EAAlB;AACAF,IAAAA,YAAY,CAACzD,MAAb,CAAoB4D,EAApB,CAAuB,MAAvB,EAAgCC,IAAD,IAAkB;AAC/C,YAAMC,UAAU,GAAGD,IAAI,CAACE,QAAL,EAAnB;AACArC,MAAAA,WAAW,IAAIoC,UAAf;;AACA,UAAIT,yBAAJ,EAA+B;AAC7BA,QAAAA,yBAAyB,CAACW,KAA1B,CAAgCC,KAAhC,CAAsCJ,IAAtC;AACD,OAFD,MAEO;AACL,YAAI3E,mBAAOgF,SAAP,EAAJ,EAAwB;AACtBhF,6BAAOiF,KAAP,CAAaL,UAAb;AACD,SAFD,MAEO;AACLX,UAAAA,MAAM,CAACiB,KAAP,CACG,mBAAkB,IAAIC,MAAJ,CAAW3C,WAAW,CAAC4C,MAAZ,GAAqB,EAAhC,CAAoC,EADzD;AAGD;AACF;AACF,KAdD;AAeAb,IAAAA,YAAY,CAAC1D,MAAb,CAAoB6D,EAApB,CAAuB,MAAvB,EAAgCC,IAAD,IAAkB;AAC/CF,MAAAA,WAAW,IAAIE,IAAf;AACD,KAFD;AAGAJ,IAAAA,YAAY,CAACG,EAAb,CAAgB,OAAhB,EAA0BW,IAAD,IAAkB;AACzC,UAAIlB,yBAAJ,EAA+B;AAC7BA,QAAAA,yBAAyB,CAACW,KAA1B,CAAgCQ,GAAhC;AACD,OAFD,MAEO;AACLrB,QAAAA,MAAM,CAACsB,IAAP;AACD;;AACD,UAAIF,IAAI,KAAK,CAAb,EAAgB;AACdtB,QAAAA,MAAM,CACJ,KAAI1E,oBAAJ,EACG;AACb;AACA;AACA,wEAAwEgG,IAAK;AAC7E;AACA,cAAc7F,YAAY,CAACK,IAAK;AAChC,WAPU,EAQEsE,yBAAyB,GACrBqB,SADqB,GAErBhD,WAAW,GAAG,IAAd,GAAqBiC,WAV3B,CADI,CAAN;AAcA;AACD;;AACDzE,yBAAOmD,OAAP,CAAe,4BAAf;;AACAW,MAAAA,OAAO,CAACtB,WAAD,CAAP;AACD,KAzBD;AA0BD,GAhFM,CAAP;AAiFD;;AAED,SAASD,aAAT,CAAuBP,iBAAvB,EAAkD;AAChD,QAAMyD,iBAAiB,GAAGC,mBAAmB,CAAC1D,iBAAD,CAA7C;;AACAhC,qBAAOC,IAAP,CAAa,aAAYwF,iBAAkB,EAA3C;;AAEA7D,2BAAciB,SAAd,CAAwB,OAAxB,EAAiC,CAAC,QAAD,EAAW,MAAX,EAAmBb,iBAAiB,CAAC1B,IAArC,CAAjC;AACD;;AAED,SAASqF,cAAT,CAAwBC,aAAxB,EAA+C;AAC7C,QAAMC,QAAQ,GAAGnE,IAAI,CAACC,KAAL,CAAWiE,aAAX,CAAjB,CAD6C,CAG7C;;AACA,OAAK,MAAME,CAAX,IAAgBD,QAAhB,EAA0B;AACxB,UAAME,gBAAgB,GAAGF,QAAQ,CAACC,CAAD,CAAR,CAAYF,aAAZ,CAA0BI,iBAAnD;;AAEA,QAAID,gBAAgB,KAAK,KAAzB,EAAgC;AAC9B,aAAO;AACLE,QAAAA,cAAc,EAAEJ,QAAQ,CAACC,CAAD,CAAR,CAAYF,aAAZ,CAA0BM,gBADrC;AAELC,QAAAA,oBAAoB,EAAEN,QAAQ,CAACC,CAAD,CAAR,CAAYF,aAAZ,CAA0BQ;AAF3C,OAAP;AAID;AACF;;AAED,SAAO,EAAP;AACD;;AAED,SAASzD,YAAT,CACEnD,YADF,EAEEoD,aAFF,EAGEJ,WAHF,EAIEzC,MAJF,EAKEsG,UAAmB,GAAG,KALxB,EAME;AACA,QAAMT,aAAa,GAAGhE,yBAAcC,YAAd,CACpB,YADoB,EAEpB,CACErC,YAAY,CAACU,WAAb,GAA2B,YAA3B,GAA0C,UAD5C,EAEEV,YAAY,CAACK,IAFf,EAGE,SAHF,EAIEE,MAJF,EAKE,MALF,EAMEuG,eAAe,CAAC9D,WAAD,CANjB,EAOE,gBAPF,EAQEI,aARF,EASE,oBATF,EAUE,OAVF,CAFoB,EAcpB;AAACd,IAAAA,QAAQ,EAAE;AAAX,GAdoB,CAAtB;;AAgBA,QAAM;AAACmE,IAAAA,cAAD;AAAiBE,IAAAA;AAAjB,MAAyCR,cAAc,CAACC,aAAD,CAA7D;;AAEA,MAAI,CAACK,cAAL,EAAqB;AACnB,UAAM,KAAI5G,oBAAJ,EAAa,2CAAb,CAAN;AACD;;AAED,MAAI,CAAC8G,oBAAL,EAA2B;AACzB,UAAM,KAAI9G,oBAAJ,EAAa,6BAAb,CAAN;AACD;;AAED,SAAQ,GAAE4G,cAAe,GACvBI,UAAU,GAAG,cAAH,GAAoB,EAC/B,IAAGF,oBAAqB,EAFzB;AAGD;;AAED,SAASG,eAAT,CAAyB9D,WAAzB,EAA8C;AAC5C;AACA,QAAM+D,iBAAiB,GAAG,sCAAsCC,IAAtC,CACxBhE,WADwB,CAA1B;;AAGA,MAAI,CAAC+D,iBAAL,EAAwB;AACtB,UAAM,KAAIlH,oBAAJ,EACJ,iIADI,CAAN;AAGD;;AACD,SAAOkH,iBAAiB,CAAC,CAAD,CAAxB;AACD;;AAED,SAASlC,mBAAT,GAA+B;AAC7B,MAAI;AACFzC,6BAAc6E,QAAd,CAAuB,sBAAvB,EAA+C;AAC7C3D,MAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,MAAJ,EAAY,QAAZ;AADsC,KAA/C;AAGD,GAJD,CAIE,OAAOtC,KAAP,EAAc;AACd,WAAO,KAAP;AACD;;AACD,SAAO,IAAP;AACD;;AAED,SAAS8D,iBAAT,GAA6B;AAC3B,MAAI;AACF1C,6BAAc6E,QAAd,CAAuB,oBAAvB,EAA6C;AAC3C3D,MAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,MAAJ,EAAY,QAAZ;AADoC,KAA7C;AAGD,GAJD,CAIE,OAAOtC,KAAP,EAAc;AACd,WAAO,KAAP;AACD;;AACD,SAAO,IAAP;AACD;;AAED,SAASgB,cAAT,CACEf,OADF,EAEEiG,UAFF,EAGE;AACA,MAAIA,UAAU,KAAK,IAAnB,EAAyB;AACvB,UAAMC,cAAc,GAAGlG,OAAO,CAACQ,IAAR,CAAcC,CAAD,IAAOA,CAAC,CAACE,IAAF,KAAW,QAA/B,CAAvB;;AACA,QAAIuF,cAAJ,EAAoB;AAClB3G,yBAAOC,IAAP,CACG,uCAAsCE,iBAAMC,IAAN,CACrCuG,cAAc,CAAC9G,IADsB,CAErC,iCAHJ;;AAKA,aAAO8G,cAAP;AACD,KAPD,MAOO;AACL3G,yBAAOQ,KAAP,CAAa,2BAAb;;AACA,aAAOgF,SAAP;AACD;AACF;;AACD,QAAMoB,YAAY,GAAGnG,OAAO,CAACQ,IAAR,CAClBZ,MAAD,IACEA,MAAM,CAACR,IAAP,KAAgB6G,UAAhB,IAA8BhB,mBAAmB,CAACrF,MAAD,CAAnB,KAAgCqG,UAF7C,CAArB;;AAIA,MAAI,CAACE,YAAL,EAAmB;AACjB5G,uBAAOQ,KAAP,CACG,mCAAkCL,iBAAMC,IAAN,CACjCyG,MAAM,CAACH,UAAD,CAD2B,CAEjC,MAAKvF,iBAAiB,CAACV,OAAD,CAAU,EAHpC;AAKD;;AACD,SAAOmG,YAAP;AACD;;AAED,SAASlB,mBAAT,CAA6BxD,SAA7B,EAAgD;AAC9C,SAAOA,SAAS,CAAC4E,OAAV,GACF,GAAE5E,SAAS,CAACrC,IAAK,KAAIqC,SAAS,CAAC4E,OAAQ,GADrC,GAEH5E,SAAS,CAACrC,IAFd;AAGD;;AAED,SAASsB,iBAAT,CAA2BV,OAA3B,EAAmD;AACjD,SAAO,CACL,oBADK,EAEL,GAAGA,OAAO,CAACsG,GAAR,CAAa1G,MAAD,IAAa,OAAMA,MAAM,CAACR,IAAK,KAAIQ,MAAM,CAACC,IAAK,GAA3D,CAFE,EAGL0C,IAHK,CAGA,IAHA,CAAP;AAID;;AAED,SAASwB,iBAAT,CAA2B;AACzBwC,EAAAA,QADyB;AAEzBC,EAAAA,QAFyB;AAGzBC,EAAAA;AAHyB,CAA3B,EAQ6B;AAC3B,MAAIF,QAAJ,EAAc;AACZ,WAAO;AACLG,MAAAA,GAAG,EAAE,EACH,GAAG7H,OAAO,CAAC6H,GADR;AAEHC,QAAAA,YAAY,EAAEH,QAFX;AAGHI,QAAAA,cAAc,EAAEH,IAAI,CAACrC,QAAL;AAHb;AADA,KAAP;AAOD;;AAED,SAAO;AACLsC,IAAAA,GAAG,EAAE,EACH,GAAG7H,OAAO,CAAC6H,GADR;AAEHC,MAAAA,YAAY,EAAEH,QAFX;AAGHK,MAAAA,sBAAsB,EAAE;AAHrB;AADA,GAAP;AAOD;;eAEc;AACbzH,EAAAA,IAAI,EAAE,SADO;AAEb0H,EAAAA,WAAW,EAAE,gDAFA;AAGbC,EAAAA,IAAI,EAAE1I,MAHO;AAIb2I,EAAAA,QAAQ,EAAE,CACR;AACEC,IAAAA,IAAI,EAAE,+DADR;AAEEC,IAAAA,GAAG,EAAE;AAFP,GADQ,EAKR;AACED,IAAAA,IAAI,EAAE,+CADR;AAEEC,IAAAA,GAAG,EAAE;AAFP,GALQ,EASR;AACED,IAAAA,IAAI,EAAE,8CADR;AAEEC,IAAAA,GAAG,EAAE;AAFP,GATQ,EAaR;AACED,IAAAA,IAAI,EAAE,8BADR;AAEEC,IAAAA,GAAG,EACD;AAHJ,GAbQ,CAJG;AAuBbC,EAAAA,OAAO,EAAE,CACP;AACE/H,IAAAA,IAAI,EAAE,sBADR;AAEE0H,IAAAA,WAAW,EACT,6EACA,qEAJJ;AAKEM,IAAAA,OAAO,EAAE;AALX,GADO,EAQP;AACEhI,IAAAA,IAAI,EAAE,0BADR;AAEE0H,IAAAA,WAAW,EAAE,gDAFf;AAGEM,IAAAA,OAAO,EAAE;AAHX,GARO,EAaP;AACEhI,IAAAA,IAAI,EAAE,mBADR;AAEE0H,IAAAA,WAAW,EAAE;AAFf,GAbO,EAiBP;AACE1H,IAAAA,IAAI,EAAE,yBADR;AAEE0H,IAAAA,WAAW,EACT,2DACA,qBAJJ;AAKEM,IAAAA,OAAO,EAAE;AALX,GAjBO,EAwBP;AACEhI,IAAAA,IAAI,EAAE,mBADR;AAEE0H,IAAAA,WAAW,EACT;AAHJ,GAxBO,EA6BP;AACE1H,IAAAA,IAAI,EAAE,iBADR;AAEE0H,IAAAA,WAAW,EAAE;AAFf,GA7BO,EAiCP;AACE1H,IAAAA,IAAI,EAAE,eADR;AAEE0H,IAAAA,WAAW,EAAE;AAFf,GAjCO,EAqCP;AACE1H,IAAAA,IAAI,EAAE,WADR;AAEE0H,IAAAA,WAAW,EAAE;AAFf,GArCO,EAyCP;AACE1H,IAAAA,IAAI,EAAE,iBADR;AAEEgI,IAAAA,OAAO,EAAEvI,OAAO,CAAC6H,GAAR,CAAYE,cAAZ,IAA8B,IAFzC;AAGE1F,IAAAA,KAAK,EAAEmG;AAHT,GAzCO,EA8CP;AACEjI,IAAAA,IAAI,EAAE,qBADR;AAEE0H,IAAAA,WAAW,EACT,+EAHJ;AAIEM,IAAAA,OAAO,EAAEE;AAJX,GA9CO;AAvBI,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport child_process, {\n  ChildProcess,\n  // @ts-ignore\n  SpawnOptionsWithoutStdio,\n} from 'child_process';\nimport fs from 'fs';\nimport path from 'path';\nimport chalk from 'chalk';\nimport {Config} from '@react-native-community/cli-types';\nimport findXcodeProject, {ProjectInfo} from './findXcodeProject';\nimport parseIOSDevicesList from './parseIOSDevicesList';\nimport parseXctraceIOSDevicesList from './parseXctraceIOSDevicesList';\nimport findMatchingSimulator from './findMatchingSimulator';\nimport warnAboutManuallyLinkedLibs from '../../link/warnAboutManuallyLinkedLibs';\nimport warnAboutPodInstall from '../../link/warnAboutPodInstall';\nimport {\n  logger,\n  CLIError,\n  getDefaultUserTerminal,\n} from '@react-native-community/cli-tools';\nimport {Device} from '../../types';\nimport ora from 'ora';\nimport execa from 'execa';\n\ntype FlagsT = {\n  simulator?: string;\n  configuration: string;\n  scheme?: string;\n  projectPath: string;\n  device?: string | true;\n  udid?: string;\n  packager: boolean;\n  verbose: boolean;\n  port: number;\n  terminal: string | undefined;\n};\n\nfunction runIOS(_: Array<string>, ctx: Config, args: FlagsT) {\n  if (!fs.existsSync(args.projectPath)) {\n    throw new CLIError(\n      'iOS project folder not found. Are you sure this is a React Native project?',\n    );\n  }\n\n  warnAboutManuallyLinkedLibs(ctx);\n  warnAboutPodInstall(ctx);\n\n  process.chdir(args.projectPath);\n\n  const xcodeProject = findXcodeProject(fs.readdirSync('.'));\n  if (!xcodeProject) {\n    throw new CLIError(\n      `Could not find Xcode project files in \"${args.projectPath}\" folder`,\n    );\n  }\n\n  const inferredSchemeName = path.basename(\n    xcodeProject.name,\n    path.extname(xcodeProject.name),\n  );\n  const scheme = args.scheme || inferredSchemeName;\n\n  logger.info(\n    `Found Xcode ${\n      xcodeProject.isWorkspace ? 'workspace' : 'project'\n    } \"${chalk.bold(xcodeProject.name)}\"`,\n  );\n\n  // No need to load all available devices\n  if (!args.device && !args.udid) {\n    return runOnSimulator(xcodeProject, scheme, args);\n  }\n\n  if (args.device && args.udid) {\n    return logger.error(\n      'The `device` and `udid` options are mutually exclusive.',\n    );\n  }\n\n  let devices;\n  try {\n    const out = execa.sync('xcrun', ['xctrace', 'list', 'devices']);\n    devices = parseXctraceIOSDevicesList(\n      // Xcode 12.5 introduced a change to output the list to stdout instead of stderr\n      out.stderr === '' ? out.stdout : out.stderr,\n    );\n  } catch (e) {\n    logger.warn(\n      'Support for Xcode 11 and older is deprecated. Please upgrade to Xcode 12.',\n    );\n    devices = parseIOSDevicesList(\n      execa.sync('xcrun', ['instruments', '-s']).stdout,\n    );\n  }\n\n  if (args.udid) {\n    const device = devices.find((d) => d.udid === args.udid);\n    if (!device) {\n      return logger.error(\n        `Could not find a device with udid: \"${chalk.bold(\n          args.udid,\n        )}\". ${printFoundDevices(devices)}`,\n      );\n    }\n    if (device.type === 'simulator') {\n      return runOnSimulator(xcodeProject, scheme, args);\n    } else {\n      return runOnDevice(device, scheme, xcodeProject, args);\n    }\n  } else {\n    const physicalDevices = devices.filter((d) => d.type !== 'simulator');\n    const device = matchingDevice(physicalDevices, args.device);\n    if (device) {\n      return runOnDevice(device, scheme, xcodeProject, args);\n    }\n  }\n}\n\nasync function runOnSimulator(\n  xcodeProject: ProjectInfo,\n  scheme: string,\n  args: FlagsT,\n) {\n  let simulators: {devices: {[index: string]: Array<Device>}};\n  try {\n    simulators = JSON.parse(\n      child_process.execFileSync(\n        'xcrun',\n        ['simctl', 'list', '--json', 'devices'],\n        {encoding: 'utf8'},\n      ),\n    );\n  } catch (error) {\n    throw new CLIError(\n      'Could not get the simulator list from Xcode. Please open Xcode and try running project directly from there to resolve the remaining issues.',\n      error,\n    );\n  }\n\n  /**\n   * If provided simulator does not exist, try simulators in following order\n   * - iPhone 13\n   * - iPhone 12\n   * - iPhone 11\n   */\n  const fallbackSimulators = ['iPhone 13', 'iPhone 12', 'iPhone 11'];\n  const selectedSimulator = fallbackSimulators.reduce((simulator, fallback) => {\n    return (\n      simulator || findMatchingSimulator(simulators, {simulator: fallback})\n    );\n  }, findMatchingSimulator(simulators, args));\n\n  if (!selectedSimulator) {\n    throw new CLIError(\n      `No simulator available with ${\n        args.simulator ? `name \"${args.simulator}\"` : `udid \"${args.udid}\"`\n      }`,\n    );\n  }\n\n  /**\n   * Booting simulator through `xcrun simctl boot` will boot it in the `headless` mode\n   * (running in the background).\n   *\n   * In order for user to see the app and the simulator itself, we have to make sure\n   * that the Simulator.app is running.\n   *\n   * We also pass it `-CurrentDeviceUDID` so that when we launch it for the first time,\n   * it will not boot the \"default\" device, but the one we set. If the app is already running,\n   * this flag has no effect.\n   */\n  const activeDeveloperDir = child_process\n    .execFileSync('xcode-select', ['-p'], {encoding: 'utf8'})\n    .trim();\n\n  child_process.execFileSync('open', [\n    `${activeDeveloperDir}/Applications/Simulator.app`,\n    '--args',\n    '-CurrentDeviceUDID',\n    selectedSimulator.udid,\n  ]);\n\n  if (!selectedSimulator.booted) {\n    bootSimulator(selectedSimulator);\n  }\n\n  const buildOutput = await buildProject(\n    xcodeProject,\n    selectedSimulator.udid,\n    scheme,\n    args,\n  );\n\n  const appPath = getBuildPath(\n    xcodeProject,\n    args.configuration,\n    buildOutput,\n    scheme,\n  );\n\n  logger.info(`Installing \"${chalk.bold(appPath)}\"`);\n\n  child_process.spawnSync(\n    'xcrun',\n    ['simctl', 'install', selectedSimulator.udid, appPath],\n    {stdio: 'inherit'},\n  );\n\n  const bundleID = child_process\n    .execFileSync(\n      '/usr/libexec/PlistBuddy',\n      ['-c', 'Print:CFBundleIdentifier', path.join(appPath, 'Info.plist')],\n      {encoding: 'utf8'},\n    )\n    .trim();\n\n  logger.info(`Launching \"${chalk.bold(bundleID)}\"`);\n\n  const result = child_process.spawnSync('xcrun', [\n    'simctl',\n    'launch',\n    selectedSimulator.udid,\n    bundleID,\n  ]);\n\n  if (result.status === 0) {\n    logger.success('Successfully launched the app on the simulator');\n  } else {\n    logger.error('Failed to launch the app on simulator', result.stderr);\n  }\n}\n\nasync function runOnDevice(\n  selectedDevice: Device,\n  scheme: string,\n  xcodeProject: ProjectInfo,\n  args: FlagsT,\n) {\n  const isIOSDeployInstalled = child_process.spawnSync(\n    'ios-deploy',\n    ['--version'],\n    {encoding: 'utf8'},\n  );\n\n  if (isIOSDeployInstalled.error) {\n    throw new CLIError(\n      `Failed to install the app on the device because we couldn't execute the \"ios-deploy\" command. Please install it by running \"${chalk.bold(\n        'npm install -g ios-deploy',\n      )}\" and try again.`,\n    );\n  }\n\n  const buildOutput = await buildProject(\n    xcodeProject,\n    selectedDevice.udid,\n    scheme,\n    args,\n  );\n\n  if (selectedDevice.type === 'catalyst') {\n    const appPath = getBuildPath(\n      xcodeProject,\n      args.configuration,\n      buildOutput,\n      scheme,\n      true,\n    );\n    const appProcess = child_process.spawn(`${appPath}/${scheme}`, [], {\n      detached: true,\n      stdio: 'ignore',\n    });\n    appProcess.unref();\n  } else {\n    const iosDeployInstallArgs = [\n      '--bundle',\n      getBuildPath(xcodeProject, args.configuration, buildOutput, scheme),\n      '--id',\n      selectedDevice.udid,\n      '--justlaunch',\n    ];\n\n    logger.info(`Installing and launching your app on ${selectedDevice.name}`);\n\n    const iosDeployOutput = child_process.spawnSync(\n      'ios-deploy',\n      iosDeployInstallArgs,\n      {encoding: 'utf8'},\n    );\n\n    if (iosDeployOutput.error) {\n      throw new CLIError(\n        `Failed to install the app on the device. We've encountered an error in \"ios-deploy\" command: ${iosDeployOutput.error.message}`,\n      );\n    }\n  }\n\n  return logger.success('Installed the app on the device.');\n}\n\nfunction buildProject(\n  xcodeProject: ProjectInfo,\n  udid: string | undefined,\n  scheme: string,\n  args: FlagsT,\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const xcodebuildArgs = [\n      xcodeProject.isWorkspace ? '-workspace' : '-project',\n      xcodeProject.name,\n      '-configuration',\n      args.configuration,\n      '-scheme',\n      scheme,\n      '-destination',\n      `id=${udid}`,\n    ];\n    const loader = ora();\n    logger.info(\n      `Building ${chalk.dim(\n        `(using \"xcodebuild ${xcodebuildArgs.join(' ')}\")`,\n      )}`,\n    );\n    let xcodebuildOutputFormatter: ChildProcess | any;\n    if (!args.verbose) {\n      if (xcbeautifyAvailable()) {\n        xcodebuildOutputFormatter = child_process.spawn('xcbeautify', [], {\n          stdio: ['pipe', process.stdout, process.stderr],\n        });\n      } else if (xcprettyAvailable()) {\n        xcodebuildOutputFormatter = child_process.spawn('xcpretty', [], {\n          stdio: ['pipe', process.stdout, process.stderr],\n        });\n      }\n    }\n    const buildProcess = child_process.spawn(\n      'xcodebuild',\n      xcodebuildArgs,\n      getProcessOptions(args),\n    );\n    let buildOutput = '';\n    let errorOutput = '';\n    buildProcess.stdout.on('data', (data: Buffer) => {\n      const stringData = data.toString();\n      buildOutput += stringData;\n      if (xcodebuildOutputFormatter) {\n        xcodebuildOutputFormatter.stdin.write(data);\n      } else {\n        if (logger.isVerbose()) {\n          logger.debug(stringData);\n        } else {\n          loader.start(\n            `Building the app${'.'.repeat(buildOutput.length % 10)}`,\n          );\n        }\n      }\n    });\n    buildProcess.stderr.on('data', (data: Buffer) => {\n      errorOutput += data;\n    });\n    buildProcess.on('close', (code: number) => {\n      if (xcodebuildOutputFormatter) {\n        xcodebuildOutputFormatter.stdin.end();\n      } else {\n        loader.stop();\n      }\n      if (code !== 0) {\n        reject(\n          new CLIError(\n            `\n            Failed to build iOS project.\n\n            We ran \"xcodebuild\" command but it exited with error code ${code}. To debug build\n            logs further, consider building your app with Xcode.app, by opening\n            ${xcodeProject.name}.\n          `,\n            xcodebuildOutputFormatter\n              ? undefined\n              : buildOutput + '\\n' + errorOutput,\n          ),\n        );\n        return;\n      }\n      logger.success('Successfully built the app');\n      resolve(buildOutput);\n    });\n  });\n}\n\nfunction bootSimulator(selectedSimulator: Device) {\n  const simulatorFullName = formattedDeviceName(selectedSimulator);\n  logger.info(`Launching ${simulatorFullName}`);\n\n  child_process.spawnSync('xcrun', ['simctl', 'boot', selectedSimulator.udid]);\n}\n\nfunction getTargetPaths(buildSettings: string) {\n  const settings = JSON.parse(buildSettings);\n\n  // Find app in all building settings - look for WRAPPER_EXTENSION: 'app',\n  for (const i in settings) {\n    const wrapperExtension = settings[i].buildSettings.WRAPPER_EXTENSION;\n\n    if (wrapperExtension === 'app') {\n      return {\n        targetBuildDir: settings[i].buildSettings.TARGET_BUILD_DIR,\n        executableFolderPath: settings[i].buildSettings.EXECUTABLE_FOLDER_PATH,\n      };\n    }\n  }\n\n  return {};\n}\n\nfunction getBuildPath(\n  xcodeProject: ProjectInfo,\n  configuration: string,\n  buildOutput: string,\n  scheme: string,\n  isCatalyst: boolean = false,\n) {\n  const buildSettings = child_process.execFileSync(\n    'xcodebuild',\n    [\n      xcodeProject.isWorkspace ? '-workspace' : '-project',\n      xcodeProject.name,\n      '-scheme',\n      scheme,\n      '-sdk',\n      getPlatformName(buildOutput),\n      '-configuration',\n      configuration,\n      '-showBuildSettings',\n      '-json',\n    ],\n    {encoding: 'utf8'},\n  );\n  const {targetBuildDir, executableFolderPath} = getTargetPaths(buildSettings);\n\n  if (!targetBuildDir) {\n    throw new CLIError('Failed to get the target build directory.');\n  }\n\n  if (!executableFolderPath) {\n    throw new CLIError('Failed to get the app name.');\n  }\n\n  return `${targetBuildDir}${\n    isCatalyst ? '-maccatalyst' : ''\n  }/${executableFolderPath}`;\n}\n\nfunction getPlatformName(buildOutput: string) {\n  // Xcode can sometimes escape `=` with a backslash or put the value in quotes\n  const platformNameMatch = /export PLATFORM_NAME\\\\?=\"?(\\w+)\"?$/m.exec(\n    buildOutput,\n  );\n  if (!platformNameMatch) {\n    throw new CLIError(\n      'Couldn\\'t find \"PLATFORM_NAME\" variable in xcodebuild output. Please report this issue and run your project with Xcode instead.',\n    );\n  }\n  return platformNameMatch[1];\n}\n\nfunction xcbeautifyAvailable() {\n  try {\n    child_process.execSync('xcbeautify --version', {\n      stdio: [0, 'pipe', 'ignore'],\n    });\n  } catch (error) {\n    return false;\n  }\n  return true;\n}\n\nfunction xcprettyAvailable() {\n  try {\n    child_process.execSync('xcpretty --version', {\n      stdio: [0, 'pipe', 'ignore'],\n    });\n  } catch (error) {\n    return false;\n  }\n  return true;\n}\n\nfunction matchingDevice(\n  devices: Array<Device>,\n  deviceName: string | true | undefined,\n) {\n  if (deviceName === true) {\n    const firstIOSDevice = devices.find((d) => d.type === 'device')!;\n    if (firstIOSDevice) {\n      logger.info(\n        `Using first available device named \"${chalk.bold(\n          firstIOSDevice.name,\n        )}\" due to lack of name supplied.`,\n      );\n      return firstIOSDevice;\n    } else {\n      logger.error('No iOS devices connected.');\n      return undefined;\n    }\n  }\n  const deviceByName = devices.find(\n    (device) =>\n      device.name === deviceName || formattedDeviceName(device) === deviceName,\n  );\n  if (!deviceByName) {\n    logger.error(\n      `Could not find a device named: \"${chalk.bold(\n        String(deviceName),\n      )}\". ${printFoundDevices(devices)}`,\n    );\n  }\n  return deviceByName;\n}\n\nfunction formattedDeviceName(simulator: Device) {\n  return simulator.version\n    ? `${simulator.name} (${simulator.version})`\n    : simulator.name;\n}\n\nfunction printFoundDevices(devices: Array<Device>) {\n  return [\n    'Available devices:',\n    ...devices.map((device) => `  - ${device.name} (${device.udid})`),\n  ].join('\\n');\n}\n\nfunction getProcessOptions({\n  packager,\n  terminal,\n  port,\n}: {\n  packager: boolean;\n  terminal: string | undefined;\n  port: number;\n}): SpawnOptionsWithoutStdio {\n  if (packager) {\n    return {\n      env: {\n        ...process.env,\n        RCT_TERMINAL: terminal,\n        RCT_METRO_PORT: port.toString(),\n      },\n    };\n  }\n\n  return {\n    env: {\n      ...process.env,\n      RCT_TERMINAL: terminal,\n      RCT_NO_LAUNCH_PACKAGER: 'true',\n    },\n  };\n}\n\nexport default {\n  name: 'run-ios',\n  description: 'builds your app and starts it on iOS simulator',\n  func: runIOS,\n  examples: [\n    {\n      desc: 'Run on a different simulator, e.g. iPhone SE (2nd generation)',\n      cmd: 'react-native run-ios --simulator \"iPhone SE (2nd generation)\"',\n    },\n    {\n      desc: 'Pass a non-standard location of iOS directory',\n      cmd: 'react-native run-ios --project-path \"./app/ios\"',\n    },\n    {\n      desc: \"Run on a connected device, e.g. Max's iPhone\",\n      cmd: 'react-native run-ios --device \"Max\\'s iPhone\"',\n    },\n    {\n      desc: 'Run on the AppleTV simulator',\n      cmd:\n        'react-native run-ios --simulator \"Apple TV\"  --scheme \"helloworld-tvOS\"',\n    },\n  ],\n  options: [\n    {\n      name: '--simulator <string>',\n      description:\n        'Explicitly set simulator to use. Optionally include iOS version between ' +\n        'parenthesis at the end to match an exact version: \"iPhone 6 (10.0)\"',\n      default: 'iPhone 13',\n    },\n    {\n      name: '--configuration <string>',\n      description: 'Explicitly set the scheme configuration to use',\n      default: 'Debug',\n    },\n    {\n      name: '--scheme <string>',\n      description: 'Explicitly set Xcode scheme to use',\n    },\n    {\n      name: '--project-path <string>',\n      description:\n        'Path relative to project root where the Xcode project ' +\n        '(.xcodeproj) lives.',\n      default: 'ios',\n    },\n    {\n      name: '--device [string]',\n      description:\n        'Explicitly set device to use by name.  The value is not required if you have a single device connected.',\n    },\n    {\n      name: '--udid <string>',\n      description: 'Explicitly set device to use by udid',\n    },\n    {\n      name: '--no-packager',\n      description: 'Do not launch packager while building',\n    },\n    {\n      name: '--verbose',\n      description: 'Do not use xcbeautify or xcpretty even if installed',\n    },\n    {\n      name: '--port <number>',\n      default: process.env.RCT_METRO_PORT || 8081,\n      parse: Number,\n    },\n    {\n      name: '--terminal <string>',\n      description:\n        'Launches the Metro Bundler in a new window using the specified terminal path.',\n      default: getDefaultUserTerminal,\n    },\n  ],\n};\n"]}