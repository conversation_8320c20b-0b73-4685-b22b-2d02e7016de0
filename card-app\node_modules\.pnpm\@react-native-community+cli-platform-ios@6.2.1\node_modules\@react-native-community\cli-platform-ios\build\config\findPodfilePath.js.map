{"version": 3, "sources": ["../../src/config/findPodfilePath.ts"], "names": ["findPodfilePath", "folder", "projectFolder", "podFilePath", "path", "join", "fs", "existsSync", "podfiles", "glob", "sync", "cwd", "ignore", "length"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAMe,SAASA,eAAT,CAAyBC,MAAzB,EAAyCC,aAAzC,EAAgE;AAC7E,QAAMC,WAAW,GAAGC,gBAAKC,IAAL,CAAUH,aAAV,EAAyB,IAAzB,EAA+B,SAA/B,CAApB;;AACA,MAAII,cAAGC,UAAH,CAAcJ,WAAd,CAAJ,EAAgC;AAC9B,WAAOA,WAAP;AACD;;AAED,QAAMK,QAAQ,GAAGC,gBAAKC,IAAL,CAAU,YAAV,EAAwB;AACvCC,IAAAA,GAAG,EAAEV,MADkC;AAEvCW,IAAAA,MAAM,EAAE;AAF+B,GAAxB,CAAjB;;AAIA,MAAIJ,QAAQ,CAACK,MAAT,GAAkB,CAAtB,EAAyB;AACvB,WAAOT,gBAAKC,IAAL,CAAUJ,MAAV,EAAkBO,QAAQ,CAAC,CAAD,CAA1B,CAAP;AACD;;AAED,SAAO,IAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport glob from 'glob';\nimport path from 'path';\n\nexport default function findPodfilePath(folder: string, projectFolder: string) {\n  const podFilePath = path.join(projectFolder, '..', 'Podfile');\n  if (fs.existsSync(podFilePath)) {\n    return podFilePath;\n  }\n\n  const podfiles = glob.sync('**/Podfile', {\n    cwd: folder,\n    ignore: 'node_modules/**',\n  });\n  if (podfiles.length > 0) {\n    return path.join(folder, podfiles[0]);\n  }\n\n  return null;\n}\n"]}