{"version": 3, "sources": ["../../src/link/getTargets.ts"], "names": ["getTargets", "project", "firstProject", "targets", "getFirstProject", "nativeTargetSection", "pbxNativeTargetSection", "filter", "target", "value", "undefined", "map", "key", "configurationListId", "buildConfigurationList", "configurationList", "pbxXCConfigurationList", "buildConfigurationId", "buildConfigurations", "buildConfiguration", "pbxXCBuildConfigurationSection", "uuid", "name", "productReference_comment", "isTVOS", "buildSettings", "SDKROOT", "indexOf"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACe,SAASA,UAAT,CAAoBC,OAApB,EAAkC;AAC/C,QAAM;AACJC,IAAAA,YAAY,EAAE;AAACC,MAAAA;AAAD;AADV,MAEFF,OAAO,CAACG,eAAR,EAFJ;AAGA,QAAMC,mBAAmB,GAAGJ,OAAO,CAACK,sBAAR,EAA5B;AACA,SAAOH,OAAO,CACXI,MADI,CACIC,MAAD,IAAoBH,mBAAmB,CAACG,MAAM,CAACC,KAAR,CAAnB,KAAsCC,SAD7D,EAEJC,GAFI,CAECH,MAAD,IAAoB;AACvB,UAAMI,GAAG,GAAGJ,MAAM,CAACC,KAAnB;AACA,UAAMI,mBAAmB,GACvBR,mBAAmB,CAACO,GAAD,CAAnB,CAAyBE,sBAD3B;AAEA,UAAMC,iBAAiB,GAAGd,OAAO,CAACe,sBAAR,GACxBH,mBADwB,CAA1B;AAGA,UAAMI,oBAAoB,GACxBF,iBAAiB,CAACG,mBAAlB,CAAsC,CAAtC,EAAyCT,KAD3C;AAEA,UAAMU,kBAAkB,GAAGlB,OAAO,CAACmB,8BAAR,GACzBH,oBADyB,CAA3B;AAGA,WAAO;AACLI,MAAAA,IAAI,EAAET,GADD;AAELJ,MAAAA,MAAM,EAAEH,mBAAmB,CAACO,GAAD,CAFtB;AAGLU,MAAAA,IAAI,EAAEjB,mBAAmB,CAACO,GAAD,CAAnB,CAAyBW,wBAH1B;AAILC,MAAAA,MAAM,EACHL,kBAAkB,CAACM,aAAnB,CAAiCC,OAAjC,IACCP,kBAAkB,CAACM,aAAnB,CAAiCC,OAAjC,CAAyCC,OAAzC,CAAiD,SAAjD,MACE,CAAC,CAFL,IAGA;AARG,KAAP;AAUD,GAxBI,CAAP;AAyBD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\ninterface Target {\n  value: string;\n}\n\n/**\n * Given xcodeproj it returns list of targets\n */\nexport default function getTargets(project: any) {\n  const {\n    firstProject: {targets},\n  } = project.getFirstProject();\n  const nativeTargetSection = project.pbxNativeTargetSection();\n  return targets\n    .filter((target: Target) => nativeTargetSection[target.value] !== undefined)\n    .map((target: Target) => {\n      const key = target.value;\n      const configurationListId =\n        nativeTargetSection[key].buildConfigurationList;\n      const configurationList = project.pbxXCConfigurationList()[\n        configurationListId\n      ];\n      const buildConfigurationId =\n        configurationList.buildConfigurations[0].value;\n      const buildConfiguration = project.pbxXCBuildConfigurationSection()[\n        buildConfigurationId\n      ];\n      return {\n        uuid: key,\n        target: nativeTargetSection[key],\n        name: nativeTargetSection[key].productReference_comment,\n        isTVOS:\n          (buildConfiguration.buildSettings.SDKROOT &&\n            buildConfiguration.buildSettings.SDKROOT.indexOf('appletv') !==\n              -1) ||\n          false,\n      };\n    });\n}\n"]}