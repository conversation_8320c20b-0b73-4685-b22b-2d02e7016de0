{"version": 3, "sources": ["../../src/link-pods/savePodFile.ts"], "names": ["savePodFile", "podfilePath", "podLines", "newPodfile", "join", "logger", "debug", "fs", "writeFileSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,WAAT,CACbC,WADa,EAEbC,QAFa,EAGb;AACA,QAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAT,CAAc,IAAd,CAAnB;;AACAC,qBAAOC,KAAP,CAAc,sBAAqBL,WAAY,EAA/C;;AACAM,gBAAGC,aAAH,CAAiBP,WAAjB,EAA8BE,UAA9B;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport default function savePodFile(\n  podfilePath: string,\n  podLines: Array<string>,\n) {\n  const newPodfile = podLines.join('\\n');\n  logger.debug(`Writing changes to ${podfilePath}`);\n  fs.writeFileSync(podfilePath, newPodfile);\n}\n"]}