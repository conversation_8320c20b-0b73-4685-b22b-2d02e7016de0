load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "core",
    srcs = glob(
        [
            "*.java",
        ],
        exclude = ["CallInvokerHolderImpl.java"],
    ),
    autoglob = False,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("java/com/facebook/proguard/annotations:annotations"),
        react_native_dep("java/com/facebook/systrace:systrace"),
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/reactperflogger:reactperflogger"),
        react_native_target("java/com/facebook/react/turbomodule/core/jni:jni"),
        react_native_target("java/com/facebook/debug/holder:holder"),
        react_native_target("java/com/facebook/react/bridge:interfaces"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/config:config"),
        react_native_target("java/com/facebook/react/module/model:model"),
        ":callinvokerholder",
    ],
    exported_deps = [
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
    ],
)

rn_android_library(
    name = "callinvokerholder",
    srcs = ["CallInvokerHolderImpl.java"],
    autoglob = False,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
        react_native_dep("libraries/fbjni:java"),
    ],
)
