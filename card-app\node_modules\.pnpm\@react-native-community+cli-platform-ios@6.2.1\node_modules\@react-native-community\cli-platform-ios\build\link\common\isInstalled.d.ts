/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { IOSProjectConfig, IOSDependencyConfig } from '@react-native-community/cli-types';
export default function isInstalled(projectConfig: IOSProjectConfig, _name: string | undefined, dependencyConfig: IOSDependencyConfig): boolean;
//# sourceMappingURL=isInstalled.d.ts.map