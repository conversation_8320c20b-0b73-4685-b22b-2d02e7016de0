#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/tslint@6.1.3_typescript@5.9.2/node_modules/tslint/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/tslint@6.1.3_typescript@5.9.2/node_modules/tslint/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/tslint@6.1.3_typescript@5.9.2/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/tslint@6.1.3_typescript@5.9.2/node_modules/tslint/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/tslint@6.1.3_typescript@5.9.2/node_modules/tslint/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/tslint@6.1.3_typescript@5.9.2/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/tslint" "$@"
else
  exec node  "$basedir/../../bin/tslint" "$@"
fi
