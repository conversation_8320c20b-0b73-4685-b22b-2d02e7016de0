/*
 * @Author: 高超
 * @Date: 2021-11-06 16:56:58
 * @LastEditTime: 2021-12-21 21:49:43
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/babel.config.js
 * love jiajia
 */
module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      require.resolve('babel-plugin-module-resolver'),
      {
        alias: {
          '@pages': './src/pages',
          '@utils': './src/utils',
          '@static': './src/static',
          '@components': './src/components',
        },
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
      },
    ],
    ["import", { "libraryName": "antd-mobile-rn" }]
  ]
};
