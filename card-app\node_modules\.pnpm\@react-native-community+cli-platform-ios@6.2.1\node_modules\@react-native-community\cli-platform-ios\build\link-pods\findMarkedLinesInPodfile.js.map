{"version": 3, "sources": ["../../src/link-pods/findMarkedLinesInPodfile.ts"], "names": ["MARKER_TEXT", "findMarkedLinesInPodfile", "podLines", "result", "i", "len", "length", "includes", "push", "line", "indentation", "indexOf"], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEO,MAAMA,WAAW,GAAG,gCAApB;;;AAEQ,SAASC,wBAAT,CAAkCC,QAAlC,EAA2D;AACxE,QAAMC,MAAM,GAAG,EAAf;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGH,QAAQ,CAACI,MAA/B,EAAuCF,CAAC,GAAGC,GAA3C,EAAgDD,CAAC,EAAjD,EAAqD;AACnD,QAAIF,QAAQ,CAACE,CAAD,CAAR,CAAYG,QAAZ,CAAqBP,WAArB,CAAJ,EAAuC;AACrCG,MAAAA,MAAM,CAACK,IAAP,CAAY;AAACC,QAAAA,IAAI,EAAEL,CAAC,GAAG,CAAX;AAAcM,QAAAA,WAAW,EAAER,QAAQ,CAACE,CAAD,CAAR,CAAYO,OAAZ,CAAoB,GAApB;AAA3B,OAAZ;AACD;AACF;;AACD,SAAOR,MAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport const MARKER_TEXT = '# Add new pods below this line';\n\nexport default function findMarkedLinesInPodfile(podLines: Array<string>) {\n  const result = [];\n  for (let i = 0, len = podLines.length; i < len; i++) {\n    if (podLines[i].includes(MARKER_TEXT)) {\n      result.push({line: i + 1, indentation: podLines[i].indexOf('#')});\n    }\n  }\n  return result;\n}\n"]}