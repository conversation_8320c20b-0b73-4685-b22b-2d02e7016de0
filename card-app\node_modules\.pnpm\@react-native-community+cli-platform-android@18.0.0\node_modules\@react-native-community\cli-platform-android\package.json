{"name": "@react-native-community/cli-platform-android", "version": "18.0.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-config-android": "18.0.0", "@react-native-community/cli-tools": "18.0.0", "chalk": "^4.1.2", "execa": "^5.0.0", "logkitty": "^0.7.1"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "18.0.0"}, "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-platform-android", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/platform-android"}, "gitHead": "f50c1f19a8068787d074560375b726d89c30a088"}