{"version": 3, "sources": ["../../src/link/hasLibraryImported.ts"], "names": ["hasLibraryImported", "libraries", "packageName", "children", "filter", "library", "comment", "length"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACe,SAASA,kBAAT,CACbC,SADa,EAEbC,WAFa,EAGb;AACA,SACED,SAAS,CAACE,QAAV,CAAmBC,MAAnB,CAA2BC,OAAD,IAAaA,OAAO,CAACC,OAAR,KAAoBJ,WAA3D,EACGK,MADH,GACY,CAFd;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Given an array of libraries already imported and packageName that will be\n * added, returns true or false depending on whether the library is already linked\n * or not\n */\nexport default function hasLibraryImported(\n  libraries: {children: Array<{comment: string}>},\n  packageName: string,\n) {\n  return (\n    libraries.children.filter((library) => library.comment === packageName)\n      .length > 0\n  );\n}\n"]}