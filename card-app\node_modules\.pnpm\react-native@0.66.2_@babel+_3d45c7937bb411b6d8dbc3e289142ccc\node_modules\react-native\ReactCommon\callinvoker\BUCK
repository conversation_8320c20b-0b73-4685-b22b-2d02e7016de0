load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "rn_xplat_cxx_library", "subdir_glob")

rn_xplat_cxx_library(
    name = "callinvoker",
    srcs = glob(["**/*.cpp"]),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    platforms = (ANDROID, APPLE),
    preferred_linkage = "static",
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
)
