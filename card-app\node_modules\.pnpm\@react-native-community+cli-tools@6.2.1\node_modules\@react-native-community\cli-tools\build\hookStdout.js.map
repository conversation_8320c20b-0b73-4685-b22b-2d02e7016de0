{"version": 3, "sources": ["../src/hookStdout.ts"], "names": ["hookStdout", "callback", "old_write", "process", "stdout", "write", "str", "apply", "arguments"], "mappings": ";;;;;;;AAAA;AAEA,SAASA,UAAT,CAAoBC,QAApB,EAAwC;AACtC,MAAIC,SAAS,GAAGC,OAAO,CAACC,MAAR,CAAeC,KAA/B,CADsC,CAGtC;;AACAF,EAAAA,OAAO,CAACC,MAAR,CAAeC,KAAf,GAAuB,CAAEA,KAAD,IACtB,UAAUC,GAAV,EAAuB;AACrBD,IAAAA,KAAK,CAACE,KAAN,CAAYJ,OAAO,CAACC,MAApB,EAA4BI,SAA5B;AACAP,IAAAA,QAAQ,CAACK,GAAD,CAAR;AACD,GAJoB,EAIlBH,OAAO,CAACC,MAAR,CAAeC,KAJG,CAAvB;;AAMA,SAAO,MAAM;AACXF,IAAAA,OAAO,CAACC,MAAR,CAAeC,KAAf,GAAuBH,SAAvB;AACD,GAFD;AAGD;;eAEcF,U", "sourcesContent": ["// https://gist.github.com/pguillory/729616\n\nfunction hookStdout(callback: Function) {\n  let old_write = process.stdout.write;\n\n  // @ts-ignore\n  process.stdout.write = ((write: any) =>\n    function (str: string) {\n      write.apply(process.stdout, arguments);\n      callback(str);\n    })(process.stdout.write);\n\n  return () => {\n    process.stdout.write = old_write;\n  };\n}\n\nexport default hookStdout;\n"]}