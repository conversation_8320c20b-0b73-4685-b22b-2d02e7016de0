{"version": 3, "sources": ["../../../src/commands/runIOS/findXcodeProject.ts"], "names": ["findXcodeProject", "files", "sortedFiles", "sort", "i", "length", "fileName", "ext", "path", "extname", "name", "isWorkspace"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AASA,SAASA,gBAAT,CAA0BC,KAA1B,EAAoE;AAClE,QAAMC,WAAW,GAAGD,KAAK,CAACE,IAAN,EAApB;;AACA,OAAK,IAAIC,CAAC,GAAGF,WAAW,CAACG,MAAZ,GAAqB,CAAlC,EAAqCD,CAAC,IAAI,CAA1C,EAA6CA,CAAC,EAA9C,EAAkD;AAChD,UAAME,QAAQ,GAAGL,KAAK,CAACG,CAAD,CAAtB;;AACA,UAAMG,GAAG,GAAGC,gBAAKC,OAAL,CAAaH,QAAb,CAAZ;;AAEA,QAAIC,GAAG,KAAK,cAAZ,EAA4B;AAC1B,aAAO;AACLG,QAAAA,IAAI,EAAEJ,QADD;AAELK,QAAAA,WAAW,EAAE;AAFR,OAAP;AAID;;AACD,QAAIJ,GAAG,KAAK,YAAZ,EAA0B;AACxB,aAAO;AACLG,QAAAA,IAAI,EAAEJ,QADD;AAELK,QAAAA,WAAW,EAAE;AAFR,OAAP;AAID;AACF;;AAED,SAAO,IAAP;AACD;;eAEcX,gB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\n\nexport type ProjectInfo = {\n  name: string;\n  isWorkspace: boolean;\n};\n\nfunction findXcodeProject(files: Array<string>): ProjectInfo | null {\n  const sortedFiles = files.sort();\n  for (let i = sortedFiles.length - 1; i >= 0; i--) {\n    const fileName = files[i];\n    const ext = path.extname(fileName);\n\n    if (ext === '.xcworkspace') {\n      return {\n        name: fileName,\n        isWorkspace: true,\n      };\n    }\n    if (ext === '.xcodeproj') {\n      return {\n        name: fileName,\n        isWorkspace: false,\n      };\n    }\n  }\n\n  return null;\n}\n\nexport default findXcodeProject;\n"]}