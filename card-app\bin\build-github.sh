#!/bin/bash
###
 # @Author: GitHub Actions Build Script
 # @Date: 2024-01-01
 # @Description: Build script adapted for GitHub Actions environment
 # Based on original build.sh by 高超
### 

set -e  # Exit on any error

echo "🚀 Starting GitHub Actions build process..."

# Get the current directory (should be card-app root)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 Project root: $PROJECT_ROOT"
echo "📁 Script directory: $SCRIPT_DIR"

# Navigate to android directory
cd "$PROJECT_ROOT/android"

echo "🧹 Cleaning previous builds..."
./gradlew clean --warning-mode all

echo "🔨 Building Release APK..."
./gradlew assembleRelease --stacktrace --warning-mode all

echo "✅ Build completed successfully!"
echo "📦 APK location: $PROJECT_ROOT/android/app/build/outputs/apk/release/"

# List the generated APK files
echo "📋 Generated APK files:"
ls -la "$PROJECT_ROOT/android/app/build/outputs/apk/release/" || echo "APK directory not found"
