/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
export declare function getMainActivityFiles(folder: string, includePackage?: boolean): string[];
export default function getPackageClassName(folder: string): string | null;
export declare function matchClassName(file: string): RegExpMatchArray | null;
//# sourceMappingURL=findPackageClassName.d.ts.map