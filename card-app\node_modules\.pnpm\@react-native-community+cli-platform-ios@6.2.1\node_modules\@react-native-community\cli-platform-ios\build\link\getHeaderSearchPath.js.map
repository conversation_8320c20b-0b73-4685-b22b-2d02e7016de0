{"version": 3, "sources": ["../../src/link/getHeaderSearchPath.ts"], "names": ["getOuterDirectory", "directories", "reduce", "topDir", "currentDir", "currentFolders", "split", "path", "sep", "topMostFolders", "length", "slice", "join", "getHeaderSearchPath", "sourceDir", "headers", "map", "dirname", "relative"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,WAAD,IACxBA,WAAW,CAACC,MAAZ,CAAmB,CAACC,MAAD,EAASC,UAAT,KAAwB;AACzC,QAAMC,cAAc,GAAGD,UAAU,CAACE,KAAX,CAAiBC,cAAKC,GAAtB,CAAvB;AACA,QAAMC,cAAc,GAAGN,MAAM,CAACG,KAAP,CAAaC,cAAKC,GAAlB,CAAvB;;AAEA,MACEH,cAAc,CAACK,MAAf,KAA0BD,cAAc,CAACC,MAAzC,IACA,oBAAKL,cAAL,MAAyB,oBAAKI,cAAL,CAF3B,EAGE;AACA,WAAOJ,cAAc,CAACM,KAAf,CAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4BC,IAA5B,CAAiCL,cAAKC,GAAtC,CAAP;AACD;;AAED,SAAOH,cAAc,CAACK,MAAf,GAAwBD,cAAc,CAACC,MAAvC,GAAgDN,UAAhD,GAA6DD,MAApE;AACD,CAZD,CADF;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACe,SAASU,mBAAT,CACbC,SADa,EAEbC,OAFa,EAGb;AACA,QAAMd,WAAW,GAAG,qBAAMc,OAAO,CAACC,GAAR,CAAYT,cAAKU,OAAjB,CAAN,CAApB;AAEA,SAAOhB,WAAW,CAACS,MAAZ,KAAuB,CAAvB,GACF,eAAcH,cAAKW,QAAL,CAAcJ,SAAd,EAAyBb,WAAW,CAAC,CAAD,CAApC,CAAyC,GADrD,GAEF,eAAcM,cAAKW,QAAL,CACbJ,SADa,EAEbd,iBAAiB,CAACC,WAAD,CAFJ,CAGb,MALN;AAMD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {posix as path} from 'path';\nimport {last, union} from 'lodash';\n\n/**\n * Given an array of directories, it returns the one that contains\n * all the other directories in a given array inside it.\n *\n * Example:\n * Given an array of directories: ['/Users/<USER>/a', '/Users/<USER>/b']\n * the returned folder is `/Users/<USER>\n *\n * Check `getHeaderSearchPath.spec.js` for more use-cases.\n */\nconst getOuterDirectory = (directories: Array<string>) =>\n  directories.reduce((topDir, currentDir) => {\n    const currentFolders = currentDir.split(path.sep);\n    const topMostFolders = topDir.split(path.sep);\n\n    if (\n      currentFolders.length === topMostFolders.length &&\n      last(currentFolders) !== last(topMostFolders)\n    ) {\n      return currentFolders.slice(0, -1).join(path.sep);\n    }\n\n    return currentFolders.length < topMostFolders.length ? currentDir : topDir;\n  });\n\n/**\n * Given an array of headers it returns search path so Xcode can resolve\n * headers when referenced like below:\n * ```\n * #import \"CodePush.h\"\n * ```\n * If all files are located in one directory (directories.length === 1),\n * we simply return a relative path to that location.\n *\n * Otherwise, we loop through them all to find the outer one that contains\n * all the headers inside. That location is then returned with /** appended at\n * the end so Xcode marks that location as `recursive` and will look inside\n * every folder of it to locate correct headers.\n */\nexport default function getHeaderSearchPath(\n  sourceDir: string,\n  headers: Array<string>,\n) {\n  const directories = union(headers.map(path.dirname));\n\n  return directories.length === 1\n    ? `\"$(SRCROOT)/${path.relative(sourceDir, directories[0])}\"`\n    : `\"$(SRCROOT)/${path.relative(\n        sourceDir,\n        getOuterDirectory(directories),\n      )}/**\"`;\n}\n"]}