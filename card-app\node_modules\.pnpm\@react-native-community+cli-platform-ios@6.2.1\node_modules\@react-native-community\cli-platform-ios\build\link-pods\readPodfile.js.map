{"version": 3, "sources": ["../../src/link-pods/readPodfile.ts"], "names": ["readPodfile", "podfilePath", "logger", "debug", "pod<PERSON>ontent", "fs", "readFileSync", "split"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,WAAT,CAAqBC,WAArB,EAA0C;AACvDC,qBAAOC,KAAP,CAAc,WAAUF,WAAY,EAApC;;AACA,QAAMG,UAAU,GAAGC,cAAGC,YAAH,CAAgBL,WAAhB,EAA6B,MAA7B,CAAnB;;AACA,SAAOG,UAAU,CAACG,KAAX,CAAiB,QAAjB,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport default function readPodfile(podfilePath: string) {\n  logger.debug(`Reading ${podfilePath}`);\n  const podContent = fs.readFileSync(podfilePath, 'utf8');\n  return podContent.split(/\\r?\\n/g);\n}\n"]}