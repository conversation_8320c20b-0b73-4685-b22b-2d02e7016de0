{"version": 3, "sources": ["../src/errors.ts"], "names": ["CLIError", "Error", "constructor", "msg", "originalError", "inlineString", "stack", "split", "slice", "join", "str", "replace", "trim"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACO,MAAMA,QAAN,SAAuBC,KAAvB,CAA6B;AAClCC,EAAAA,WAAW,CAACC,GAAD,EAAcC,aAAd,EAA8C;AACvD,UAAMC,YAAY,CAACF,GAAD,CAAlB;;AACA,QAAIC,aAAJ,EAAmB;AACjB,WAAKE,KAAL,GACE,OAAOF,aAAP,KAAyB,QAAzB,GACIA,aADJ,GAEIA,aAAa,CAACE,KAAd,IAAuB,GAAGC,KAAH,CAAS,IAAT,EAAeC,KAAf,CAAqB,CAArB,EAAwB,CAAxB,EAA2BC,IAA3B,CAAgC,IAAhC,CAH7B;AAID,KALD,MAKO;AACL;AACA;AACA;AACA,aAAO,KAAKH,KAAZ;AACD;AACF;;AAdiC;;;;AAiB7B,MAAMD,YAAY,GAAIK,GAAD,IAC1BA,GAAG,CAACC,OAAJ,CAAY,YAAZ,EAA0B,GAA1B,EAA+BC,IAA/B,EADK", "sourcesContent": ["/**\n * A custom Error that creates a single-lined message to match current styling inside CLI.\n * Uses original stack trace when `originalError` is passed or erase the stack if it's not defined.\n */\nexport class CLIError extends Error {\n  constructor(msg: string, originalError?: Error | string) {\n    super(inlineString(msg));\n    if (originalError) {\n      this.stack =\n        typeof originalError === 'string'\n          ? originalError\n          : originalError.stack || ''.split('\\n').slice(0, 2).join('\\n');\n    } else {\n      // When the \"originalError\" is not passed, it means that we know exactly\n      // what went wrong and provide means to fix it. In such cases showing the\n      // stack is an unnecessary clutter to the CLI output, hence removing it.\n      delete this.stack;\n    }\n  }\n}\n\nexport const inlineString = (str: string) =>\n  str.replace(/(\\s{2,})/gm, ' ').trim();\n"]}