#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules/fast-xml-parser/src/cli/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules/fast-xml-parser/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules/fast-xml-parser/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules/fast-xml-parser/src/cli/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules/fast-xml-parser/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules/fast-xml-parser/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/fast-xml-parser@4.5.3/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../src/cli/cli.js" "$@"
else
  exec node  "$basedir/../../src/cli/cli.js" "$@"
fi
