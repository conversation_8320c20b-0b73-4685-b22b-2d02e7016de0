{"version": 3, "sources": ["../src/launchDefaultBrowser.ts"], "names": ["launchDefaultBrowser", "url", "err", "logger", "error"], "mappings": ";;;;;;;AASA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA,eAAeA,oBAAf,CAAoCC,GAApC,EAAiD;AAC/C,MAAI;AACF,UAAM,qBAAKA,GAAL,CAAN;AACD,GAFD,CAEE,OAAOC,GAAP,EAAY;AACZ,QAAIA,GAAJ,EAAS;AACPC,sBAAOC,KAAP,CAAa,4BAAb,EAA2CF,GAA3C;AACD;AACF;AACF;;eAEcF,oB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport open from 'open';\nimport logger from './logger';\n\nasync function launchDefaultBrowser(url: string) {\n  try {\n    await open(url);\n  } catch (err) {\n    if (err) {\n      logger.error('<PERSON><PERSON><PERSON> exited with error:', err);\n    }\n  }\n}\n\nexport default launchDefaultBrowser;\n"]}