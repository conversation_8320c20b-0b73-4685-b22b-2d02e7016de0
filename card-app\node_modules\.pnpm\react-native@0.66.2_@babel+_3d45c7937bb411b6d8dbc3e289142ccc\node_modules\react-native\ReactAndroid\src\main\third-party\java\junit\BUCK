load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "junit",
    autoglob = False,
    visibility = ["//ReactAndroid/..."],
    exported_deps = [
        ":hamcrest",
        ":junit-core",
    ],
)

rn_prebuilt_jar(
    name = "junit-core",
    binary_jar = ":download-junit.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-junit.jar",
    sha1 = "2973d150c0dc1fefe998f834810d68f278ea58ec",
    url = "mvn:junit:junit:jar:4.12",
)

rn_prebuilt_jar(
    name = "hamcrest",
    binary_jar = ":download-hamcrest.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-hamcrest.jar",
    sha1 = "63a21ebc981131004ad02e0434e799fd7f3a8d5a",
    url = "mvn:org.hamcrest:hamcrest-all:jar:1.3",
)
