#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro-inspector-proxy@0.66.2/node_modules/metro-inspector-proxy/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro-inspector-proxy@0.66.2/node_modules/metro-inspector-proxy/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro-inspector-proxy@0.66.2/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro-inspector-proxy@0.66.2/node_modules/metro-inspector-proxy/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro-inspector-proxy@0.66.2/node_modules/metro-inspector-proxy/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro-inspector-proxy@0.66.2/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../src/cli.js" "$@"
else
  exec node  "$basedir/../../src/cli.js" "$@"
fi
