load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "kotlin-stdlib",
    visibility = ["PUBLIC"],
    exported_deps = [
        ":jetbrains-annotations",
        ":kotlin-stdlib-binary",
        ":kotlin-stdlib-common",
    ],
)

rn_android_library(
    name = "kotlin-stdlib-jdk7",
    visibility = ["PUBLIC"],
    exported_deps = [
        ":kotlin-stdlib",
        ":kotlin-stdlib-jdk7-binary",
    ],
)

rn_android_library(
    name = "kotlin-stdlib-jdk8",
    visibility = ["PUBLIC"],
    exported_deps = [
        ":kotlin-stdlib",
        ":kotlin-stdlib-jdk7",
        ":kotlin-stdlib-jdk8-binary",
    ],
)

rn_prebuilt_jar(
    name = "jetbrains-annotations",
    binary_jar = ":jetbrains-annotations.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "jetbrains-annotations.jar",
    sha1 = "919f0dfe192fb4e063e7dacadee7f8bb9a2672a9",
    url = "mvn:org.jetbrains:annotations:jar:13.0",
)

rn_prebuilt_jar(
    name = "kotlin-stdlib-binary",
    binary_jar = ":kotlin-stdlib-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "kotlin-stdlib-binary.jar",
    sha1 = "ea29e063d2bbe695be13e9d044dcfb0c7add398e",
    url = "mvn:org.jetbrains.kotlin:kotlin-stdlib:jar:1.4.10",
)

rn_prebuilt_jar(
    name = "kotlin-stdlib-common",
    binary_jar = ":kotlin-stdlib-common.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "kotlin-stdlib-common.jar",
    sha1 = "6229be3465805c99db1142ad75e6c6ddeac0b04c",
    url = "mvn:org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.4.10",
)

rn_prebuilt_jar(
    name = "kotlin-stdlib-jdk7-binary",
    binary_jar = ":kotlin-stdlib-jdk7-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "kotlin-stdlib-jdk7-binary.jar",
    sha1 = "30e46450b0bb3dbf43898d2f461be4a942784780",
    url = "mvn:org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.4.10",
)

rn_prebuilt_jar(
    name = "kotlin-stdlib-jdk8-binary",
    binary_jar = ":kotlin-stdlib-jdk8-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "kotlin-stdlib-jdk8-binary.jar",
    sha1 = "998caa30623f73223194a8b657abd2baec4880ea",
    url = "mvn:org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.4.10",
)
