// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0314E39D1B661A460092D183 /* CameraFocusSquare.m in Sources */ = {isa = PBXBuildFile; fileRef = 0314E39C1B661A460092D183 /* CameraFocusSquare.m */; };
		1354413B2487FF390024C19F /* BarcodeDetectorManagerMlkit.m in Sources */ = {isa = PBXBuildFile; fileRef = 1354413A2487FF390024C19F /* BarcodeDetectorManagerMlkit.m */; };
		2647D6712256BBD5007D2F91 /* FaceDetectorManagerMlkit.m in Sources */ = {isa = PBXBuildFile; fileRef = 2647D6702256BBD5007D2F91 /* FaceDetectorManagerMlkit.m */; };
		2647D6742256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.m in Sources */ = {isa = PBXBuildFile; fileRef = 2647D6722256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.m */; };
		4107014D1ACB732B00C6AA39 /* RCTCamera.m in Sources */ = {isa = PBXBuildFile; fileRef = 410701481ACB732B00C6AA39 /* RCTCamera.m */; };
		4107014E1ACB732B00C6AA39 /* RCTCameraManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 4107014A1ACB732B00C6AA39 /* RCTCameraManager.m */; };
		454EBCF41B5082DC00AD0F86 /* NSMutableDictionary+ImageMetadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 454EBCF31B5082DC00AD0F86 /* NSMutableDictionary+ImageMetadata.m */; };
		7162BE672013EAA100FE51FF /* RNCamera.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C7FFCC2013C7BF006EB75A /* RNCamera.m */; };
		7162BE682013EAA400FE51FF /* RNCameraManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C7FFC92013C7AE006EB75A /* RNCameraManager.m */; };
		71C7FFD02013C7E5006EB75A /* RNCameraUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C7FFCF2013C7E5006EB75A /* RNCameraUtils.m */; };
		71C7FFD32013C817006EB75A /* RNImageUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C7FFD22013C817006EB75A /* RNImageUtils.m */; };
		71C7FFD62013C824006EB75A /* RNFileSystem.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C7FFD52013C824006EB75A /* RNFileSystem.m */; };
		9FE592B31CA3CBF500788287 /* RCTSensorOrientationChecker.m in Sources */ = {isa = PBXBuildFile; fileRef = 9FE592B21CA3CBF500788287 /* RCTSensorOrientationChecker.m */; };
		A7D02BC9243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = A7D02BC8243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.m */; };
		F8393BEC21469C0000AB1995 /* RNSensorOrientationChecker.m in Sources */ = {isa = PBXBuildFile; fileRef = F8393BEB21469C0000AB1995 /* RNSensorOrientationChecker.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		4107012D1ACB723B00C6AA39 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0314E39B1B661A0C0092D183 /* CameraFocusSquare.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CameraFocusSquare.h; sourceTree = "<group>"; };
		0314E39C1B661A460092D183 /* CameraFocusSquare.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CameraFocusSquare.m; sourceTree = "<group>"; };
		135441392487FF390024C19F /* BarcodeDetectorManagerMlkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BarcodeDetectorManagerMlkit.h; sourceTree = "<group>"; };
		1354413A2487FF390024C19F /* BarcodeDetectorManagerMlkit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BarcodeDetectorManagerMlkit.m; sourceTree = "<group>"; };
		2647D66F2256BBD5007D2F91 /* FaceDetectorManagerMlkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FaceDetectorManagerMlkit.h; sourceTree = "<group>"; };
		2647D6702256BBD5007D2F91 /* FaceDetectorManagerMlkit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FaceDetectorManagerMlkit.m; sourceTree = "<group>"; };
		2647D6722256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RNFaceDetectorModuleMLKit.m; sourceTree = "<group>"; };
		2647D6732256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RNFaceDetectorModuleMLKit.h; sourceTree = "<group>"; };
		4107012F1ACB723B00C6AA39 /* libRNCamera.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNCamera.a; sourceTree = BUILT_PRODUCTS_DIR; };
		410701471ACB732B00C6AA39 /* RCTCamera.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTCamera.h; sourceTree = "<group>"; };
		410701481ACB732B00C6AA39 /* RCTCamera.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTCamera.m; sourceTree = "<group>"; };
		410701491ACB732B00C6AA39 /* RCTCameraManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTCameraManager.h; sourceTree = "<group>"; };
		4107014A1ACB732B00C6AA39 /* RCTCameraManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTCameraManager.m; sourceTree = "<group>"; };
		454EBCF31B5082DC00AD0F86 /* NSMutableDictionary+ImageMetadata.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSMutableDictionary+ImageMetadata.m"; sourceTree = "<group>"; };
		71C7FFC82013C7AE006EB75A /* RNCameraManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNCameraManager.h; sourceTree = "<group>"; };
		71C7FFC92013C7AE006EB75A /* RNCameraManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNCameraManager.m; sourceTree = "<group>"; };
		71C7FFCB2013C7BF006EB75A /* RNCamera.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNCamera.h; sourceTree = "<group>"; };
		71C7FFCC2013C7BF006EB75A /* RNCamera.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNCamera.m; sourceTree = "<group>"; };
		71C7FFCE2013C7E5006EB75A /* RNCameraUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNCameraUtils.h; sourceTree = "<group>"; };
		71C7FFCF2013C7E5006EB75A /* RNCameraUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNCameraUtils.m; sourceTree = "<group>"; };
		71C7FFD12013C817006EB75A /* RNImageUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNImageUtils.h; sourceTree = "<group>"; };
		71C7FFD22013C817006EB75A /* RNImageUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNImageUtils.m; sourceTree = "<group>"; };
		71C7FFD42013C824006EB75A /* RNFileSystem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNFileSystem.h; sourceTree = "<group>"; };
		71C7FFD52013C824006EB75A /* RNFileSystem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNFileSystem.m; sourceTree = "<group>"; };
		9FE592B11CA3CBF500788287 /* RCTSensorOrientationChecker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTSensorOrientationChecker.h; sourceTree = "<group>"; };
		9FE592B21CA3CBF500788287 /* RCTSensorOrientationChecker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTSensorOrientationChecker.m; sourceTree = "<group>"; };
		A7D02BC7243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNCustomWhiteBalanceSettings.h; sourceTree = "<group>"; };
		C5627D22245D10A100B761CF /* NSMutableDictionary+ImageMetadata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSMutableDictionary+ImageMetadata.h"; sourceTree = "<group>"; };
		A7D02BC8243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNCustomWhiteBalanceSettings.m; sourceTree = "<group>"; };
		F8393BEA21469C0000AB1995 /* RNSensorOrientationChecker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RNSensorOrientationChecker.h; sourceTree = "<group>"; };
		F8393BEB21469C0000AB1995 /* RNSensorOrientationChecker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RNSensorOrientationChecker.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4107012C1ACB723B00C6AA39 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		410701241ACB719800C6AA39 = {
			isa = PBXGroup;
			children = (
				714166162013E1B600EE9FCC /* RN */,
				714166152013E19D00EE9FCC /* RCT */,
				410701301ACB723B00C6AA39 /* Products */,
			);
			sourceTree = "<group>";
		};
		410701301ACB723B00C6AA39 /* Products */ = {
			isa = PBXGroup;
			children = (
				4107012F1ACB723B00C6AA39 /* libRNCamera.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		714166152013E19D00EE9FCC /* RCT */ = {
			isa = PBXGroup;
			children = (
				9FE592B11CA3CBF500788287 /* RCTSensorOrientationChecker.h */,
				9FE592B21CA3CBF500788287 /* RCTSensorOrientationChecker.m */,
				0314E39C1B661A460092D183 /* CameraFocusSquare.m */,
				0314E39B1B661A0C0092D183 /* CameraFocusSquare.h */,
				C5627D22245D10A100B761CF /* NSMutableDictionary+ImageMetadata.h */,
				454EBCF31B5082DC00AD0F86 /* NSMutableDictionary+ImageMetadata.m */,
				410701471ACB732B00C6AA39 /* RCTCamera.h */,
				410701481ACB732B00C6AA39 /* RCTCamera.m */,
				410701491ACB732B00C6AA39 /* RCTCameraManager.h */,
				4107014A1ACB732B00C6AA39 /* RCTCameraManager.m */,
			);
			path = RCT;
			sourceTree = "<group>";
		};
		714166162013E1B600EE9FCC /* RN */ = {
			isa = PBXGroup;
			children = (
				135441392487FF390024C19F /* BarcodeDetectorManagerMlkit.h */,
				1354413A2487FF390024C19F /* BarcodeDetectorManagerMlkit.m */,
				F8393BEA21469C0000AB1995 /* RNSensorOrientationChecker.h */,
				F8393BEB21469C0000AB1995 /* RNSensorOrientationChecker.m */,
				2647D66F2256BBD5007D2F91 /* FaceDetectorManagerMlkit.h */,
				2647D6702256BBD5007D2F91 /* FaceDetectorManagerMlkit.m */,
				71C7FFD42013C824006EB75A /* RNFileSystem.h */,
				71C7FFD52013C824006EB75A /* RNFileSystem.m */,
				2647D6732256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.h */,
				2647D6722256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.m */,
				71C7FFD12013C817006EB75A /* RNImageUtils.h */,
				71C7FFD22013C817006EB75A /* RNImageUtils.m */,
				71C7FFCE2013C7E5006EB75A /* RNCameraUtils.h */,
				71C7FFCF2013C7E5006EB75A /* RNCameraUtils.m */,
				71C7FFC82013C7AE006EB75A /* RNCameraManager.h */,
				71C7FFC92013C7AE006EB75A /* RNCameraManager.m */,
				71C7FFCB2013C7BF006EB75A /* RNCamera.h */,
				71C7FFCC2013C7BF006EB75A /* RNCamera.m */,
				A7D02BC7243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.h */,
				A7D02BC8243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.m */,
			);
			path = RN;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4107012E1ACB723B00C6AA39 /* RNCamera */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 410701411ACB723B00C6AA39 /* Build configuration list for PBXNativeTarget "RNCamera" */;
			buildPhases = (
				4107012B1ACB723B00C6AA39 /* Sources */,
				4107012C1ACB723B00C6AA39 /* Frameworks */,
				4107012D1ACB723B00C6AA39 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNCamera;
			productName = RCTCamera;
			productReference = 4107012F1ACB723B00C6AA39 /* libRNCamera.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		410701251ACB719800C6AA39 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0620;
				TargetAttributes = {
					4107012E1ACB723B00C6AA39 = {
						CreatedOnToolsVersion = 6.2;
					};
				};
			};
			buildConfigurationList = 410701281ACB719800C6AA39 /* Build configuration list for PBXProject "RNCamera" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 410701241ACB719800C6AA39;
			productRefGroup = 410701301ACB723B00C6AA39 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4107012E1ACB723B00C6AA39 /* RNCamera */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		4107012B1ACB723B00C6AA39 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2647D6742256BBE8007D2F91 /* RNFaceDetectorModuleMLKit.m in Sources */,
				0314E39D1B661A460092D183 /* CameraFocusSquare.m in Sources */,
				454EBCF41B5082DC00AD0F86 /* NSMutableDictionary+ImageMetadata.m in Sources */,
				71C7FFD62013C824006EB75A /* RNFileSystem.m in Sources */,
				A7D02BC9243627E100C0EDE6 /* RNCustomWhiteBalanceSettings.m in Sources */,
				4107014E1ACB732B00C6AA39 /* RCTCameraManager.m in Sources */,
				4107014D1ACB732B00C6AA39 /* RCTCamera.m in Sources */,
				1354413B2487FF390024C19F /* BarcodeDetectorManagerMlkit.m in Sources */,
				F8393BEC21469C0000AB1995 /* RNSensorOrientationChecker.m in Sources */,
				71C7FFD02013C7E5006EB75A /* RNCameraUtils.m in Sources */,
				7162BE682013EAA400FE51FF /* RNCameraManager.m in Sources */,
				7162BE672013EAA100FE51FF /* RNCamera.m in Sources */,
				9FE592B31CA3CBF500788287 /* RCTSensorOrientationChecker.m in Sources */,
				2647D6712256BBD5007D2F91 /* FaceDetectorManagerMlkit.m in Sources */,
				71C7FFD32013C817006EB75A /* RNImageUtils.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		410701291ACB719800C6AA39 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Debug;
		};
		4107012A1ACB719800C6AA39 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Release;
		};
		410701421ACB723B00C6AA39 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES = "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj node_modules";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../../ios/**",
					"${BUILT_PRODUCTS_DIR}/**",
					"$(SRCROOT)/../../ios/**",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.2;
				LIBRARY_SEARCH_PATHS = "";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		410701431ACB723B00C6AA39 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES = "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj node_modules";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../../ios/**",
					"${BUILT_PRODUCTS_DIR}/**",
					"$(SRCROOT)/../../ios/**",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.2;
				LIBRARY_SEARCH_PATHS = "";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		410701281ACB719800C6AA39 /* Build configuration list for PBXProject "RNCamera" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				410701291ACB719800C6AA39 /* Debug */,
				4107012A1ACB719800C6AA39 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		410701411ACB723B00C6AA39 /* Build configuration list for PBXNativeTarget "RNCamera" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				410701421ACB723B00C6AA39 /* Debug */,
				410701431ACB723B00C6AA39 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 410701251ACB719800C6AA39 /* Project object */;
}
