{"version": 3, "sources": ["../../src/link/writePlist.ts"], "names": ["writePlist", "project", "sourceDir", "plist", "plist<PERSON><PERSON>", "fs", "writeFileSync", "p<PERSON><PERSON><PERSON><PERSON>", "build", "indent", "offset"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACe,SAASA,UAAT,CACbC,OADa,EAEbC,SAFa,EAGbC,KAHa,EAIb;AACA,QAAMC,SAAS,GAAG,2BAAaH,OAAb,EAAsBC,SAAtB,CAAlB;;AAEA,MAAI,CAACE,SAAL,EAAgB;AACd,WAAO,IAAP;AACD,GALD,CAOA;AACA;AACA;;;AACA,SAAOC,cAAGC,aAAH,CACLF,SADK,EAEL;AACC,KAAEG,iBAAYC,KAAZ,CAAkBL,KAAlB,EAAyB;AAACM,IAAAA,MAAM,EAAE,IAAT;AAAeC,IAAAA,MAAM,EAAE,CAAC;AAAxB,GAAzB,CAAqD,IAHnD,CAAP;AAKD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport plistParser, {PlistValue} from 'plist';\nimport fs from 'fs';\nimport getPlistPath from './getPlistPath';\n\n/**\n * Writes to Info.plist located in the iOS project\n *\n * Returns `null` if INFOPLIST_FILE is not specified or file is non-existent.\n */\nexport default function writePlist(\n  project: any,\n  sourceDir: string,\n  plist: PlistValue | null,\n) {\n  const plistPath = getPlistPath(project, sourceDir);\n\n  if (!plistPath) {\n    return null;\n  }\n\n  // We start with an offset of -1, because Xcode maintains a custom\n  // indentation of the plist.\n  // Ref: https://github.com/facebook/react-native/issues/11668\n  return fs.writeFileSync(\n    plistPath,\n    // @ts-ignore Type mismatch\n    `${plistParser.build(plist, {indent: '\\t', offset: -1})}\\n`,\n  );\n}\n"]}