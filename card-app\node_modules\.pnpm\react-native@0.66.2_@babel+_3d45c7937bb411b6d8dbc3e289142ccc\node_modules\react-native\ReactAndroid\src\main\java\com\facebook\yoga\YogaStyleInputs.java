/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.yoga;

public class YogaStyleInputs {
  public static final short LAYOUT_DIRECTION = 0;
  public static final short FLEX_DIRECTION = 1;
  public static final short FLEX = 2;
  public static final short FLEX_GROW = 3;
  public static final short FLEX_SHRINK = 4;
  public static final short FLEX_BASIS = 5;
  public static final short FLEX_BASIS_PERCENT = 6;
  public static final short FLEX_BASIS_AUTO = 7;
  public static final short FLEX_WRAP = 8;
  public static final short WIDTH = 9;
  public static final short WIDTH_PERCENT = 10;
  public static final short WIDTH_AUTO = 11;
  public static final short MIN_WIDTH = 12;
  public static final short MIN_WIDTH_PERCENT = 13;
  public static final short MAX_WIDTH = 14;
  public static final short MAX_WIDTH_PERCENT = 15;
  public static final short HEIGHT = 16;
  public static final short HEIGHT_PERCENT = 17;
  public static final short HEIGHT_AUTO = 18;
  public static final short MIN_HEIGHT = 19;
  public static final short MIN_HEIGHT_PERCENT = 20;
  public static final short MAX_HEIGHT = 21;
  public static final short MAX_HEIGHT_PERCENT = 22;
  public static final short JUSTIFY_CONTENT = 23;
  public static final short ALIGN_ITEMS = 24;
  public static final short ALIGN_SELF = 25;
  public static final short ALIGN_CONTENT = 26;
  public static final short POSITION_TYPE = 27;
  public static final short ASPECT_RATIO = 28;
  public static final short OVERFLOW = 29;
  public static final short DISPLAY = 30;
  public static final short MARGIN = 31;
  public static final short MARGIN_PERCENT = 32;
  public static final short MARGIN_AUTO = 33;
  public static final short PADDING = 34;
  public static final short PADDING_PERCENT = 35;
  public static final short BORDER = 36;
  public static final short POSITION = 37;
  public static final short POSITION_PERCENT = 38;
  public static final short IS_REFERENCE_BASELINE = 39;
}
