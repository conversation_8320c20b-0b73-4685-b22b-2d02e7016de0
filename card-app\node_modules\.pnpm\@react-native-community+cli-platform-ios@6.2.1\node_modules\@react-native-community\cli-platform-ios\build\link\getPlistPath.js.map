{"version": 3, "sources": ["../../src/link/getPlistPath.ts"], "names": ["getPlistPath", "project", "sourceDir", "plistFile", "path", "join", "replace"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,YAAT,CAAsBC,OAAtB,EAAuCC,SAAvC,EAA0D;AACvE,QAAMC,SAAS,GAAG,+BAAiBF,OAAjB,EAA0B,gBAA1B,CAAlB;;AAEA,MAAI,CAACE,SAAL,EAAgB;AACd,WAAO,IAAP;AACD;;AAED,SAAOC,gBAAKC,IAAL,CACLH,SADK,EAELC,SAAS,CAACG,OAAV,CAAkB,IAAlB,EAAwB,EAAxB,EAA4BA,OAA5B,CAAoC,YAApC,EAAkD,EAAlD,CAFK,CAAP;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport getBuildProperty from './getBuildProperty';\n\nexport default function getPlistPath(project: string, sourceDir: string) {\n  const plistFile = getBuildProperty(project, 'INFOPLIST_FILE');\n\n  if (!plistFile) {\n    return null;\n  }\n\n  return path.join(\n    sourceDir,\n    plistFile.replace(/\"/g, '').replace('$(SRCROOT)', ''),\n  );\n}\n"]}