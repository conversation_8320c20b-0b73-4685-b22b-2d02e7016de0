{"version": 3, "sources": ["../../src/config/getPodspecName.ts"], "names": ["getPodspecName", "podspecFile", "path", "basename", "replace"], "mappings": ";;;;;;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAPA;AACA;AACA;AACA;AACA;AACA;AACA;AAGe,SAASA,cAAT,CAAwBC,WAAxB,EAA6C;AAC1D,SAAOC,gBAAKC,QAAL,CAAcF,WAAd,EAA2BG,OAA3B,CAAmC,YAAnC,EAAiD,EAAjD,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport path from 'path';\n\nexport default function getPodspecName(podspecFile: string) {\n  return path.basename(podspecFile).replace(/\\.podspec$/, '');\n}\n"]}