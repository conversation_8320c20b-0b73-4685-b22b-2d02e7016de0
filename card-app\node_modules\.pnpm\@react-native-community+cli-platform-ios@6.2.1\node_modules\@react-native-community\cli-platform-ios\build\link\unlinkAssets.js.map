{"version": 3, "sources": ["../../src/link/unlinkAssets.ts"], "names": ["unlinkAssetsIOS", "files", "projectConfig", "project", "xcode", "pbxproj<PERSON><PERSON>", "parseSync", "assets", "plist", "sourceDir", "logger", "error", "pbxGroupByName", "removeResourceFiles", "f", "map", "asset", "debug", "removeResourceFile", "path", "relative", "target", "get<PERSON><PERSON>t<PERSON>arget", "uuid", "file", "basename", "image", "fonts", "font", "UIAppFonts", "fs", "writeFileSync", "writeSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAfA;AACA;AACA;AACA;AACA;AACA;AACA;;AAYA;AACA;AACA;AACA;AACe,SAASA,eAAT,CACbC,KADa,EAEbC,aAFa,EAGb;AACA,QAAMC,OAAO,GAAGC,iBAAMD,OAAN,CAAcD,aAAa,CAACG,WAA5B,EAAyCC,SAAzC,EAAhB;;AACA,QAAMC,MAAM,GAAG,kCAAiBN,KAAjB,CAAf;AACA,QAAMO,KAAK,GAAG,uBAASL,OAAT,EAAkBD,aAAa,CAACO,SAAhC,CAAd;;AAEA,MAAI,CAACD,KAAL,EAAY;AACVE,uBAAOC,KAAP,CACE,6FADF;;AAGA;AACD;;AAED,MAAI,CAACR,OAAO,CAACS,cAAR,CAAuB,WAAvB,CAAL,EAA0C;AACxCF,uBAAOC,KAAP,CACE,qFADF;;AAGA;AACD;;AAED,QAAME,mBAAmB,GAAG,CAACC,CAAa,GAAG,EAAjB,KAC1B,CAACA,CAAC,IAAI,EAAN,EACGC,GADH,CACQC,KAAD,IAAW;AACdN,uBAAOO,KAAP,CAAc,mBAAkBD,KAAM,EAAtC;;AACA,WAAOb,OAAO,CAACe,kBAAR,CACLC,gBAAKC,QAAL,CAAclB,aAAa,CAACO,SAA5B,EAAuCO,KAAvC,CADK,EAEL;AAACK,MAAAA,MAAM,EAAElB,OAAO,CAACmB,cAAR,GAAyBC;AAAlC,KAFK,CAAP;AAID,GAPH,EAQGR,GARH,CAQQS,IAAD,IAAUA,IAAI,CAACC,QARtB,CADF;;AAWAZ,EAAAA,mBAAmB,CAACN,MAAM,CAACmB,KAAR,CAAnB;AAEA,QAAMC,KAAK,GAAGd,mBAAmB,CAACN,MAAM,CAACqB,IAAR,CAAjC,CAhCA,CAkCA;;AACApB,EAAAA,KAAK,CAACqB,UAAN,GAAmB,0BAAWrB,KAAK,CAACqB,UAAN,IAAoB,EAA/B,EAAmCF,KAAnC,CAAnB;;AAEAG,gBAAGC,aAAH,CAAiB7B,aAAa,CAACG,WAA/B,EAA4CF,OAAO,CAAC6B,SAAR,EAA5C;;AAEA,2BAAW7B,OAAX,EAAoBD,aAAa,CAACO,SAAlC,EAA6CD,KAA7C;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport path from 'path';\nimport xcode from 'xcode';\nimport {difference} from 'lodash';\n\nimport getPlist from './getPlist';\nimport writePlist from './writePlist';\nimport {logger, groupFilesByType} from '@react-native-community/cli-tools';\nimport {IOSProjectConfig} from '@react-native-community/cli-types';\n\n/**\n * Unlinks assets from iOS project. Removes references for fonts from `Info.plist`\n * fonts provided by application and from `Resources` group\n */\nexport default function unlinkAssetsIOS(\n  files: any,\n  projectConfig: IOSProjectConfig,\n) {\n  const project = xcode.project(projectConfig.pbxprojPath).parseSync();\n  const assets = groupFilesByType(files);\n  const plist = getPlist(project, projectConfig.sourceDir);\n\n  if (!plist) {\n    logger.error(\n      'Could not locate \"Info.plist\" file. Check if your project has \"INFOPLIST_FILE\" set properly',\n    );\n    return;\n  }\n\n  if (!project.pbxGroupByName('Resources')) {\n    logger.error(\n      'Group \"Resources\" does not exist in your Xcode project. There is nothing to unlink.',\n    );\n    return;\n  }\n\n  const removeResourceFiles = (f: Array<any> = []) =>\n    (f || [])\n      .map((asset) => {\n        logger.debug(`Unlinking asset ${asset}`);\n        return project.removeResourceFile(\n          path.relative(projectConfig.sourceDir, asset),\n          {target: project.getFirstTarget().uuid},\n        );\n      })\n      .map((file) => file.basename);\n\n  removeResourceFiles(assets.image);\n\n  const fonts = removeResourceFiles(assets.font);\n\n  // @ts-ignore Type mismatch\n  plist.UIAppFonts = difference(plist.UIAppFonts || [], fonts);\n\n  fs.writeFileSync(projectConfig.pbxprojPath, project.writeSync());\n\n  writePlist(project, projectConfig.sourceDir, plist);\n}\n"]}