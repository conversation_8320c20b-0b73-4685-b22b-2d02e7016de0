{"version": 3, "sources": ["../../src/link/registerNativeModule.ts"], "names": ["registerNativeModuleIOS", "dependencyConfig", "projectConfig", "logger", "debug", "pbxproj<PERSON><PERSON>", "project", "xcode", "parseSync", "dependencyProject", "libraries", "libraryFolder", "file", "path", "relative", "sourceDir", "projectPath", "targets", "for<PERSON>ach", "product", "i", "isTVOS", "length", "name", "target", "addStaticLibrary", "uuid", "sharedLibraries", "headers", "folder", "fs", "writeFileSync", "writeSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAKA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAxBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,uBAAT,CACbC,gBADa,EAEbC,aAFa,EAGb;AACAC,qBAAOC,KAAP,CAAc,WAAUF,aAAa,CAACG,WAAY,EAAlD;;AACA,QAAMC,OAAO,GAAGC,iBAAMD,OAAN,CAAcJ,aAAa,CAACG,WAA5B,EAAyCG,SAAzC,EAAhB;;AACA,QAAMC,iBAAiB,GAAGF,iBACvBD,OADuB,CACfL,gBAAgB,CAACI,WADF,EAEvBG,SAFuB,EAA1B;;AAIA,QAAME,SAAS,GAAG,qCAChBJ,OADgB,EAEhBJ,aAAa,CAACS,aAFE,CAAlB;AAIA,QAAMC,IAAI,GAAG,+BACXN,OADW,EAEXO,gBAAKC,QAAL,CAAcZ,aAAa,CAACa,SAA5B,EAAuCd,gBAAgB,CAACe,WAAxD,CAFW,CAAb;AAKA,QAAMC,OAAO,GAAG,yBAAWX,OAAX,CAAhB;AAEA,sCAAsBI,SAAtB,EAAiCE,IAAjC;AAEA,2BAAWH,iBAAX,EAA8BS,OAA9B,CAAuCC,OAAD,IAAkB;AACtD,QAAIC,CAAJ;;AACA,QAAI,CAACD,OAAO,CAACE,MAAb,EAAqB;AACnB,WAAKD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGH,OAAO,CAACK,MAAxB,EAAgCF,CAAC,EAAjC,EAAqC;AACnC,YAAI,CAACH,OAAO,CAACG,CAAD,CAAP,CAAWC,MAAhB,EAAwB;AACtBlB,6BAAOC,KAAP,CAAc,UAASe,OAAO,CAACI,IAAK,OAAMN,OAAO,CAACG,CAAD,CAAP,CAAWI,MAAX,CAAkBD,IAAK,EAAjE;;AACAjB,UAAAA,OAAO,CAACmB,gBAAR,CAAyBN,OAAO,CAACI,IAAjC,EAAuC;AACrCC,YAAAA,MAAM,EAAEP,OAAO,CAACG,CAAD,CAAP,CAAWM;AADkB,WAAvC;AAGD;AACF;AACF;;AAED,QAAIP,OAAO,CAACE,MAAZ,EAAoB;AAClB,WAAKD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGH,OAAO,CAACK,MAAxB,EAAgCF,CAAC,EAAjC,EAAqC;AACnC,YAAIH,OAAO,CAACG,CAAD,CAAP,CAAWC,MAAf,EAAuB;AACrBlB,6BAAOC,KAAP,CAAc,UAASe,OAAO,CAACI,IAAK,OAAMN,OAAO,CAACG,CAAD,CAAP,CAAWI,MAAX,CAAkBD,IAAK,EAAjE;;AACAjB,UAAAA,OAAO,CAACmB,gBAAR,CAAyBN,OAAO,CAACI,IAAjC,EAAuC;AACrCC,YAAAA,MAAM,EAAEP,OAAO,CAACG,CAAD,CAAP,CAAWM;AADkB,WAAvC;AAGD;AACF;AACF;AACF,GAvBD;AAyBA,mCAAmBpB,OAAnB,EAA4BL,gBAAgB,CAAC0B,eAA7C;AAEA,QAAMC,OAAO,GAAG,iCAAmB3B,gBAAgB,CAAC4B,MAApC,CAAhB;;AACA,MAAI,CAAC,uBAAQD,OAAR,CAAL,EAAuB;AACrB,yCACEtB,OADF,EAEE,kCAAoBJ,aAAa,CAACa,SAAlC,EAA6Ca,OAA7C,CAFF;AAID;;AAEDzB,qBAAOC,KAAP,CAAc,sBAAqBF,aAAa,CAACG,WAAY,EAA7D;;AACAyB,gBAAGC,aAAH,CAAiB7B,aAAa,CAACG,WAA/B,EAA4CC,OAAO,CAAC0B,SAAR,EAA5C;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport xcode from 'xcode';\nimport fs from 'fs';\nimport path from 'path';\nimport {isEmpty} from 'lodash';\nimport {\n  IOSDependencyConfig,\n  IOSProjectConfig,\n} from '@react-native-community/cli-types';\nimport addToHeaderSearchPaths from './addToHeaderSearchPaths';\nimport getHeadersInFolder from './getHeadersInFolder';\nimport getHeaderSearchPath from './getHeaderSearchPath';\nimport getTargets from './getTargets';\nimport createGroupWithMessage from './createGroupWithMessage';\nimport addFileToProject from './addFileToProject';\nimport addProjectToLibraries from './addProjectToLibraries';\nimport addSharedLibraries from './addSharedLibraries';\nimport {logger} from '@react-native-community/cli-tools';\n\n/**\n * Register native module IOS adds given dependency to project by adding\n * its xcodeproj to project libraries as well as attaching static library\n * to the first target (the main one)\n *\n * If library is already linked, this action is a no-op.\n */\nexport default function registerNativeModuleIOS(\n  dependencyConfig: IOSDependencyConfig,\n  projectConfig: IOSProjectConfig,\n) {\n  logger.debug(`Reading ${projectConfig.pbxprojPath}`);\n  const project = xcode.project(projectConfig.pbxprojPath).parseSync();\n  const dependencyProject = xcode\n    .project(dependencyConfig.pbxprojPath)\n    .parseSync();\n\n  const libraries = createGroupWithMessage(\n    project,\n    projectConfig.libraryFolder,\n  );\n  const file = addFileToProject(\n    project,\n    path.relative(projectConfig.sourceDir, dependencyConfig.projectPath),\n  );\n\n  const targets = getTargets(project);\n\n  addProjectToLibraries(libraries, file);\n\n  getTargets(dependencyProject).forEach((product: any) => {\n    let i;\n    if (!product.isTVOS) {\n      for (i = 0; i < targets.length; i++) {\n        if (!targets[i].isTVOS) {\n          logger.debug(`Adding ${product.name} to ${targets[i].target.name}`);\n          project.addStaticLibrary(product.name, {\n            target: targets[i].uuid,\n          });\n        }\n      }\n    }\n\n    if (product.isTVOS) {\n      for (i = 0; i < targets.length; i++) {\n        if (targets[i].isTVOS) {\n          logger.debug(`Adding ${product.name} to ${targets[i].target.name}`);\n          project.addStaticLibrary(product.name, {\n            target: targets[i].uuid,\n          });\n        }\n      }\n    }\n  });\n\n  addSharedLibraries(project, dependencyConfig.sharedLibraries);\n\n  const headers = getHeadersInFolder(dependencyConfig.folder);\n  if (!isEmpty(headers)) {\n    addToHeaderSearchPaths(\n      project,\n      getHeaderSearchPath(projectConfig.sourceDir, headers),\n    );\n  }\n\n  logger.debug(`Writing changes to ${projectConfig.pbxprojPath}`);\n  fs.writeFileSync(projectConfig.pbxprojPath, project.writeSync());\n}\n"]}