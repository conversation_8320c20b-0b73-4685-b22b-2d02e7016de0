{"version": 3, "sources": ["../../../src/link/common/unregisterNativeModule.ts"], "names": ["unregisterNativeModule", "_name", "dependencyConfig", "projectConfig", "otherDependencies", "isIosInstalled", "isPodInstalled", "iOSDependencies", "map", "d", "platforms", "ios"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAZA;AACA;AACA;AACA;AACA;AACA;AACA;AAYe,SAASA,sBAAT,CACbC,KADa,EAEbC,gBAFa,EAGbC,aAHa,EAIb;AACAC,iBALa,EAMb;AACA,QAAMC,cAAc,GAAG,0BAAeF,aAAf,EAA8BD,gBAA9B,CAAvB;AACA,QAAMI,cAAc,GAAG,2BAAgBH,aAAhB,EAA+BD,gBAA/B,CAAvB;;AACA,MAAIG,cAAJ,EAAoB;AAClB,UAAME,eAAe,GAAG,uBACtBH,iBAAiB,CAACI,GAAlB,CAAuBC,CAAD,IAAOA,CAAC,CAACC,SAAF,CAAYC,GAAzC,CADsB,CAAxB;AAGA,yCAAwBT,gBAAxB,EAA0CC,aAA1C,EAAyDI,eAAzD;AACD,GALD,MAKO,IAAID,cAAJ,EAAoB;AACzB,0CAAyBJ,gBAAzB,EAA2CC,aAA3C;AACD;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {compact} from 'lodash';\nimport isInstalledIOS from '../isInstalled';\nimport isInstalledPods from '../../link-pods/isInstalled';\nimport unregisterDependencyIOS from '../unregisterNativeModule';\nimport unregisterDependencyPods from '../../link-pods/unregisterNativeModule';\nimport {\n  IOSDependencyConfig,\n  IOSProjectConfig,\n} from '@react-native-community/cli-types';\n\nexport default function unregisterNativeModule(\n  _name: string,\n  dependencyConfig: IOSDependencyConfig,\n  projectConfig: IOSProjectConfig,\n  // FIXME: Add type signature here\n  otherDependencies: Array<any>,\n) {\n  const isIosInstalled = isInstalledIOS(projectConfig, dependencyConfig);\n  const isPodInstalled = isInstalledPods(projectConfig, dependencyConfig);\n  if (isIosInstalled) {\n    const iOSDependencies = compact(\n      otherDependencies.map((d) => d.platforms.ios),\n    );\n    unregisterDependencyIOS(dependencyConfig, projectConfig, iOSDependencies);\n  } else if (isPodInstalled) {\n    unregisterDependencyPods(dependencyConfig, projectConfig);\n  }\n}\n"]}