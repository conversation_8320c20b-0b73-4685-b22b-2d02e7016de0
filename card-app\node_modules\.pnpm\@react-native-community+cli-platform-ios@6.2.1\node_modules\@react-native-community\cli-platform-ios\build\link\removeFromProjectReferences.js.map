{"version": 3, "sources": ["../../src/link/removeFromProjectReferences.ts"], "names": ["removeFromProjectReferences", "project", "file", "firstProject", "getFirstProject", "projectRef", "projectReferences", "find", "item", "ProjectRef", "uuid", "splice", "indexOf"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,2BAAT,CAAqCC,OAArC,EAAmDC,IAAnD,EAA8D;AAC3E,QAAM;AAACC,IAAAA;AAAD,MAAiBF,OAAO,CAACG,eAAR,EAAvB;AAEA,QAAMC,UAAU,GAAGF,YAAY,CAACG,iBAAb,CAA+BC,IAA/B,CAChBC,IAAD,IAAeA,IAAI,CAACC,UAAL,KAAoBP,IAAI,CAACQ,IADvB,CAAnB;;AAIA,MAAI,CAACL,UAAL,EAAiB;AACf,WAAO,IAAP;AACD;;AAEDF,EAAAA,YAAY,CAACG,iBAAb,CAA+BK,MAA/B,CACER,YAAY,CAACG,iBAAb,CAA+BM,OAA/B,CAAuCP,UAAvC,CADF,EAEE,CAFF;AAKA,SAAOA,UAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * For each file (.xcodeproj), there's an entry in `projectReferences` created\n * that has two entries - `ProjectRef` - reference to a file.uuid and\n * `ProductGroup` - uuid of a Products group.\n *\n * When projectReference is found - it's deleted and the removed value is returned\n * so that ProductGroup in PBXGroup section can be removed as well.\n *\n * Otherwise returns null\n */\nexport default function removeFromProjectReferences(project: any, file: any) {\n  const {firstProject} = project.getFirstProject();\n\n  const projectRef = firstProject.projectReferences.find(\n    (item: any) => item.ProjectRef === file.uuid,\n  );\n\n  if (!projectRef) {\n    return null;\n  }\n\n  firstProject.projectReferences.splice(\n    firstProject.projectReferences.indexOf(projectRef),\n    1,\n  );\n\n  return projectRef;\n}\n"]}