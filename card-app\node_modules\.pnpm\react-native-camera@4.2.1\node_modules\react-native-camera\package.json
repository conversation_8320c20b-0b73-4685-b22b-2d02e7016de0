{"name": "react-native-camera", "description": "A Camera component for React Native. Also reads barcodes.", "version": "4.2.1", "author": "<PERSON><PERSON> <<EMAIL>> (http://lwansbrough.com)", "collective": {"type": "opencollective", "url": "https://opencollective.com/react-native-camera", "logo": "https://opencollective.com/opencollective/logo.txt"}, "dependencies": {"prop-types": "^15.6.2"}, "devDependencies": {"@babel/runtime": "^7.3.1", "@commitlint/cli": "^9.1.2", "@commitlint/config-conventional": "^9.1.2", "@commitlint/prompt": "^9.1.2", "@semantic-release/commit-analyzer": "^8.0.1", "@semantic-release/git": "^9.0.0", "@semantic-release/github": "^7.0.7", "@semantic-release/npm": "^7.0.5", "@semantic-release/release-notes-generator": "^9.0.1", "@types/react": "^16.8.3", "@types/react-native": "^0.63.13", "babel-eslint": "^10.1.0", "commitizen": "^4.2.1", "eslint": "^7.8.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-react": "^7.10.0", "eslint-plugin-react-native": "^3.2.1", "husky": "^4.2.5", "lint-staged": "^10.2.13", "pre-commit": "^1.2.2", "prettier": "^2.1.1", "react": "16.13.1", "react-native": "0.63.2", "semantic-release": "^17.1.1"}, "homepage": "https://github.com/react-native-community/react-native-camera", "keywords": ["barcode", "camera", "code", "native", "qr", "react", "react-native"], "license": "MIT AND Apache-2.0 AND BSD-3-<PERSON><PERSON>", "lint-staged": {"*.js": ["yarn prettier", "eslint --fix", "git add"], "*.d.ts": ["yarn prettier", "git add"], "*.md": ["yarn prettier", "git add"]}, "main": "src/index.js", "nativePackage": true, "pre-commit": "lint:staged", "repository": {"type": "git", "url": "https://github.com/react-native-community/react-native-camera.git"}, "scripts": {"commit": "git-cz", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint src --max-warnings=0", "lint:staged": "lint-staged", "prettier": "prettier --write", "ci:publish": "semantic-release"}, "types": "types/index.d.ts", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", ["@semantic-release/github", {"assets": ["package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/git", {"assets": "package.json", "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}, "config": {"commitizen": {"path": "@commitlint/prompt"}}}