/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { IOSDependencyConfig, IOSProjectConfig, IOSProjectParams } from '@react-native-community/cli-types';
/**
 * Unregister native module IOS
 *
 * If library is already unlinked, this action is a no-op.
 */
export default function unregisterNativeModule(dependencyConfig: IOSDependencyConfig, projectConfig: IOSProjectConfig, iOSDependencies: Array<IOSProjectParams>): void;
//# sourceMappingURL=unregisterNativeModule.d.ts.map