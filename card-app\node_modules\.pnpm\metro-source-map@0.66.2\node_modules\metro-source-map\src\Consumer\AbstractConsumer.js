/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 */
"use strict";

const invariant = require("invariant");

const { GENERATED_ORDER, iterationOrderToString } = require("./constants");

// Implementation details shared between MappingsConsumer and SectionsConsumer
class AbstractConsumer {
  constructor(sourceMap) {
    this._sourceMap = sourceMap;
  }

  originalPositionFor(generatedPosition) {
    invariant(false, "Not implemented");
  }

  generatedMappings() {
    invariant(false, "Not implemented");
  }

  eachMapping(callback, context = null, order = GENERATED_ORDER) {
    invariant(
      order === GENERATED_ORDER,
      `Iteration order not implemented: ${iterationOrderToString(order)}`
    );

    for (const mapping of this.generatedMappings()) {
      callback.call(context, mapping);
    }
  } // flowlint-next-line unsafe-getters-setters:off

  get file() {
    return this._sourceMap.file;
  }

  sourceContentFor(source, nullOnMissing) {
    invariant(false, "Not implemented");
  }
}

module.exports = AbstractConsumer;
