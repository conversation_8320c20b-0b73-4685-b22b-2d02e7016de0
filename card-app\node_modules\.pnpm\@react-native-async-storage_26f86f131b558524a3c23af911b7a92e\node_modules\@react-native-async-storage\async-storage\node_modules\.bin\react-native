#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc/node_modules/react-native/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc/node_modules/react-native/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc/node_modules/react-native/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc/node_modules/react-native/cli.js" "$@"
fi
