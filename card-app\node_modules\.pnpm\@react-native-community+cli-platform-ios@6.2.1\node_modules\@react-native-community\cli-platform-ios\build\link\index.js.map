{"version": 3, "sources": ["../../src/link/index.ts"], "names": ["getIOSLinkConfig", "isInstalled", "register", "unregister", "copyAssets", "unlinkAssets"], "mappings": ";;;;;;;;AAQA;;AACA;;AACA;;AACA;;AACA;;;;AAZA;AACA;AACA;AACA;AACA;AACA;AACA;AAQO,SAASA,gBAAT,GAA4B;AACjC,SAAO;AAACC,IAAAA,WAAW,EAAXA,oBAAD;AAAcC,IAAAA,QAAQ,EAARA,6BAAd;AAAwBC,IAAAA,UAAU,EAAVA,+BAAxB;AAAoCC,IAAAA,UAAU,EAAVA,mBAApC;AAAgDC,IAAAA,YAAY,EAAZA;AAAhD,GAAP;AACD;;eAEcL,gB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport isInstalled from './common/isInstalled';\nimport register from './common/registerNativeModule';\nimport unregister from './common/unregisterNativeModule';\nimport copyAssets from './copyAssets';\nimport unlinkAssets from './unlinkAssets';\n\nexport function getIOSLinkConfig() {\n  return {isInstalled, register, unregister, copyAssets, unlinkAssets};\n}\n\nexport default getIOSLinkConfig;\n"]}