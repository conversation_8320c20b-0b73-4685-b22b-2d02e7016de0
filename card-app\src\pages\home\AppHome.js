/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2021-12-24 00:35:56
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/pages/home/<USER>
 * love jiajia
 */
import React, {Component} from 'react';
import { View, Text, StyleSheet, DeviceEventEmitter, ToastAndroid,TouchableHighlight, Alert, Modal, Linking, Image } from 'react-native';
import { List, NoticeBar, Flex } from '@ant-design/react-native';
import Layout from '../../components/Layout';
import Loading from '../../components/Loading';
import Tools from '@utils/tools';
import moment from 'moment';
const Item = List.Item;

export default class AppHomeScreen extends Component {
  static navigationOptions = {
    headerShown : false
  }
  
  state = {
    nfc : false,
    num : 0,
    init: false,
    loading: false,
    update: false
  }

  async componentDidMount() {
    const nfc  = await Tools.getData('NFC')
    this.setState({
      nfc,
      num: parseInt(await Tools.getData('num'), 10)
    })    
    this.listener = DeviceEventEmitter.addListener('nfc',(e)=>{
      this.props.navigation.navigate('UserInfo', {
        data : {
          txt: e,
          end: moment(moment().add(10 ,'m')).unix().valueOf(),
          way: 0
        }
      })
    });
    this.listenerBack = DeviceEventEmitter.addListener("hardwareBackPress",  async (e) => {
      //await this.getData()
    });
    this.listenerHeaderBack = DeviceEventEmitter.addListener("nickBack", async (e) => {
      await this.getData()
    });
    await this.getData()
  }
  
  componentWillUnmount() {
    this.listener.remove();
    this.listenerBack.remove();
    this.listenerHeaderBack.remove();
  }

  async getData () {
    this.setState({
      loading: true
    })
    const data = await Tools.Api('showData', 'POST', {vin : Tools.vin})
    if (data.code === 200) {
      this.setState({
        init: data.data,
        loading: false,
        update: data.data.update
      })
    } else {
      ToastAndroid.show(data.message, 2000)
      this.setState({
        loading: false
      })
    }
  }

  showAlert () {
    Alert.alert(
    "系统消息",
    "确定退出登录状态？",
    [
      {
        text: "确定",
        onPress: async () => {
          await Tools.setData('token', null)
          this.props.navigation.navigate('Login')
        },
        style: "cancel",
      },
      {
        text: "取消",
        onPress: () => {

        },
        style: "cancel",
      },
    ]
    );
  }


  downLoadApp = async () => {      
    const url = 'https://test.qqyhmmwg.com/checker/appUpdate';
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    } else {
      Alert.alert(`浏览器访问: ${url}`);
    }
  }

  render() {
    const checkTools = [
      {
        icon: 'https://test.qqyhmmwg.com/res/app/scan.png',
        text: '贴卡验证',
      },
      {
        icon: 'https://test.qqyhmmwg.com/res/app/erma.png',
        text: '扫码验证',
      },
      {
        icon: 'https://test.qqyhmmwg.com/res/app/key.png',
        text: '卡密验证',
      }
    ];
    if (this.state.init === false) {
      return  <Layout oper={this.props}><Loading show={this.state.loading}/><Text></Text></Layout>
    }
    return (
      <Layout oper={this.props}>
        {this.state.update ? <Modal
            animationType="slide"
            transparent={true}
            visible={true}
        >
            <View style={styles.centeredView}>
                  <View style={styles.modalView}>
                    <Text style={styles.modalText}>请下载更新版本!</Text>
                    <TouchableHighlight
                      style={{ ...styles.openButton, backgroundColor: "#2196F3" }}
                      onPress={() => {
                        this.setState({
                          update: false
                        }, async () => await this.downLoadApp())
                      }}
                    >
                    <Text style={styles.textStyle}>立即下载</Text>
                    </TouchableHighlight>
                  </View>
            </View>
        </Modal> : null}
        
         <Loading show={this.state.loading}/>
         {
           Boolean(this.state.init.note) ? <NoticeBar
           onPress={() => this.props.navigation.navigate('Web', {url : `noteInfo?code=${this.state.init.note.code}`})}
           marqueeProps={{ loop: true, style: { fontSize: 12, color: 'red' } }
          }
         >
           {this.state.init.note.title}
          </NoticeBar> : null
         }
        <Text style={styles.title}>{this.state.init.brand.brand_name}</Text>
        <View style={{backgroundColor: '#fff', paddingTop: 10, paddingBottom: 10}}>
          <Flex>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4, alignItems: 'center' }}>
              <Text style ={{fontSize : 16, fontWeight: 'bold', color: '#000'}}>今日入园</Text>
              <Text><Text style={{fontSize: 26, color: '#b71b0a', fontWeight: 'bold'}}>{this.state.init.today}</Text><Text> 人</Text></Text>
            </Flex.Item>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4, alignItems: 'center', borderLeftWidth: 1, borderRightWidth: 1, borderStyle: "solid",  borderRightColor: "#ccc",  borderLeftColor: "#ccc" }}>
              <Text style ={{fontSize : 16, fontWeight: 'bold', color: '#000'}}>本周入园</Text>
              <Text><Text style={{fontSize: 26, color: '#b71b0a', fontWeight: 'bold'}}>{this.state.init.week}</Text><Text> 人</Text></Text>
            </Flex.Item>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4, alignItems: 'center' }}>
              <Text style ={{fontSize : 16, fontWeight: 'bold', color: '#000'}}>累计入园</Text>
              <Text><Text style={{fontSize: 26, color: '#b71b0a', fontWeight: 'bold'}}>{this.state.init.all}</Text><Text> 人</Text></Text>
            </Flex.Item>
          </Flex>
        </View>
        <View style={{height: 8}}></View>
         <View style={{backgroundColor: '#ffffff', paddingTop: 10, paddingBottom: 10}}>
          <Flex>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4, alignItems: 'center' }} onTouchEnd = {() => {
              if (this.state.nfc === true || this.state.nfc === 'true') {
                ToastAndroid.show('请将卡片紧贴在机器顶端', 1000)
              } else {
                ToastAndroid.show('该设备不支持NFC贴卡验证', 1000)
              }
            }}>
              <Image
                style={styles.tinyLogo}
                source={{uri: 'https://test.qqyhmmwg.com/res/app/scan.png'}}
              />
              <Text style ={{fontSize : 17, color: '#000', marginTop: 5, color: '#3391ff', fontWeight: "600"}}>贴卡验证</Text>
            </Flex.Item>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4, alignItems: 'center' }}
              onTouchEnd={ async () => { this.props.navigation.navigate('MaLook')}}
              >
              <Image
                style={styles.tinyLogo}
                source={{uri: 'https://test.qqyhmmwg.com/res/app/erma.png'}}
              />
              <Text style ={{fontSize : 17, color: '#000', marginTop: 5, color: '#3391ff', fontWeight: "600"}}>扫码验证</Text>
            </Flex.Item>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4, alignItems: 'center' }}
              onTouchEnd={ async () => {this.props.navigation.navigate('KeyLook')}}
            >
              <Image
                style={styles.tinyLogo}
                source={{uri: 'https://test.qqyhmmwg.com/res/app/key.png'}}
              />
              <Text style ={{fontSize : 17, color: '#000', marginTop: 5, color: '#3391ff', fontWeight: "600"}}>卡密验证</Text>
            </Flex.Item>
          </Flex>
        </View>
        <View style={{height: 8}}></View>
        <List>
        <Item
            thumb="https://test.qqyhmmwg.com/res/app/rule.png"
            arrow="horizontal"
            onPressOut = { () => {
              this.props.navigation.navigate('Web', {url : `brandLimit?code=${this.state.init.brand.code}`})
            }}
          >
            核验规则
          </Item>
        <Item
            thumb="https://test.qqyhmmwg.com/res/app/enter.png"
            arrow="horizontal"
            onPressOut = { () => {
              this.props.navigation.navigate('History')
            }}
          >
            入园记录
          </Item>
          <Item
            thumb="https://test.qqyhmmwg.com/res/app/book.png"
            arrow="horizontal"
            onPressOut = { () => {
              this.props.navigation.navigate('Book')
            }}
          >
            预约记录
          </Item>
        </List>
        <View style={{height: 8}}></View>
        <List>
        <Item
            thumb="https://test.qqyhmmwg.com/res/app/note.png"
            arrow="horizontal"
            onPressOut={ async () => {
              this.props.navigation.navigate('Web', {url : `noteList?code=${this.state.init.brand.code}`})
            }}
          >
            系统通知
          </Item>
          <Item
            thumb="https://test.qqyhmmwg.com/res/app/vin.png"
            arrow="horizontal"
            onPressOut={ async () => {
              if (this.state.init.update === true) {
                this.props.navigation.navigate('Web', {url : `appUpdate`})
              } else {
                ToastAndroid.show('已是最新版本', 1000) 
              }
            }}
            extra={Tools.vin}
          >
            版本检查
          </Item>
          <Item
            thumb="https://test.qqyhmmwg.com/res/app/exit.png"
            arrow="horizontal"
            onPressOut={  () => {
              // await Tools.setData('token', null)
              // this.props.navigation.navigate('Login')
              this.showAlert()
            }}
          >
            登录注销
          </Item>
        </List>
        {/* <View style={styles.container}>
          <Button
            title="Go to Web"
            onPress={ async () => {
              await Tools.linkTo(`tel:${Tools.phone}`)
            }}  
          />
          <Button
            title="Go to Er"
            onPress={ () => {
              this.props.navigation.navigate('MaLook')
            }}
          />
        </View> */}
        <View style={{color: '#666', alignItems: 'center', marginTop: 10}} onTouchEnd={async () => {
          //await this.downLoadApp()
        }}><Text>{Tools.tips}</Text></View>
    </Layout>
    );
  }
}
const styles = StyleSheet.create({
  tinyLogo : {
    width: 45,
    height: 45
  },  
  title : {
    color: '#000000',
    fontSize: 30,
    fontWeight: 'bold',
    paddingLeft: 15,
    paddingTop: 10,
    paddingBottom: 10
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 22
  },
  modalView: {
    width: '60%',
    margin: 20,
    backgroundColor: "white",
    borderRadius: 5,
    padding: 25,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5
  },
  openButton: {
    backgroundColor: "#F194FF",
    borderRadius: 5,
    padding: 10,
    elevation: 2
  },
  textStyle: {
    color: "white",
    fontWeight: "bold",
    textAlign: "center"
  },
  modalText: {
    marginBottom: 15,
    textAlign: "center",
    fontSize: 18,
    color: 'red'
  }
});