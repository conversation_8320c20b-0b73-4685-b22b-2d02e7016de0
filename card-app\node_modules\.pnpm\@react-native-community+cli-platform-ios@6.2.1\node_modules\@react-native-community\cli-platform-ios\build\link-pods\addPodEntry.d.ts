/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
export default function addPodEntry(podLines: Array<string>, linesToAddEntry: Array<{
    line: number;
    indentation: number;
}> | {
    line: number;
    indentation: number;
} | null | undefined, podspecPath: string, nodeModulePath: string): void;
//# sourceMappingURL=addPodEntry.d.ts.map