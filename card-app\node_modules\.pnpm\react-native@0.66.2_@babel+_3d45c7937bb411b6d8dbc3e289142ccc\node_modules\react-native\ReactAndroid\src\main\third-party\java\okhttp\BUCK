load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "okhttp3",
    autoglob = False,
    visibility = ["//ReactAndroid/..."],
    exported_deps = [
        ":okhttp3-binary",
        react_native_dep("third-party/java/okio:okio"),
        # Forces resolver to use OSS Kotlin version
        react_native_target("third-party/kotlin:kotlin-stdlib-jdk8"),
    ],
)

rn_android_library(
    name = "okhttp3-urlconnection",
    autoglob = False,
    visibility = ["//ReactAndroid/..."],
    exported_deps = [
        ":okhttp3",
        ":okhttp3-urlconnection-binary",
    ],
)

rn_prebuilt_jar(
    name = "okhttp3-binary",
    binary_jar = ":okhttp3-binary.jar",
)

fb_native.remote_file(
    name = "okhttp3-binary.jar",
    sha1 = "51215279c3fe472c59b6b7dd7491e6ac2e28a81b",
    url = "mvn:com.squareup.okhttp3:okhttp:jar:4.9.1",
)

rn_prebuilt_jar(
    name = "okhttp3-urlconnection-binary",
    binary_jar = ":okhttp3-urlconnection-binary.jar",
)

fb_native.remote_file(
    name = "okhttp3-urlconnection-binary.jar",
    sha1 = "f45e809215bd0961350148cf5b78707865084e6f",
    url = "mvn:com.squareup.okhttp3:okhttp-urlconnection:jar:4.9.1",
)
