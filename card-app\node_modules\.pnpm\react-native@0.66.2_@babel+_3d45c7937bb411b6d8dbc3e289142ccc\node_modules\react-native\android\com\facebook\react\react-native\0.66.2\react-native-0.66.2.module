{"formatVersion": "1.1", "component": {"group": "com.facebook.react", "module": "react-native", "version": "0.66.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "6.9", "buildId": "rwmtamy2ebcovnwazbol6pl5rm"}}, "variants": [{"name": "releaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.facebook.infer.annotation", "module": "infer-annotation", "version": {"requires": "0.18.0"}}, {"group": "com.facebook.yoga", "module": "proguard-annotations", "version": {"requires": "1.19.0"}}, {"group": "javax.inject", "module": "javax.inject", "version": {"requires": "1"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.0.2"}}, {"group": "androidx.autofill", "module": "autofill", "version": {"requires": "1.1.0"}}, {"group": "androidx.swiperefreshlayout", "module": "swiperefreshlayout", "version": {"requires": "1.0.0"}}, {"group": "com.facebook.fresco", "module": "fresco", "version": {"requires": "2.5.0"}}, {"group": "com.facebook.fresco", "module": "imagepipeline-okhttp3", "version": {"requires": "2.5.0"}}, {"group": "com.facebook.fresco", "module": "ui-common", "version": {"requires": "2.5.0"}}, {"group": "com.facebook.soloader", "module": "soloader", "version": {"requires": "0.10.1"}}, {"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "4.9.1"}}, {"group": "com.squareup.okhttp3", "module": "okhttp-urlconnection", "version": {"requires": "4.9.1"}}, {"group": "com.squareup.okio", "module": "okio", "version": {"requires": "2.9.0"}}, {"group": "com.facebook.fbjni", "module": "fbjni-java-only", "version": {"requires": "0.2.2"}}], "files": [{"name": "react-native-0.66.2.aar", "url": "react-native-0.66.2.aar", "size": 17710261, "sha512": "5db5f669fe0e9917cc28ed0c87f0cf2d8d7f4a76927d6c4c84f00ddd757cf2e7499627bef37cacc3afd85a99131e4edbee4f5ad93646baa9fd6bf7aa606d8a45", "sha256": "f659c22c513bb9151c7b12f047ff9e3e07c5c5f1d6361cf15eceddae65436275", "sha1": "72cf71a9fe00641c2331a33734749bfdc59267c6", "md5": "415c8b2e6145bc56c406b7f9def398b9"}]}, {"name": "releaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.facebook.infer.annotation", "module": "infer-annotation", "version": {"requires": "0.18.0"}}, {"group": "com.facebook.yoga", "module": "proguard-annotations", "version": {"requires": "1.19.0"}}, {"group": "javax.inject", "module": "javax.inject", "version": {"requires": "1"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.0.2"}}, {"group": "androidx.autofill", "module": "autofill", "version": {"requires": "1.1.0"}}, {"group": "androidx.swiperefreshlayout", "module": "swiperefreshlayout", "version": {"requires": "1.0.0"}}, {"group": "com.facebook.fresco", "module": "fresco", "version": {"requires": "2.5.0"}}, {"group": "com.facebook.fresco", "module": "imagepipeline-okhttp3", "version": {"requires": "2.5.0"}}, {"group": "com.facebook.fresco", "module": "ui-common", "version": {"requires": "2.5.0"}}, {"group": "com.facebook.soloader", "module": "soloader", "version": {"requires": "0.10.1"}}, {"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "4.9.1"}}, {"group": "com.squareup.okhttp3", "module": "okhttp-urlconnection", "version": {"requires": "4.9.1"}}, {"group": "com.squareup.okio", "module": "okio", "version": {"requires": "2.9.0"}}, {"group": "com.facebook.fbjni", "module": "fbjni-java-only", "version": {"requires": "0.2.2"}}], "files": [{"name": "react-native-0.66.2.aar", "url": "react-native-0.66.2.aar", "size": 17710261, "sha512": "5db5f669fe0e9917cc28ed0c87f0cf2d8d7f4a76927d6c4c84f00ddd757cf2e7499627bef37cacc3afd85a99131e4edbee4f5ad93646baa9fd6bf7aa606d8a45", "sha256": "f659c22c513bb9151c7b12f047ff9e3e07c5c5f1d6361cf15eceddae65436275", "sha1": "72cf71a9fe00641c2331a33734749bfdc59267c6", "md5": "415c8b2e6145bc56c406b7f9def398b9"}]}]}