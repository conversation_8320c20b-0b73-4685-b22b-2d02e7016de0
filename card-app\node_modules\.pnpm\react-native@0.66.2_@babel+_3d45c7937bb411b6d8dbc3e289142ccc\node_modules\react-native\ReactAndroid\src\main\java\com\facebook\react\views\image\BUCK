load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_target", "rn_android_library")

IMAGE_EVENT_FILES = [
    "ImageLoadEvent.java",
]

rn_android_library(
    name = "imageevents",
    srcs = IMAGE_EVENT_FILES,
    autoglob = False,
    is_androidx = True,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    provided_deps = [
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-ui"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
    ],
    required_for_source_only_abi = True,
    visibility = ["PUBLIC"],
    deps = [
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
    ],
)

rn_android_library(
    name = "image",
    srcs = glob(
        ["*.java"],
        exclude = IMAGE_EVENT_FILES,
    ),
    autoglob = False,
    is_androidx = True,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    provided_deps = [
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-ui"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        YOGA_TARGET,
        react_native_dep("libraries/fresco/fresco-react-native:fbcore"),
        react_native_dep("libraries/fresco/fresco-react-native:fresco-drawee"),
        react_native_dep("libraries/fresco/fresco-react-native:fresco-react-native"),
        react_native_dep("libraries/fresco/fresco-react-native:imagepipeline"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_target("java/com/facebook/react/modules/fresco:fresco"),
        react_native_target("java/com/facebook/react/uimanager/annotations:annotations"),
        react_native_target("java/com/facebook/react/views/imagehelper:withmultisource"),
    ],
    exported_deps = [
        ":imageevents",
    ],
)
