{"version": 3, "sources": ["../../src/link/addProjectToLibraries.ts"], "names": ["addProjectToLibraries", "libraries", "file", "children", "push", "value", "fileRef", "comment", "basename"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,qBAAT,CACbC,SADa,EAEbC,IAFa,EAGb;AACA,SAAOD,SAAS,CAACE,QAAV,CAAmBC,IAAnB,CAAwB;AAC7BC,IAAAA,KAAK,EAAEH,IAAI,CAACI,OADiB;AAE7BC,IAAAA,OAAO,EAAEL,IAAI,CAACM;AAFe,GAAxB,CAAP;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Given an array of xcodeproj libraries and pbxFile,\n * it appends it to that group\n *\n * Important: That function mutates `libraries` and it's not pure.\n * It's mainly due to limitations of `xcode` library.\n */\nexport default function addProjectToLibraries(\n  libraries: {children: Array<{value: string; comment: string}>},\n  file: {fileRef: string; basename: string},\n) {\n  return libraries.children.push({\n    value: file.fileRef,\n    comment: file.basename,\n  });\n}\n"]}