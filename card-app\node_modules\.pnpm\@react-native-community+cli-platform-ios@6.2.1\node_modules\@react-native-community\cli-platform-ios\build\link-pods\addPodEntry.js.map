{"version": 3, "sources": ["../../src/link-pods/addPodEntry.ts"], "names": ["addPodEntry", "podLines", "linesToAddEntry", "podspecPath", "nodeModulePath", "podName", "newEntry", "Array", "isArray", "map", "line", "indentation", "idx", "logger", "debug", "splice", "getLineToAdd", "spaces", "join"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,WAAT,CACbC,QADa,EAEbC,eAFa,EAObC,WAPa,EAQbC,cARa,EASb;AACA,QAAMC,OAAO,GAAG,6BAAeF,WAAf,CAAhB;AACA,QAAMG,QAAQ,GAAI,QAAOD,OAAQ,gCAA+BD,cAAe,KAA/E;;AAEA,MAAI,CAACF,eAAL,EAAsB;AACpB;AACD;;AAED,MAAIK,KAAK,CAACC,OAAN,CAAcN,eAAd,CAAJ,EAAoC;AAClCA,IAAAA,eAAe,CAACO,GAAhB,CAAoB,CAAC;AAACC,MAAAA,IAAD;AAAOC,MAAAA;AAAP,KAAD,EAAsBC,GAAtB,KAA8B;AAChDC,yBAAOC,KAAP,CAAc,UAAST,OAAQ,eAA/B;;AACAJ,MAAAA,QAAQ,CAACc,MAAT,CAAgBL,IAAI,GAAGE,GAAvB,EAA4B,CAA5B,EAA+BI,YAAY,CAACV,QAAD,EAAWK,WAAX,CAA3C;AACD,KAHD;AAID,GALD,MAKO;AACL,UAAM;AAACD,MAAAA,IAAD;AAAOC,MAAAA;AAAP,QAAsBT,eAA5B;;AACAW,uBAAOC,KAAP,CAAc,UAAST,OAAQ,eAA/B;;AACAJ,IAAAA,QAAQ,CAACc,MAAT,CAAgBL,IAAhB,EAAsB,CAAtB,EAAyBM,YAAY,CAACV,QAAD,EAAWK,WAAX,CAArC;AACD;AACF;;AAED,SAASK,YAAT,CAAsBV,QAAtB,EAAwCK,WAAxC,EAA6D;AAC3D,QAAMM,MAAM,GAAGV,KAAK,CAACI,WAAW,GAAG,CAAf,CAAL,CAAuBO,IAAvB,CAA4B,GAA5B,CAAf;AACA,SAAOD,MAAM,GAAGX,QAAhB;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {logger} from '@react-native-community/cli-tools';\nimport getPodspecName from '../config/getPodspecName';\n\nexport default function addPodEntry(\n  podLines: Array<string>,\n  linesToAddEntry:\n    | Array<{line: number; indentation: number}>\n    | {line: number; indentation: number}\n    | null\n    | undefined,\n  podspecPath: string,\n  nodeModulePath: string,\n) {\n  const podName = getPodspecName(podspecPath);\n  const newEntry = `pod '${podName}', :path => '../node_modules/${nodeModulePath}'\\n`;\n\n  if (!linesToAddEntry) {\n    return;\n  }\n\n  if (Array.isArray(linesToAddEntry)) {\n    linesToAddEntry.map(({line, indentation}, idx) => {\n      logger.debug(`Adding ${podName} to Pod file\"`);\n      podLines.splice(line + idx, 0, getLineToAdd(newEntry, indentation));\n    });\n  } else {\n    const {line, indentation} = linesToAddEntry;\n    logger.debug(`Adding ${podName} to Pod file\"`);\n    podLines.splice(line, 0, getLineToAdd(newEntry, indentation));\n  }\n}\n\nfunction getLineToAdd(newEntry: string, indentation: number) {\n  const spaces = Array(indentation + 1).join(' ');\n  return spaces + newEntry;\n}\n"]}