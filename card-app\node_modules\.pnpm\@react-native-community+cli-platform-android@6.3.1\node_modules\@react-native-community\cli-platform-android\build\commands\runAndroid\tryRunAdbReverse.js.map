{"version": 3, "sources": ["../../../src/commands/runAndroid/tryRunAdbReverse.ts"], "names": ["tryRunAdbReverse", "packagerPort", "device", "adbPath", "adbArgs", "unshift", "logger", "info", "debug", "join", "stdio", "e", "warn", "message"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA,SAASA,gBAAT,CACEC,YADF,EAEEC,MAFF,EAGE;AACA,MAAI;AACF,UAAMC,OAAO,GAAG,0BAAhB;AACA,UAAMC,OAAO,GAAG,CAAC,SAAD,EAAa,OAAMH,YAAa,EAAhC,EAAoC,OAAMA,YAAa,EAAvD,CAAhB,CAFE,CAIF;;AACA,QAAIC,MAAJ,EAAY;AACVE,MAAAA,OAAO,CAACC,OAAR,CAAgB,IAAhB,EAAsBH,MAAtB;AACD;;AAEDI,uBAAOC,IAAP,CAAY,yCAAZ;;AACAD,uBAAOE,KAAP,CAAc,oBAAmBL,OAAQ,IAAGC,OAAO,CAACK,IAAR,CAAa,GAAb,CAAkB,GAA9D;;AAEA,uCAAaN,OAAb,EAAsBC,OAAtB,EAA+B;AAACM,MAAAA,KAAK,EAAE;AAAR,KAA/B;AACD,GAbD,CAaE,OAAOC,CAAP,EAAU;AACVL,uBAAOM,IAAP,CACG,gEAA+DD,CAAC,CAACE,OAAQ,EAD5E;AAGD;AACF;;eAEcb,gB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execFileSync} from 'child_process';\nimport {logger} from '@react-native-community/cli-tools';\nimport getAdbPath from './getAdbPath';\n\n// Runs ADB reverse tcp:8081 tcp:8081 to allow loading the jsbundle from the packager\nfunction tryRunAdbReverse(\n  packagerPort: number | string,\n  device?: string | void,\n) {\n  try {\n    const adbPath = getAdbPath();\n    const adbArgs = ['reverse', `tcp:${packagerPort}`, `tcp:${packagerPort}`];\n\n    // If a device is specified then tell adb to use it\n    if (device) {\n      adbArgs.unshift('-s', device);\n    }\n\n    logger.info('Connecting to the development server...');\n    logger.debug(`Running command \"${adbPath} ${adbArgs.join(' ')}\"`);\n\n    execFileSync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (e) {\n    logger.warn(\n      `Failed to connect to development server using \"adb reverse\": ${e.message}`,\n    );\n  }\n}\n\nexport default tryRunAdbReverse;\n"]}