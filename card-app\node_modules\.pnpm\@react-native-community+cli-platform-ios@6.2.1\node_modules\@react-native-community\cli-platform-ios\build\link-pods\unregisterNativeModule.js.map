{"version": 3, "sources": ["../../src/link-pods/unregisterNativeModule.ts"], "names": ["unregisterNativeModule", "dependencyConfig", "iOSProject", "pod<PERSON>ontent", "fs", "readFileSync", "podfile", "removed", "podspecPath", "logger", "debug", "writeFileSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;;AAUA;AACA;AACA;AACe,SAASA,sBAAT,CACbC,gBADa,EAEbC,UAFa,EAGb;AACA,QAAMC,UAAU,GAAGC,cAAGC,YAAH,CAAgBH,UAAU,CAACI,OAA3B,EAAoC,MAApC,CAAnB;;AACA,QAAMC,OAAO,GAAG,6BAAeJ,UAAf,EAA2BF,gBAAgB,CAACO,WAA5C,CAAhB;;AACAC,qBAAOC,KAAP,CAAc,sBAAqBR,UAAU,CAACI,OAAQ,EAAtD;;AACAF,gBAAGO,aAAH,CAAiBT,UAAU,CAACI,OAA5B,EAAqCC,OAArC;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport removePodEntry from './removePodEntry';\nimport {logger} from '@react-native-community/cli-tools';\nimport {\n  IOSDependencyConfig,\n  IOSProjectConfig,\n} from '@react-native-community/cli-types';\n\n/**\n * Unregister native module IOS with CocoaPods\n */\nexport default function unregisterNativeModule(\n  dependencyConfig: IOSDependencyConfig,\n  iOSProject: IOSProjectConfig,\n) {\n  const podContent = fs.readFileSync(iOSProject.podfile, 'utf8');\n  const removed = removePodEntry(podContent, dependencyConfig.podspecPath);\n  logger.debug(`Writing changes to ${iOSProject.podfile}`);\n  fs.writeFileSync(iOSProject.podfile, removed);\n}\n"]}