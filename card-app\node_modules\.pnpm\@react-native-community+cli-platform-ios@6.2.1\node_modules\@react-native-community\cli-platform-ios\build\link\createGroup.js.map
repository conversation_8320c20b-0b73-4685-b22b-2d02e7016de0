{"version": 3, "sources": ["../../src/link/createGroup.ts"], "names": ["hasGroup", "pbxGroup", "name", "children", "find", "group", "comment", "createGroup", "project", "path", "split", "reduce", "uuid", "pbxCreateGroup", "push", "value", "pbxGroupByName"], "mappings": ";;;;;;;AAQA;;;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA,MAAMA,QAAQ,GAAG,CACfC,QADe,EAEfC,IAFe,KAGZD,QAAQ,CAACE,QAAT,CAAkBC,IAAlB,CAAwBC,KAAD,IAAWA,KAAK,CAACC,OAAN,KAAkBJ,IAApD,CAHL;AAKA;AACA;AACA;AACA;AACA;AACA;;;AACe,SAASK,WAAT,CAAqBC,OAArB,EAAmCC,IAAnC,EAAiD;AAC9D,SAAOA,IAAI,CAACC,KAAL,CAAW,GAAX,EAAgBC,MAAhB,CAAuB,CAACN,KAAD,EAAQH,IAAR,KAAiB;AAC7C,QAAI,CAACF,QAAQ,CAACK,KAAD,EAAQH,IAAR,CAAb,EAA4B;AAC1B,YAAMU,IAAI,GAAGJ,OAAO,CAACK,cAAR,CAAuBX,IAAvB,EAA6B,IAA7B,CAAb;AAEAG,MAAAA,KAAK,CAACF,QAAN,CAAeW,IAAf,CAAoB;AAClBC,QAAAA,KAAK,EAAEH,IADW;AAElBN,QAAAA,OAAO,EAAEJ;AAFS,OAApB;AAID;;AAED,WAAOM,OAAO,CAACQ,cAAR,CAAuBd,IAAvB,CAAP;AACD,GAXM,EAWJ,uBAASM,OAAT,CAXI,CAAP;AAYD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport getGroup from './getGroup';\n\nconst hasGroup = (\n  pbxGroup: {children: Array<{comment: string}>},\n  name: string,\n) => pbxGroup.children.find((group) => group.comment === name);\n\n/**\n * Given project and path of the group, it deeply creates a given group\n * making all outer groups if necessary\n *\n * Returns newly created group\n */\nexport default function createGroup(project: any, path: string) {\n  return path.split('/').reduce((group, name) => {\n    if (!hasGroup(group, name)) {\n      const uuid = project.pbxCreateGroup(name, '\"\"');\n\n      group.children.push({\n        value: uuid,\n        comment: name,\n      });\n    }\n\n    return project.pbxGroupByName(name);\n  }, getGroup(project));\n}\n"]}