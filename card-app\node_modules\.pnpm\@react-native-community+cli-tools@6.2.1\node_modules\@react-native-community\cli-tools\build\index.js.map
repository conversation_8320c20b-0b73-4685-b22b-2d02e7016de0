{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA", "sourcesContent": ["export {default as logger} from './logger';\nexport {default as groupFilesByType} from './groupFilesByType';\nexport {default as isPackagerRunning} from './isPackagerRunning';\nexport {default as getDefaultUserTerminal} from './getDefaultUserTerminal';\nexport {fetch, fetchToTemp} from './fetch';\nexport {default as launchDefaultBrowser} from './launchDefaultBrowser';\nexport {default as launchDebugger} from './launchDebugger';\nexport {default as launchEditor} from './launchEditor';\nexport {default as releaseChecker} from './releaseChecker';\nexport {default as resolveNodeModuleDir} from './resolveNodeModuleDir';\nexport {default as hookStdout} from './hookStdout';\n\nexport * from './errors';\n"]}