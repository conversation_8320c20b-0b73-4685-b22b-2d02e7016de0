#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/image-size@0.6.3/node_modules/image-size/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/image-size@0.6.3/node_modules/image-size/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/image-size@0.6.3/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/image-size@0.6.3/node_modules/image-size/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/image-size@0.6.3/node_modules/image-size/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/image-size@0.6.3/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/image-size.js" "$@"
else
  exec node  "$basedir/../../bin/image-size.js" "$@"
fi
