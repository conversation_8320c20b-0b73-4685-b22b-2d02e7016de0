{"version": 3, "sources": ["../src/launchDebugger.ts"], "names": ["launchDebugger", "url"], "mappings": ";;;;;;;AASA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA,eAAeA,cAAf,CAA8BC,GAA9B,EAA2C;AACzC,SAAO,mCAAqBA,GAArB,CAAP;AACD;;eAEcD,c", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport launchDefaultBrowser from './launchDefaultBrowser';\n\nasync function launchDebugger(url: string) {\n  return launchDefaultBrowser(url);\n}\n\nexport default launchDebugger;\n"]}