load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_root_target", "react_native_target", "rn_android_library")

rn_android_library(
    name = "switchview",
    srcs = glob(["*.java"]),
    autoglob = False,
    is_androidx = True,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    provided_deps = [
        react_native_dep("third-party/android/androidx:appcompat"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-ui"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        YOGA_TARGET,
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_target("java/com/facebook/react/uimanager/annotations:annotations"),
        react_native_root_target(":generated_components_java-FBReactNativeComponentSpec"),
    ],
)
