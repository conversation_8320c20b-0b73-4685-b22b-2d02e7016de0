/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { IOSDependencyConfig, IOSProjectConfig } from '@react-native-community/cli-types';
export default function unregisterNativeModule(_name: string, dependencyConfig: IOSDependencyConfig, projectConfig: IOSProjectConfig, otherDependencies: Array<any>): void;
//# sourceMappingURL=unregisterNativeModule.d.ts.map