"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = removeSharedLibraries;

/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
function removeSharedLibraries(project, libraries) {
  if (!libraries.length) {
    return;
  }

  const target = project.getFirstTarget().uuid;

  for (const name of libraries) {
    project.removeFramework(name, {
      target
    });
  }
}

//# sourceMappingURL=removeSharedLibraries.js.map