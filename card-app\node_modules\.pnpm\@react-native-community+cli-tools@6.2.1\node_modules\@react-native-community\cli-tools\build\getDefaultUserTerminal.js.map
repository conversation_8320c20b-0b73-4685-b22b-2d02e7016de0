{"version": 3, "sources": ["../src/getDefaultUserTerminal.ts"], "names": ["getDefaultUserTerminal", "REACT_TERMINAL", "TERM_PROGRAM", "TERM", "process", "env", "os", "platform"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEA,MAAMA,sBAAsB,GAAG,MAA0B;AACvD,QAAM;AAACC,IAAAA,cAAD;AAAiBC,IAAAA,YAAjB;AAA+BC,IAAAA;AAA/B,MAAuCC,OAAO,CAACC,GAArD;;AAEA,MAAIJ,cAAJ,EAAoB;AAClB,WAAOA,cAAP;AACD;;AAED,MAAIK,cAAGC,QAAH,OAAkB,QAAtB,EAAgC;AAC9B,WAAOL,YAAP;AACD;;AAED,SAAOC,IAAP;AACD,CAZD;;eAceH,sB", "sourcesContent": ["import os from 'os';\n\nconst getDefaultUserTerminal = (): string | undefined => {\n  const {REACT_TERMINAL, TERM_PROGRAM, TERM} = process.env;\n\n  if (REACT_TERMINAL) {\n    return REACT_TERMINAL;\n  }\n\n  if (os.platform() === 'darwin') {\n    return TERM_PROGRAM;\n  }\n\n  return TERM;\n};\n\nexport default getDefaultUserTerminal;\n"]}