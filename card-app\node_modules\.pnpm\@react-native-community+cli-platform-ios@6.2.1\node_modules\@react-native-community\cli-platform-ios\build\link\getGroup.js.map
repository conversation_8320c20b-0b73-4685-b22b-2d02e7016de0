{"version": 3, "sources": ["../../src/link/getGroup.ts"], "names": ["getFirstProject", "project", "firstProject", "findGroup", "groups", "name", "children", "find", "group", "comment", "getGroup", "path", "getPBXGroupByKey", "mainGroup", "split", "foundGroup", "value"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMA,eAAe,GAAIC,OAAD,IACtBA,OAAO,CAACD,eAAR,GAA0BE,YAD5B;;AAGA,MAAMC,SAAS,GAAG,CAChBC,MADgB,EAEhBC,IAFgB,KAGbD,MAAM,CAACE,QAAP,CAAgBC,IAAhB,CAAsBC,KAAD,IAAWA,KAAK,CAACC,OAAN,KAAkBJ,IAAlD,CAHL;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACe,SAASK,QAAT,CAAkBT,OAAlB,EAAgCU,IAAhC,EAA+C;AAC5D,QAAMT,YAAY,GAAGF,eAAe,CAACC,OAAD,CAApC;AAEA,MAAIG,MAAM,GAAGH,OAAO,CAACW,gBAAR,CAAyBV,YAAY,CAACW,SAAtC,CAAb;;AAEA,MAAI,CAACF,IAAL,EAAW;AACT,WAAOP,MAAP;AACD;;AAED,OAAK,MAAMC,IAAX,IAAmBM,IAAI,CAACG,KAAL,CAAW,GAAX,CAAnB,EAAoC;AAClC,UAAMC,UAAU,GAAGZ,SAAS,CAACC,MAAD,EAASC,IAAT,CAA5B;;AAEA,QAAIU,UAAJ,EAAgB;AACdX,MAAAA,MAAM,GAAGH,OAAO,CAACW,gBAAR,CAAyBG,UAAU,CAACC,KAApC,CAAT;AACD,KAFD,MAEO;AACLZ,MAAAA,MAAM,GAAG,IAAT;AACA;AACD;AACF;;AAED,SAAOA,MAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst getFirstProject = (project: any) =>\n  project.getFirstProject().firstProject;\n\nconst findGroup = (\n  groups: {children: Array<{comment: string; value: string}>},\n  name: string,\n) => groups.children.find((group) => group.comment === name);\n\n/**\n * Returns group from .xcodeproj if one exists, null otherwise\n *\n * Unlike node-xcode `pbxGroupByName` - it does not return `first-matching`\n * group if multiple groups with the same name exist\n *\n * If path is not provided, it returns top-level group\n */\nexport default function getGroup(project: any, path?: string) {\n  const firstProject = getFirstProject(project);\n\n  let groups = project.getPBXGroupByKey(firstProject.mainGroup);\n\n  if (!path) {\n    return groups;\n  }\n\n  for (const name of path.split('/')) {\n    const foundGroup = findGroup(groups, name);\n\n    if (foundGroup) {\n      groups = project.getPBXGroupByKey(foundGroup.value);\n    } else {\n      groups = null;\n      break;\n    }\n  }\n\n  return groups;\n}\n"]}