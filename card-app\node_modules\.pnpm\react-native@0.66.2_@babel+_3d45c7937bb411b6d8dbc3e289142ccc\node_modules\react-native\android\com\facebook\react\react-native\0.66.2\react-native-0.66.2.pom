<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.facebook.react</groupId>
  <artifactId>react-native</artifactId>
  <version>0.66.2</version>
  <packaging>aar</packaging>
  <name>ReactNative</name>
  <description>A framework for building native apps with React</description>
  <url>https://github.com/facebook/react-native</url>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://github.com/facebook/react-native/blob/HEAD/LICENSE</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>facebook</id>
      <name>Facebook</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/facebook/react-native.git</connection>
    <developerConnection>scm:git:**************:facebook/react-native.git</developerConnection>
    <url>https://github.com/facebook/react-native.git</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.facebook.infer.annotation</groupId>
      <artifactId>infer-annotation</artifactId>
      <version>0.18.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.yoga</groupId>
      <artifactId>proguard-annotations</artifactId>
      <version>1.19.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.appcompat</groupId>
      <artifactId>appcompat</artifactId>
      <version>1.0.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.autofill</groupId>
      <artifactId>autofill</artifactId>
      <version>1.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.swiperefreshlayout</groupId>
      <artifactId>swiperefreshlayout</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.fresco</groupId>
      <artifactId>fresco</artifactId>
      <version>2.5.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.fresco</groupId>
      <artifactId>imagepipeline-okhttp3</artifactId>
      <version>2.5.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.fresco</groupId>
      <artifactId>ui-common</artifactId>
      <version>2.5.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.soloader</groupId>
      <artifactId>soloader</artifactId>
      <version>0.10.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.9.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp-urlconnection</artifactId>
      <version>4.9.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okio</groupId>
      <artifactId>okio</artifactId>
      <version>2.9.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.fbjni</groupId>
      <artifactId>fbjni-java-only</artifactId>
      <version>0.2.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
