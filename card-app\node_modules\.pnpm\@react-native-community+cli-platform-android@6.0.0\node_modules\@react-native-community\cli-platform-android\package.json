{"name": "@react-native-community/cli-platform-android", "version": "6.0.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-tools": "^6.0.0-rc.0", "chalk": "^3.0.0", "execa": "^1.0.0", "fs-extra": "^8.1.0", "glob": "^7.1.3", "jetifier": "^1.6.2", "lodash": "^4.17.15", "logkitty": "^0.7.1", "slash": "^3.0.0", "xmldoc": "^1.1.2"}, "files": ["build", "!*.d.ts", "!*.map", "native_modules.gradle"], "devDependencies": {"@react-native-community/cli-types": "^6.0.0", "@types/execa": "^0.9.0", "@types/fs-extra": "^8.1.0", "@types/glob": "^7.1.1", "@types/lodash": "^4.14.149", "@types/xmldoc": "^1.1.4"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/platform-android", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/platform-android"}, "gitHead": "47aed081b4cbea02d4cc7f12d4fec732ee85b2a4"}