﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Resources">
      <UniqueIdentifier>accd3aa8-1ba0-4223-9bbe-0c431709210b</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tga;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{926ab91d-31b5-48c3-b9a4-e681349f27f0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\ReactNativeCameraCPP\pch.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="..\ReactNativeCameraCPP\ReactPackageProvider.cpp" />
    <ClCompile Include="..\ReactNativeCameraCPP\ReactCameraViewManager.cpp" />
    <ClCompile Include="..\ReactNativeCameraCPP\ReactCameraView.cpp" />
    <ClCompile Include="..\ReactNativeCameraCPP\CameraRotationHelper.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\ReactNativeCameraCPP\pch.h" />
    <ClInclude Include="..\ReactNativeCameraCPP\ReactPackageProvider.h" />
    <ClInclude Include="..\ReactNativeCameraCPP\ReactCameraModule.h" />
    <ClInclude Include="..\ReactNativeCameraCPP\ReactCameraViewManager.h" />
    <ClInclude Include="..\ReactNativeCameraCPP\ReactCameraConstants.h" />
    <ClInclude Include="..\ReactNativeCameraCPP\ReactCameraView.h" />
    <ClInclude Include="..\ReactNativeCameraCPP\CameraRotationHelper.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\ReactNativeCameraCPP\ReactNativeCameraCPP.def" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="PropertySheet.props" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="..\ReactNativeCameraCPP\ReactPackageProvider.idl" />
    <Midl Include="..\ReactNativeCameraCPP\CameraRotationHelper.idl" />
  </ItemGroup>
</Project>