@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Sites\card-all\card-app\node_modules\.pnpm\react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc\node_modules\react-native\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Sites\card-all\card-app\node_modules\.pnpm\react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc\node_modules\react-native\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc\node_modules\react-native\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\react-native@0.66.2_@babel+_3d45c7937bb411b6d8dbc3e289142ccc\node_modules\react-native\cli.js" %*
)
