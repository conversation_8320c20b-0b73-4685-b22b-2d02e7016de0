{"version": 3, "sources": ["../../src/link-pods/isInstalled.ts"], "names": ["isInstalled", "iOSProject", "dependencyConfig", "podfile", "podspecPath", "dependencyRegExp", "RegExp", "podLines", "i", "len", "length", "match"], "mappings": ";;;;;;;AAQA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AASe,SAASA,WAAT,CACbC,UADa,EAEbC,gBAFa,EAGb;AACA,MAAI,CAACD,UAAU,CAACE,OAAZ,IAAuB,CAACD,gBAAgB,CAACE,WAA7C,EAA0D;AACxD,WAAO,KAAP;AACD,GAHD,CAIA;;;AACA,QAAMC,gBAAgB,GAAG,IAAIC,MAAJ,CACtB,eAAc,6BAAeJ,gBAAgB,CAACE,WAAhC,CAA6C,OADrC,EAEvB,GAFuB,CAAzB;AAIA,QAAMG,QAAQ,GAAG,0BAAYN,UAAU,CAACE,OAAvB,CAAjB;;AACA,OAAK,IAAIK,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,QAAQ,CAACG,MAA/B,EAAuCF,CAAC,GAAGC,GAA3C,EAAgDD,CAAC,EAAjD,EAAqD;AACnD,UAAMG,KAAK,GAAGJ,QAAQ,CAACC,CAAD,CAAR,CAAYG,KAAZ,CAAkBN,gBAAlB,CAAd;;AACA,QAAIM,KAAJ,EAAW;AACT,aAAO,IAAP;AACD;AACF;;AACD,SAAO,KAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport readPodfile from './readPodfile';\nimport getPodspecName from '../config/getPodspecName';\nimport {\n  IOSProjectConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\n\nexport default function isInstalled(\n  iOSProject: IOSProjectConfig,\n  dependencyConfig: IOSDependencyConfig,\n) {\n  if (!iOSProject.podfile || !dependencyConfig.podspecPath) {\n    return false;\n  }\n  // match line with pod declaration: pod 'dependencyPodName' (other possible parameters of pod are ignored)\n  const dependencyRegExp = new RegExp(\n    `pod\\\\s+('|\")${getPodspecName(dependencyConfig.podspecPath)}('|\")`,\n    'g',\n  );\n  const podLines = readPodfile(iOSProject.podfile);\n  for (let i = 0, len = podLines.length; i < len; i++) {\n    const match = podLines[i].match(dependencyRegExp);\n    if (match) {\n      return true;\n    }\n  }\n  return false;\n}\n"]}