{"version": 3, "names": ["getOS", "os", "platform", "_platform", "_version", "doclink", "section", "path", "hashOrOverrides", "url", "URL", "isObj", "hash", "version", "OS", "pathname", "searchParams", "set", "assert", "ok", "plat", "otherKeys", "Object", "keys", "filter", "key", "includes", "doesNotMatch", "toString", "docs", "bind", "contributing", "community", "showcase", "blog", "setPlatform", "target", "setVersion", "reactNativeVersion"], "sources": ["../src/doclink.ts"], "sourcesContent": ["import os from 'os';\nimport assert from 'assert';\n\ntype Platforms =\n  | 'android'\n  | 'ios'\n  | 'inherit' // Expect the platform to be defined before this link is called with the link.setPlatform()\n  | 'none'; // No platform specific documentation\n\nexport function getOS(): string {\n  // Using os.platform instead of process.platform so we can test more easily. Once jest upgrades\n  // to ^29.4 we could use process.platforms and jest.replaceProperty(process, 'platforms', 'someplatform');\n  switch (os.platform()) {\n    case 'aix':\n    case 'freebsd':\n    case 'linux':\n    case 'openbsd':\n    case 'sunos':\n      // King of controversy, right here.\n      return 'linux';\n    case 'darwin':\n      return 'macos';\n    case 'win32':\n      return 'windows';\n    default:\n      return '';\n  }\n}\n\nlet _platform: Platforms | null = null;\nlet _version: string | undefined;\n\ninterface Overrides {\n  os?: string;\n  hash?: string;\n  version?: string;\n}\n\ninterface Other {\n  [key: string]: string;\n}\n\n/**\n * Create a deeplink to our documentation based on the user's OS and the Platform they're trying to build.\n */\nfunction doclink(\n  section: string,\n  path: string,\n  platform: Platforms,\n  hashOrOverrides?: string | (Overrides & Other),\n): string {\n  const url = new URL('https://reactnative.dev/');\n\n  // Overrides\n  const isObj = typeof hashOrOverrides === 'object';\n\n  const hash = isObj ? hashOrOverrides.hash : hashOrOverrides;\n  const version =\n    isObj && hashOrOverrides.version ? hashOrOverrides.version : _version;\n  const OS = isObj && hashOrOverrides.os ? hashOrOverrides.os : getOS();\n\n  url.pathname = _version\n    ? `${section}/${version}/${path}`\n    : `${section}/${path}`;\n\n  url.searchParams.set('os', OS);\n\n  if (platform === 'inherit') {\n    assert.ok(\n      _platform !== null,\n      `Please report this CLI error:  link.setPlatform('ios'|'android'|'none') was expected to be set before using link.${section}(${path}, 'inherit').`,\n    );\n  }\n\n  const plat: Platforms =\n    platform === 'inherit' ? (_platform as Platforms) : platform ?? _platform;\n\n  if (plat !== 'none') {\n    url.searchParams.set('platform', plat);\n  }\n\n  if (isObj) {\n    const otherKeys = Object.keys(hashOrOverrides).filter(\n      (key) => !['hash', 'version', 'os'].includes(key),\n    );\n    for (let key of otherKeys) {\n      url.searchParams.set(key, hashOrOverrides[key]);\n    }\n  }\n\n  if (hash) {\n    assert.doesNotMatch(\n      hash,\n      /#/,\n      \"Anchor links should be written without a '#'\",\n    );\n    url.hash = hash;\n  }\n\n  return url.toString();\n}\n\nexport const docs = doclink.bind(null, 'docs');\nexport const contributing = doclink.bind(null, 'contributing');\nexport const community = doclink.bind(null, 'community');\nexport const showcase = doclink.bind(null, 'showcase');\nexport const blog = doclink.bind(null, 'blog');\n\n/**\n * When the user builds, we should define the target platform globally.\n */\nexport function setPlatform(target: Platforms): void {\n  _platform = target;\n}\n\n/**\n * Can we figure out what version of react native they're using?\n */\nexport function setVersion(reactNativeVersion: string): void {\n  _version = reactNativeVersion;\n}\n"], "mappings": ";;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA4B;AAMhB;;AAEL,SAASA,KAAK,GAAW;EAC9B;EACA;EACA,QAAQC,aAAE,CAACC,QAAQ,EAAE;IACnB,KAAK,KAAK;IACV,KAAK,SAAS;IACd,KAAK,OAAO;IACZ,KAAK,SAAS;IACd,KAAK,OAAO;MACV;MACA,OAAO,OAAO;IAChB,KAAK,QAAQ;MACX,OAAO,OAAO;IAChB,KAAK,OAAO;MACV,OAAO,SAAS;IAClB;MACE,OAAO,EAAE;EAAC;AAEhB;AAEA,IAAIC,SAA2B,GAAG,IAAI;AACtC,IAAIC,QAA4B;AAYhC;AACA;AACA;AACA,SAASC,OAAO,CACdC,OAAe,EACfC,IAAY,EACZL,QAAmB,EACnBM,eAA8C,EACtC;EACR,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,0BAA0B,CAAC;;EAE/C;EACA,MAAMC,KAAK,GAAG,OAAOH,eAAe,KAAK,QAAQ;EAEjD,MAAMI,IAAI,GAAGD,KAAK,GAAGH,eAAe,CAACI,IAAI,GAAGJ,eAAe;EAC3D,MAAMK,OAAO,GACXF,KAAK,IAAIH,eAAe,CAACK,OAAO,GAAGL,eAAe,CAACK,OAAO,GAAGT,QAAQ;EACvE,MAAMU,EAAE,GAAGH,KAAK,IAAIH,eAAe,CAACP,EAAE,GAAGO,eAAe,CAACP,EAAE,GAAGD,KAAK,EAAE;EAErES,GAAG,CAACM,QAAQ,GAAGX,QAAQ,GAClB,GAAEE,OAAQ,IAAGO,OAAQ,IAAGN,IAAK,EAAC,GAC9B,GAAED,OAAQ,IAAGC,IAAK,EAAC;EAExBE,GAAG,CAACO,YAAY,CAACC,GAAG,CAAC,IAAI,EAAEH,EAAE,CAAC;EAE9B,IAAIZ,QAAQ,KAAK,SAAS,EAAE;IAC1BgB,iBAAM,CAACC,EAAE,CACPhB,SAAS,KAAK,IAAI,EACjB,oHAAmHG,OAAQ,IAAGC,IAAK,eAAc,CACnJ;EACH;EAEA,MAAMa,IAAe,GACnBlB,QAAQ,KAAK,SAAS,GAAIC,SAAS,GAAiBD,QAAQ,IAAIC,SAAS;EAE3E,IAAIiB,IAAI,KAAK,MAAM,EAAE;IACnBX,GAAG,CAACO,YAAY,CAACC,GAAG,CAAC,UAAU,EAAEG,IAAI,CAAC;EACxC;EAEA,IAAIT,KAAK,EAAE;IACT,MAAMU,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACf,eAAe,CAAC,CAACgB,MAAM,CAClDC,GAAG,IAAK,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAClD;IACD,KAAK,IAAIA,GAAG,IAAIJ,SAAS,EAAE;MACzBZ,GAAG,CAACO,YAAY,CAACC,GAAG,CAACQ,GAAG,EAAEjB,eAAe,CAACiB,GAAG,CAAC,CAAC;IACjD;EACF;EAEA,IAAIb,IAAI,EAAE;IACRM,iBAAM,CAACS,YAAY,CACjBf,IAAI,EACJ,GAAG,EACH,8CAA8C,CAC/C;IACDH,GAAG,CAACG,IAAI,GAAGA,IAAI;EACjB;EAEA,OAAOH,GAAG,CAACmB,QAAQ,EAAE;AACvB;AAEO,MAAMC,IAAI,GAAGxB,OAAO,CAACyB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;AAAC;AACxC,MAAMC,YAAY,GAAG1B,OAAO,CAACyB,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC;AAAC;AACxD,MAAME,SAAS,GAAG3B,OAAO,CAACyB,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC;AAAC;AAClD,MAAMG,QAAQ,GAAG5B,OAAO,CAACyB,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;AAAC;AAChD,MAAMI,IAAI,GAAG7B,OAAO,CAACyB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;;AAE9C;AACA;AACA;AAFA;AAGO,SAASK,WAAW,CAACC,MAAiB,EAAQ;EACnDjC,SAAS,GAAGiC,MAAM;AACpB;;AAEA;AACA;AACA;AACO,SAASC,UAAU,CAACC,kBAA0B,EAAQ;EAC3DlC,QAAQ,GAAGkC,kBAAkB;AAC/B"}