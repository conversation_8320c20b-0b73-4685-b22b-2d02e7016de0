{"version": 3, "names": ["getMainActivityFiles", "folder", "includePackage", "patternArray", "push", "glob", "sync", "join", "cwd", "unixifyPaths", "getPackageClassName", "files", "packages", "getClassNameMatches", "length", "Array", "isArray", "fs", "existsSync", "path", "map", "filePath", "readFileSync", "matchClassName", "filter", "match", "file", "nativeModuleMatch"], "sources": ["../../src/config/findPackageClassName.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport glob from 'fast-glob';\nimport path from 'path';\nimport {unixifyPaths} from '@react-native-community/cli-tools';\n\nexport function getMainActivityFiles(\n  folder: string,\n  includePackage: boolean = true,\n) {\n  let patternArray = [];\n\n  if (includePackage) {\n    patternArray.push('*Package.java', '*Package.kt');\n  } else {\n    patternArray.push('*.java', '*.kt');\n  }\n\n  return glob.sync(`**/+(${patternArray.join('|')})`, {\n    cwd: unixifyPaths(folder),\n  });\n}\n\nexport default function getPackageClassName(folder: string) {\n  let files = getMainActivityFiles(folder);\n  let packages = getClassNameMatches(files, folder);\n\n  if (packages && packages.length > 0 && Array.isArray(packages[0])) {\n    return packages[0][1];\n  }\n\n  /*\n    When module contains `expo-module.config.json` we return null\n    because expo modules follow other practices and don't implement\n    ReactPackage/TurboReactPackage directly, so it doesn't make sense\n    to scan and read hundreds of files to get package class name.\n\n    Exception is `expo` package itself which contains `expo-module.config.json`\n    and implements `ReactPackage/TurboReactPackage`.\n\n    Following logic is done due to performance optimization.\n  */\n\n  if (fs.existsSync(path.join(folder, '..', 'expo-module.config.json'))) {\n    return null;\n  }\n\n  files = getMainActivityFiles(folder, false);\n  packages = getClassNameMatches(files, folder);\n\n  // @ts-ignore\n  return packages.length ? packages[0][1] : null;\n}\n\nfunction getClassNameMatches(files: string[], folder: string) {\n  return files\n    .map((filePath) => fs.readFileSync(path.join(folder, filePath), 'utf8'))\n    .map(matchClassName)\n    .filter((match) => match);\n}\n\nexport function matchClassName(file: string) {\n  const nativeModuleMatch = file.match(\n    /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+implements\\s+|:)[\\s\\w():,]*[^{]*ReactPackage/,\n  );\n  // We first check for implementation of ReactPackage to find native\n  // modules and then for subclasses of TurboReactPackage to find turbo modules.\n  if (nativeModuleMatch) {\n    return nativeModuleMatch;\n  } else {\n    return file.match(\n      /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+extends\\s+|:)[\\s\\w():,]*[^{]*(Turbo|Base)ReactPackage/,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+D;AAX/D;AACA;AACA;AACA;AACA;AACA;AACA;;AAOO,SAASA,oBAAoB,CAClCC,MAAc,EACdC,cAAuB,GAAG,IAAI,EAC9B;EACA,IAAIC,YAAY,GAAG,EAAE;EAErB,IAAID,cAAc,EAAE;IAClBC,YAAY,CAACC,IAAI,CAAC,eAAe,EAAE,aAAa,CAAC;EACnD,CAAC,MAAM;IACLD,YAAY,CAACC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;EACrC;EAEA,OAAOC,mBAAI,CAACC,IAAI,CAAE,QAAOH,YAAY,CAACI,IAAI,CAAC,GAAG,CAAE,GAAE,EAAE;IAClDC,GAAG,EAAE,IAAAC,wBAAY,EAACR,MAAM;EAC1B,CAAC,CAAC;AACJ;AAEe,SAASS,mBAAmB,CAACT,MAAc,EAAE;EAC1D,IAAIU,KAAK,GAAGX,oBAAoB,CAACC,MAAM,CAAC;EACxC,IAAIW,QAAQ,GAAGC,mBAAmB,CAACF,KAAK,EAAEV,MAAM,CAAC;EAEjD,IAAIW,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;IACjE,OAAOA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAIE,IAAIK,aAAE,CAACC,UAAU,CAACC,eAAI,CAACZ,IAAI,CAACN,MAAM,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAAC,EAAE;IACrE,OAAO,IAAI;EACb;EAEAU,KAAK,GAAGX,oBAAoB,CAACC,MAAM,EAAE,KAAK,CAAC;EAC3CW,QAAQ,GAAGC,mBAAmB,CAACF,KAAK,EAAEV,MAAM,CAAC;;EAE7C;EACA,OAAOW,QAAQ,CAACE,MAAM,GAAGF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AAChD;AAEA,SAASC,mBAAmB,CAACF,KAAe,EAAEV,MAAc,EAAE;EAC5D,OAAOU,KAAK,CACTS,GAAG,CAAEC,QAAQ,IAAKJ,aAAE,CAACK,YAAY,CAACH,eAAI,CAACZ,IAAI,CAACN,MAAM,EAAEoB,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,CACvED,GAAG,CAACG,cAAc,CAAC,CACnBC,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAAC;AAC7B;AAEO,SAASF,cAAc,CAACG,IAAY,EAAE;EAC3C,MAAMC,iBAAiB,GAAGD,IAAI,CAACD,KAAK,CAClC,gFAAgF,CACjF;EACD;EACA;EACA,IAAIE,iBAAiB,EAAE;IACrB,OAAOA,iBAAiB;EAC1B,CAAC,MAAM;IACL,OAAOD,IAAI,CAACD,KAAK,CACf,yFAAyF,CAC1F;EACH;AACF"}