{"version": 3, "names": ["getTaskNames", "appName", "mode", "tasks", "taskPrefix", "appTasks", "length", "toPascalCase", "map", "command"], "sources": ["../../../src/commands/runAndroid/getTaskNames.ts"], "sourcesContent": ["import {toPascalCase} from './toPascalCase';\nimport type {BuildFlags} from '../buildAndroid';\n\nexport function getTaskNames(\n  appName: string,\n  mode: BuildFlags['mode'] = 'debug',\n  tasks: BuildFlags['tasks'],\n  taskPrefix: 'assemble' | 'install' | 'bundle',\n): Array<string> {\n  const appTasks =\n    tasks && tasks.length ? tasks : [taskPrefix + toPascalCase(mode)];\n\n  return appName\n    ? appTasks.map((command) => `${appName}:${command}`)\n    : appTasks;\n}\n"], "mappings": ";;;;;;AAAA;AAGO,SAASA,YAAY,CAC1BC,OAAe,EACfC,IAAwB,GAAG,OAAO,EAClCC,KAA0B,EAC1BC,UAA6C,EAC9B;EACf,MAAMC,QAAQ,GACZF,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAGH,KAAK,GAAG,CAACC,UAAU,GAAG,IAAAG,0BAAY,EAACL,IAAI,CAAC,CAAC;EAEnE,OAAOD,OAAO,GACVI,QAAQ,CAACG,GAAG,CAAEC,OAAO,IAAM,GAAER,OAAQ,IAAGQ,OAAQ,EAAC,CAAC,GAClDJ,QAAQ;AACd"}