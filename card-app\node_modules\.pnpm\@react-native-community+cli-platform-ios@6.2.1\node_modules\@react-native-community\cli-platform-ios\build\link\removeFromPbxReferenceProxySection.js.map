{"version": 3, "sources": ["../../src/link/removeFromPbxReferenceProxySection.ts"], "names": ["removeFromPbxReferenceProxySection", "project", "file", "section", "hash", "objects", "PBXReferenceProxy", "key", "Object", "keys", "path", "basename"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACe,SAASA,kCAAT,CACbC,OADa,EAEbC,IAFa,EAGb;AACA,QAAMC,OAAO,GAAGF,OAAO,CAACG,IAAR,CAAaH,OAAb,CAAqBI,OAArB,CAA6BC,iBAA7C;;AAEA,OAAK,MAAMC,GAAX,IAAkBC,MAAM,CAACC,IAAP,CAAYN,OAAZ,CAAlB,EAAwC;AACtC,QAAIA,OAAO,CAACI,GAAD,CAAP,CAAaG,IAAb,KAAsBR,IAAI,CAACS,QAA/B,EAAyC;AACvC,aAAOR,OAAO,CAACI,GAAD,CAAd;AACD;AACF;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Every file added to the project from another project is attached to\n * `PBXItemContainerProxy` through `PBXReferenceProxy`.\n */\nexport default function removeFromPbxReferenceProxySection(\n  project: any,\n  file: any,\n) {\n  const section = project.hash.project.objects.PBXReferenceProxy;\n\n  for (const key of Object.keys(section)) {\n    if (section[key].path === file.basename) {\n      delete section[key];\n    }\n  }\n}\n"]}