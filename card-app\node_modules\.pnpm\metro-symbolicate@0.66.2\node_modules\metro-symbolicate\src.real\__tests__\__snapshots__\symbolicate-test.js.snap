// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`coverage option symbolicating a CJS stack trace ignores the module id of the file name 1`] = `
"/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js:13:getValue
"
`;

exports[`coverage option symbolicating a CJS stack trace ignores the segment id of the file name 1`] = `
"/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js:13:getValue
"
`;

exports[`coverage option symbolicating a coverage stack trace CJS 1`] = `
Array [
  Object {
    "column": 2,
    "functionName": "<global>",
    "line": 16,
    "name": null,
    "source": "/js/react-native-github/Libraries/Utilities/createPerformanceLogger.js",
  },
  Object {
    "column": 2,
    "functionName": "<global>",
    "line": 25,
    "name": null,
    "source": "/js/RKJSModules/Libraries/MobileConfig/MobileConfig.js",
  },
  Object {
    "column": 0,
    "functionName": "<global>",
    "line": 8,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
]
`;

exports[`coverage option symbolicating a coverage stack trace Classic 1`] = `
Array [
  Object {
    "column": 9,
    "functionName": "<anonymous>",
    "line": 162,
    "name": "param",
    "source": "/js/RKJSModules/Libraries/MobileConfig/MobileConfig.js",
  },
  Object {
    "column": 21,
    "functionName": "<anonymous>",
    "line": 100,
    "name": null,
    "source": "/js/RKJSModules/Libraries/MobileConfig/MobileConfig.js",
  },
  Object {
    "column": 15,
    "functionName": "getValue",
    "line": 10,
    "name": "getValue",
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
]
`;

exports[`directory context does not read source maps outside the root dir, with relative or absolute paths 1`] = `
"<testDir>/__fixtures__/testfile.js:1:null
../testfile.js:1:null
"
`;

exports[`directory context symbolicating a stack trace 1`] = `
"/js/react-native-github/Libraries/BatchedBridge/BatchedBridge.js:23:Object
/js/react-native-github/Libraries/BatchedBridge/MessageQueue.js:89:null
fileThatDoesntExist.js:10:null
"
`;

exports[`directory context symbolicating a stack trace with absolute paths 1`] = `
"/js/react-native-github/Libraries/BatchedBridge/BatchedBridge.js:23:Object
/js/react-native-github/Libraries/BatchedBridge/MessageQueue.js:89:null
<testDir>/__fixtures__/directory/fileThatDoesntExist.js:10:null
"
`;

exports[`hermes-crash option symbolicating a hermes stack trace 1`] = `
Array [
  Object {
    "column": 21,
    "functionName": "columns.forEach$argument_0",
    "line": 480,
    "name": "rows",
    "source": "/js/node_modules/react-native/Libraries/polyfills/console.js",
  },
  Object {
    "column": 8,
    "functionName": "setEnabled",
    "line": 101,
    "name": "_enabled",
    "source": "/js/react-native-github/Libraries/Performance/Systrace.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "column": null,
    "functionName": null,
    "line": null,
    "name": null,
    "source": null,
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "NativeCode": true,
  },
]
`;

exports[`hermes-crash option symbolicating a hermes stack trace CJS 1`] = `
Array [
  Object {
    "column": 36,
    "functionName": "result.clearExceptTimespans",
    "line": 160,
    "name": null,
    "source": "/js/react-native-github/Libraries/Utilities/createPerformanceLogger.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "NativeCode": true,
  },
]
`;

exports[`hermes-crash option symbolicating a hermes stack trace CJS with SegmentID 1`] = `
Array [
  Object {
    "column": 36,
    "functionName": "result.clearExceptTimespans",
    "line": 160,
    "name": null,
    "source": "/js/react-native-github/Libraries/Utilities/createPerformanceLogger.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "column": 2,
    "functionName": "getValue",
    "line": 13,
    "name": null,
    "source": "/js/RKJSModules/Apps/HermesModulesTest/HMTLazyFetched.js",
  },
  Object {
    "NativeCode": true,
  },
]
`;

exports[`symbolicating a profiler map 1`] = `
"JS_0000_xxxxxxxxxxxxxxxxxxxxxx throws0::thrower.js:48:11
JS_0001_xxxxxxxxxxxxxxxxxxxxxx throws6::thrower.js:35:38
JS_0002_xxxxxxxxxxxxxxxxxxxxxx name::thrower.js:1:0
JS_0003_xxxxxxxxxxxxxxxxxxxxxx (unknown)::thrower.js:1:0"
`;

exports[`symbolicating a stack trace 1`] = `
"thrower.js:18:null
thrower.js:30:arg
null:null:null
thrower.js:30:arguments
thrower.js:35:this
eval code
null:null:null
thrower.js:27:null
thrower.js:39:throws3
thrower.js:44:throws2
thrower.js:49:throws1
thrower.js:53:throws0
global thrower.js:1:null
"
`;

exports[`symbolicating a stack trace ignoring a function map 1`] = `
"/js/RKJSModules/segmented/biz.js:5:null
/js/RKJSModules/segmented/glo.js:7:throwSmthInner
/js/RKJSModules/bar.js:14:throwSmth
/js/RKJSModules/bar.js:10:makeItThrowInner
/js/RKJSModules/bar.js:5:makeItThrow
"
`;

exports[`symbolicating a stack trace in Node format 1`] = `
"TypeError: undefined is not a function
    at throws6 (thrower.js:18:null)
    at thrower.js:30:arg
    at forEach (null:null:null)
    at o (thrower.js:30:arguments)
    at throws4 (thrower.js:35:this)
    at eval (eval at thrower.js:30:forEach)
    at t (thrower.js:27:null)
    at throws2 (thrower.js:39:throws3)
    at throws1 (thrower.js:44:throws2)
    at throws0 (thrower.js:49:throws1)
    at thrower.js:53:throws0
    at global code (thrower.js:1:null)
"
`;

exports[`symbolicating a stack trace with a function map 1`] = `
"/js/RKJSModules/segmented/biz.js:5:module.exports.throwSmthInner
/js/RKJSModules/segmented/glo.js:7:module.exports.throwSmth
/js/RKJSModules/bar.js:14:makeItThrowInner
/js/RKJSModules/bar.js:10:makeItThrow
/js/RKJSModules/bar.js:5:import.then$argument_0
"
`;

exports[`symbolicating a stack trace with a segmented RAM bundle map 1`] = `
"/js/RKJSModules/segmented/biz.js:5:null
/js/RKJSModules/segmented/glo.js:7:throwSmthInner
/js/RKJSModules/bar.js:14:throwSmth
/js/RKJSModules/bar.js:10:makeItThrowInner
/js/RKJSModules/bar.js:5:makeItThrow
"
`;

exports[`symbolicating an attribution file 1`] = `
"{\\"functionId\\":1,\\"location\\":{\\"file\\":\\"thrower.js\\",\\"line\\":4,\\"column\\":11},\\"usage\\":[]}
{\\"functionId\\":2,\\"location\\":{\\"file\\":\\"thrower.js\\",\\"line\\":14,\\"column\\":14},\\"usage\\":[]}
"
`;

exports[`symbolicating an attribution file with 1-based column output 1`] = `
"{\\"functionId\\":1,\\"location\\":{\\"file\\":\\"thrower.js\\",\\"line\\":4,\\"column\\":12},\\"usage\\":[]}
{\\"functionId\\":2,\\"location\\":{\\"file\\":\\"thrower.js\\",\\"line\\":14,\\"column\\":15},\\"usage\\":[]}
"
`;

exports[`symbolicating with a cpuprofile 1`] = `
Object {
  "stackFrames": Object {
    "1": Object {
      "category": "JavaScript",
      "funcVirtAddr": "0",
      "name": "<global>(temp/bench.js:2:1)",
      "offset": "0",
    },
    "2": Object {
      "category": "JavaScript",
      "funcVirtAddr": "0",
      "name": "<global>(temp/bench.js:21:11)",
      "offset": "55",
    },
    "3": Object {
      "category": "JavaScript",
      "funcVirtAddr": "67",
      "name": "entryPoint(temp/bench.js:3:9)",
      "offset": "16",
      "parent": 2,
    },
    "4": Object {
      "category": "JavaScript",
      "funcVirtAddr": "89",
      "name": "helper(temp/bench.js:6:19)",
      "offset": "0",
      "parent": 3,
    },
    "5": Object {
      "category": "JavaScript",
      "funcVirtAddr": "89",
      "name": "helper(temp/bench.js:14:20)",
      "offset": "146",
      "parent": 3,
    },
    "6": Object {
      "category": "Native",
      "name": "[Native]4367295792",
      "parent": 5,
    },
  },
}
`;

exports[`symbolicating with a cpuprofile ignoring a function map 1`] = `
Object {
  "stackFrames": Object {
    "1": Object {
      "category": "JavaScript",
      "funcVirtAddr": "0",
      "name": "global+0(temp/bench.js:2:1)",
      "offset": "0",
    },
    "2": Object {
      "category": "JavaScript",
      "funcVirtAddr": "0",
      "name": "global+55(temp/bench.js:21:11)",
      "offset": "55",
    },
    "3": Object {
      "category": "JavaScript",
      "funcVirtAddr": "67",
      "name": "entryPoint+16(temp/bench.js:3:9)",
      "offset": "16",
      "parent": 2,
    },
    "4": Object {
      "category": "JavaScript",
      "funcVirtAddr": "89",
      "name": "helper+0(temp/bench.js:6:19)",
      "offset": "0",
      "parent": 3,
    },
    "5": Object {
      "category": "JavaScript",
      "funcVirtAddr": "89",
      "name": "helper+146(temp/bench.js:14:20)",
      "offset": "146",
      "parent": 3,
    },
    "6": Object {
      "category": "Native",
      "name": "[Native]4367295792",
      "parent": 5,
    },
  },
}
`;
