load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "viewmanagers",
    srcs = glob(["*.java"]),
    autoglob = False,
    is_androidx = True,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    provided_deps = [
        react_native_dep("third-party/android/androidx:annotation"),
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
    ],
)
