{"version": 3, "sources": ["../../src/link-pods/removePodEntry.ts"], "names": ["removePodEntry", "podfile<PERSON><PERSON>nt", "podspecPath", "podName", "podRegex", "RegExp", "logger", "debug", "replace"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,cAAT,CACbC,cADa,EAEbC,WAFa,EAGb;AACA,QAAMC,OAAO,GAAG,6BAAeD,WAAf,CAAhB,CADA,CAEA;;AACA,QAAME,QAAQ,GAAG,IAAIC,MAAJ,CACd,0BAAyBF,OAAQ,sEADnB,EAEf,GAFe,CAAjB;;AAIAG,qBAAOC,KAAP,CAAc,YAAWJ,OAAQ,gBAAjC;;AACA,SAAOF,cAAc,CAACO,OAAf,CAAuBJ,QAAvB,EAAiC,IAAjC,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {logger} from '@react-native-community/cli-tools';\nimport getPodspecName from '../config/getPodspecName';\n\nexport default function removePodEntry(\n  podfileContent: string,\n  podspecPath: string,\n) {\n  const podName = getPodspecName(podspecPath);\n  // this regex should catch line(s) with full pod definition, like: pod 'podname', :path => '../node_modules/podname', :subspecs => ['Sub2', 'Sub1']\n  const podRegex = new RegExp(\n    `\\\\n( |\\\\t)*pod\\\\s+(\"|')${podName}(\"|')(,\\\\s*(:[a-z]+\\\\s*=>)?\\\\s*((\"|').*?(\"|')|\\\\[[\\\\s\\\\S]*?\\\\]))*\\\\n`,\n    'g',\n  );\n  logger.debug(`Removing ${podName} from Pod file`);\n  return podfileContent.replace(podRegex, '\\n');\n}\n"]}