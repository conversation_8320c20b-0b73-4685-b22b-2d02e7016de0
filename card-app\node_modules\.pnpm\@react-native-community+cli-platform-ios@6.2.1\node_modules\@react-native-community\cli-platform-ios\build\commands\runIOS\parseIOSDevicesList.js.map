{"version": 3, "sources": ["../../../src/commands/runIOS/parseIOSDevicesList.ts"], "names": ["parseIOSDevicesList", "text", "devices", "split", "for<PERSON>ach", "line", "device", "match", "name", "version", "udid", "isSimulator", "metadata", "type", "push"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAT,CAA6BC,IAA7B,EAA0D;AACxD,QAAMC,OAAsB,GAAG,EAA/B;AAEAD,EAAAA,IAAI,CAACE,KAAL,CAAW,IAAX,EAAiBC,OAAjB,CAA0BC,IAAD,IAAU;AACjC,UAAMC,MAAM,GAAGD,IAAI,CAACE,KAAL,CACb,2DADa,CAAf;;AAGA,QAAID,MAAJ,EAAY;AACV,YAAM,GAAGE,IAAH,GAAWC,OAAX,EAAoBC,IAApB,EAA0BC,WAA1B,IAAyCL,MAA/C;AACA,YAAMM,QAAgB,GAAG;AAACJ,QAAAA,IAAD;AAAOE,QAAAA;AAAP,OAAzB;;AACA,UAAID,OAAJ,EAAa;AACXG,QAAAA,QAAQ,CAACH,OAAT,GAAmBA,OAAnB;AACAG,QAAAA,QAAQ,CAACC,IAAT,GAAgBF,WAAW,GAAG,WAAH,GAAiB,QAA5C;AACD,OAHD,MAGO;AACLC,QAAAA,QAAQ,CAACC,IAAT,GAAgB,UAAhB;AACD;;AACDX,MAAAA,OAAO,CAACY,IAAR,CAAaF,QAAb;AACD;AACF,GAfD;AAiBA,SAAOV,OAAP;AACD;;eAEcF,mB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport {Device} from '../../types';\n\n/**\n * Parses the output of the `xcrun instruments -s` command and returns metadata\n * about available iOS simulators and physical devices, as well as host Mac for\n * Catalyst purposes.\n *\n * Expected text looks roughly like this:\n *\n * ```\n * Known Devices:\n * this-mac-device [UDID]\n * A Physical Device (OS Version) [UDID]\n * A Simulator Device (OS Version) [UDID] (Simulator)\n * ```\n */\nfunction parseIOSDevicesList(text: string): Array<Device> {\n  const devices: Array<Device> = [];\n\n  text.split('\\n').forEach((line) => {\n    const device = line.match(\n      /(.*?) (\\(([0-9.]+)\\) )?\\[([0-9A-F-]+)\\]( \\(Simulator\\))?/i,\n    );\n    if (device) {\n      const [, name, , version, udid, isSimulator] = device;\n      const metadata: Device = {name, udid};\n      if (version) {\n        metadata.version = version;\n        metadata.type = isSimulator ? 'simulator' : 'device';\n      } else {\n        metadata.type = 'catalyst';\n      }\n      devices.push(metadata);\n    }\n  });\n\n  return devices;\n}\n\nexport default parseIOSDevicesList;\n"]}