/**
* Copyright (c) Facebook, Inc. and its affiliates.
*
* This source code is licensed under the MIT license found in the
* LICENSE file in the root directory of this source tree.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;

public interface SwitchManagerInterface<T extends View> {
  void setDisabled(T view, boolean value);
  void setValue(T view, boolean value);
  void setTintColor(T view, @Nullable Integer value);
  void setOnTintColor(T view, @Nullable Integer value);
  void setThumbTintColor(T view, @Nullable Integer value);
  void setThumbColor(T view, @Nullable Integer value);
  void setTrackColorForFalse(T view, @Nullable Integer value);
  void setTrackColorForTrue(T view, @Nullable Integer value);
}
