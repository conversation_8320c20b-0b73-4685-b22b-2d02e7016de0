load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_target", "react_native_tests_target", "rn_robolectric_test")

rn_robolectric_test(
    name = "modules",
    srcs = glob(["**/*.java"]),
    contacts = ["<EMAIL>"],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        YOGA_TARGET,
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-ui"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/fest:fest"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_dep("third-party/java/okhttp:okhttp3"),
        react_native_dep("third-party/java/okio:okio"),
        react_native_target("java/com/facebook/react:react"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/common/network:network"),
        react_native_target("java/com/facebook/react/devsupport:interfaces"),
        react_native_target("java/com/facebook/react/jstasks:jstasks"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/modules/blob:blob"),
        react_native_target("java/com/facebook/react/modules/camera:camera"),
        react_native_target("java/com/facebook/react/modules/clipboard:clipboard"),
        react_native_target("java/com/facebook/react/modules/common:common"),
        react_native_target("java/com/facebook/react/modules/core:core"),
        react_native_target("java/com/facebook/react/modules/debug:debug"),
        react_native_target("java/com/facebook/react/modules/deviceinfo:deviceinfo"),
        react_native_target("java/com/facebook/react/modules/dialog:dialog"),
        react_native_target("java/com/facebook/react/modules/network:network"),
        react_native_target("java/com/facebook/react/modules/share:share"),
        react_native_target("java/com/facebook/react/modules/storage:storage"),
        react_native_target("java/com/facebook/react/modules/systeminfo:systeminfo"),
        react_native_target("java/com/facebook/react/touch:touch"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_tests_target("java/com/facebook/react/bridge:testhelpers"),
    ],
)
