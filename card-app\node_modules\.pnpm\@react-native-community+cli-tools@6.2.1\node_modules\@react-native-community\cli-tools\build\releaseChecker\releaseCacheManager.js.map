{"version": 3, "sources": ["../../src/releaseChecker/releaseCacheManager.ts"], "names": ["loadCache", "name", "cacheRaw", "fs", "readFileSync", "path", "resolve", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cache", "JSON", "parse", "e", "code", "saveCache", "logger", "debug", "undefined", "writeFileSync", "stringify", "legacyPath", "os", "homedir", "cachePath", "appName", "existsSync", "mkdirSync", "recursive", "get", "key", "set", "value"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAKA,SAASA,SAAT,CAAmBC,IAAnB,EAAoD;AAClD,MAAI;AACF,UAAMC,QAAQ,GAAGC,cAAGC,YAAH,CACfC,gBAAKC,OAAL,CAAaC,gBAAgB,EAA7B,EAAiCN,IAAjC,CADe,EAEf,MAFe,CAAjB;;AAIA,UAAMO,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWR,QAAX,CAAd;AACA,WAAOM,KAAP;AACD,GAPD,CAOE,OAAOG,CAAP,EAAU;AACV,QAAIA,CAAC,CAACC,IAAF,KAAW,QAAf,EAAyB;AACvB;AACAC,MAAAA,SAAS,CAACZ,IAAD,EAAO,EAAP,CAAT;AACD;;AACDa,oBAAOC,KAAP,CAAa,wBAAb;;AACA,WAAOC,SAAP;AACD;AACF;;AAED,SAASH,SAAT,CAAmBZ,IAAnB,EAAiCO,KAAjC,EAA+C;AAC7CL,gBAAGc,aAAH,CACEZ,gBAAKC,OAAL,CAAaC,gBAAgB,EAA7B,EAAiCN,IAAjC,CADF,EAEEQ,IAAI,CAACS,SAAL,CAAeV,KAAf,EAAsB,IAAtB,EAA4B,CAA5B,CAFF;AAID;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASD,gBAAT,GAA4B;AAC1B,QAAMY,UAAU,GAAGd,gBAAKC,OAAL,CAAac,cAAGC,OAAH,EAAb,EAA2B,mBAA3B,EAAgD,OAAhD,CAAnB;;AACA,QAAMC,SAAS,GAAG,0BAAQ;AAACC,IAAAA,OAAO,EAAE,kBAAV;AAA8BJ,IAAAA;AAA9B,GAAR,EAAmDX,KAArE;;AAEA,MAAI,CAACL,cAAGqB,UAAH,CAAcF,SAAd,CAAL,EAA+B;AAC7BnB,kBAAGsB,SAAH,CAAaH,SAAb,EAAwB;AAACI,MAAAA,SAAS,EAAE;AAAZ,KAAxB;AACD;;AAED,SAAOJ,SAAP;AACD;;AAED,SAASK,GAAT,CAAa1B,IAAb,EAA2B2B,GAA3B,EAAqE;AACnE,QAAMpB,KAAK,GAAGR,SAAS,CAACC,IAAD,CAAvB;;AACA,MAAIO,KAAJ,EAAW;AACT,WAAOA,KAAK,CAACoB,GAAD,CAAZ;AACD;;AACD,SAAOZ,SAAP;AACD;;AAED,SAASa,GAAT,CAAa5B,IAAb,EAA2B2B,GAA3B,EAAiDE,KAAjD,EAAgE;AAC9D,QAAMtB,KAAK,GAAGR,SAAS,CAACC,IAAD,CAAvB;;AACA,MAAIO,KAAJ,EAAW;AACTA,IAAAA,KAAK,CAACoB,GAAD,CAAL,GAAaE,KAAb;AACAjB,IAAAA,SAAS,CAACZ,IAAD,EAAOO,KAAP,CAAT;AACD;AACF;;eAEc;AACbmB,EAAAA,GADa;AAEbE,EAAAA;AAFa,C", "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\nimport os from 'os';\nimport appDirs from 'appdirsjs';\nimport logger from '../logger';\n\ntype ReleaseCacheKey = 'eTag' | 'lastChecked' | 'latestVersion';\ntype Cache = {[key in ReleaseCacheKey]?: string};\n\nfunction loadCache(name: string): Cache | undefined {\n  try {\n    const cacheRaw = fs.readFileSync(\n      path.resolve(getCacheRootPath(), name),\n      'utf8',\n    );\n    const cache = JSON.parse(cacheRaw);\n    return cache;\n  } catch (e) {\n    if (e.code === 'ENOENT') {\n      // Create cache file since it doesn't exist.\n      saveCache(name, {});\n    }\n    logger.debug('No release cache found');\n    return undefined;\n  }\n}\n\nfunction saveCache(name: string, cache: Cache) {\n  fs.writeFileSync(\n    path.resolve(getCacheRootPath(), name),\n    JSON.stringify(cache, null, 2),\n  );\n}\n\n/**\n * Returns the path string of `$HOME/.react-native-cli`.\n *\n * In case it doesn't exist, it will be created.\n */\nfunction getCacheRootPath() {\n  const legacyPath = path.resolve(os.homedir(), '.react-native-cli', 'cache');\n  const cachePath = appDirs({appName: 'react-native-cli', legacyPath}).cache;\n\n  if (!fs.existsSync(cachePath)) {\n    fs.mkdirSync(cachePath, {recursive: true});\n  }\n\n  return cachePath;\n}\n\nfunction get(name: string, key: ReleaseCacheKey): string | undefined {\n  const cache = loadCache(name);\n  if (cache) {\n    return cache[key];\n  }\n  return undefined;\n}\n\nfunction set(name: string, key: ReleaseCacheKey, value: string) {\n  const cache = loadCache(name);\n  if (cache) {\n    cache[key] = value;\n    saveCache(name, cache);\n  }\n}\n\nexport default {\n  get,\n  set,\n};\n"]}