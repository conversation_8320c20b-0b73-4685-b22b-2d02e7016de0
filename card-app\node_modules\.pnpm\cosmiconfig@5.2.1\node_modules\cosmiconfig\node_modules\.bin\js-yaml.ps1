#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules\js-yaml\bin\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules\js-yaml\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/js-yaml@3.14.1/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../js-yaml@3.14.1/node_modules/js-yaml/bin/js-yaml.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../js-yaml@3.14.1/node_modules/js-yaml/bin/js-yaml.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../js-yaml@3.14.1/node_modules/js-yaml/bin/js-yaml.js" $args
  } else {
    & "node$exe"  "$basedir/../../../../../js-yaml@3.14.1/node_modules/js-yaml/bin/js-yaml.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
