{"version": 3, "names": ["tryLaunchAppOnDevice", "device", "androidProject", "adbPath", "args", "appId", "appIdSuffix", "packageName", "mainActivity", "applicationId", "applicationIdWithSuffix", "filter", "Boolean", "join", "activityToLaunch", "startsWith", "includes", "adbArgs", "unshift", "logger", "info", "debug", "execa", "sync", "stdio", "error", "CLIError"], "sources": ["../../../src/commands/runAndroid/tryLaunchAppOnDevice.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport execa from 'execa';\nimport {AndroidProject, Flags} from '.';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\n\nfunction tryLaunchAppOnDevice(\n  device: string | void,\n  androidProject: AndroidProject,\n  adbPath: string,\n  args: Flags,\n) {\n  const {appId, appIdSuffix} = args;\n\n  const {packageName, mainActivity, applicationId} = androidProject;\n\n  const applicationIdWithSuffix = [appId || applicationId, appIdSuffix]\n    .filter(Boolean)\n    .join('.');\n\n  const activityToLaunch =\n    mainActivity.startsWith(packageName) ||\n    (!mainActivity.startsWith('.') && mainActivity.includes('.'))\n      ? mainActivity\n      : mainActivity.startsWith('.')\n      ? [packageName, mainActivity].join('')\n      : [packageName, mainActivity].filter(Boolean).join('.');\n\n  try {\n    // Here we're using the same flags as Android Studio to launch the app\n    const adbArgs = [\n      'shell',\n      'am',\n      'start',\n      '-n',\n      `${applicationIdWithSuffix}/${activityToLaunch}`,\n      '-a',\n      'android.intent.action.MAIN',\n      '-c',\n      'android.intent.category.LAUNCHER',\n    ];\n\n    if (device) {\n      adbArgs.unshift('-s', device);\n      logger.info(`Starting the app on \"${device}\"...`);\n    } else {\n      logger.info('Starting the app...');\n    }\n    logger.debug(`Running command \"${adbPath} ${adbArgs.join(' ')}\"`);\n    execa.sync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (error) {\n    throw new CLIError('Failed to start the app.', error as any);\n  }\n}\n\nexport default tryLaunchAppOnDevice;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAmE;AAVnE;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,SAASA,oBAAoB,CAC3BC,MAAqB,EACrBC,cAA8B,EAC9BC,OAAe,EACfC,IAAW,EACX;EACA,MAAM;IAACC,KAAK;IAAEC;EAAW,CAAC,GAAGF,IAAI;EAEjC,MAAM;IAACG,WAAW;IAAEC,YAAY;IAAEC;EAAa,CAAC,GAAGP,cAAc;EAEjE,MAAMQ,uBAAuB,GAAG,CAACL,KAAK,IAAII,aAAa,EAAEH,WAAW,CAAC,CAClEK,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;EAEZ,MAAMC,gBAAgB,GACpBN,YAAY,CAACO,UAAU,CAACR,WAAW,CAAC,IACnC,CAACC,YAAY,CAACO,UAAU,CAAC,GAAG,CAAC,IAAIP,YAAY,CAACQ,QAAQ,CAAC,GAAG,CAAE,GACzDR,YAAY,GACZA,YAAY,CAACO,UAAU,CAAC,GAAG,CAAC,GAC5B,CAACR,WAAW,EAAEC,YAAY,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,GACpC,CAACN,WAAW,EAAEC,YAAY,CAAC,CAACG,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3D,IAAI;IACF;IACA,MAAMI,OAAO,GAAG,CACd,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACH,GAAEP,uBAAwB,IAAGI,gBAAiB,EAAC,EAChD,IAAI,EACJ,4BAA4B,EAC5B,IAAI,EACJ,kCAAkC,CACnC;IAED,IAAIb,MAAM,EAAE;MACVgB,OAAO,CAACC,OAAO,CAAC,IAAI,EAAEjB,MAAM,CAAC;MAC7BkB,kBAAM,CAACC,IAAI,CAAE,wBAAuBnB,MAAO,MAAK,CAAC;IACnD,CAAC,MAAM;MACLkB,kBAAM,CAACC,IAAI,CAAC,qBAAqB,CAAC;IACpC;IACAD,kBAAM,CAACE,KAAK,CAAE,oBAAmBlB,OAAQ,IAAGc,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;IACjES,gBAAK,CAACC,IAAI,CAACpB,OAAO,EAAEc,OAAO,EAAE;MAACO,KAAK,EAAE;IAAS,CAAC,CAAC;EAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAAC,0BAA0B,EAAED,KAAK,CAAQ;EAC9D;AACF;AAAC,eAEczB,oBAAoB;AAAA"}