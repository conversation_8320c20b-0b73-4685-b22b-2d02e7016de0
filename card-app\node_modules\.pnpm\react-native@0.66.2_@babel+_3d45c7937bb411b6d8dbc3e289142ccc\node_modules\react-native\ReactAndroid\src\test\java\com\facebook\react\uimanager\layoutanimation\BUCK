load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_target", "rn_robolectric_test")

rn_robolectric_test(
    name = "layoutanimation",
    srcs = glob(["**/*.java"]),
    contacts = ["<EMAIL>"],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        YOGA_TARGET,
        react_native_dep("third-party/java/fest:fest"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
    ],
)
