{"version": 3, "sources": ["../../src/link-pods/findLineToAddPod.ts"], "names": ["findLineToAddPod", "podLines", "firstTargetLine", "<PERSON><PERSON><PERSON><PERSON>", "endOfCurrentTarget", "functionDefinition", "i", "length", "matchNextConstruct", "match", "matchEnd", "firstNonSpaceCharacter", "search", "indentation", "line"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEe,SAASA,gBAAT,CACbC,QADa,EAEbC,eAFa,EAGb;AACA;AACA,QAAMC,UAAU,GAAG,0BAAnB,CAFA,CAGA;;AACA,QAAMC,kBAAkB,GAAG,cAA3B,CAJA,CAKA;;AACA,QAAMC,kBAAkB,GAAG,mCAA3B;;AAEA,OAAK,IAAIC,CAAC,GAAGJ,eAAb,EAA8BI,CAAC,GAAGL,QAAQ,CAACM,MAAT,GAAkB,CAApD,EAAuDD,CAAC,EAAxD,EAA4D;AAC1D,UAAME,kBAAkB,GACtBP,QAAQ,CAACK,CAAD,CAAR,CAAYG,KAAZ,CAAkBN,UAAlB,KAAiCF,QAAQ,CAACK,CAAD,CAAR,CAAYG,KAAZ,CAAkBJ,kBAAlB,CADnC;AAEA,UAAMK,QAAQ,GAAGT,QAAQ,CAACK,CAAD,CAAR,CAAYG,KAAZ,CAAkBL,kBAAlB,CAAjB;;AAEA,QAAII,kBAAkB,IAAIE,QAA1B,EAAoC;AAClC,YAAMC,sBAAsB,GAAGV,QAAQ,CAACK,CAAD,CAAR,CAAYM,MAAZ,CAAmB,IAAnB,CAA/B;AACA,aAAO;AACLC,QAAAA,WAAW,EAAEF,sBAAsB,IAAID,QAAQ,GAAG,CAAH,GAAO,CAAnB,CAD9B;AAELI,QAAAA,IAAI,EAAER;AAFD,OAAP;AAID;AACF;;AACD,SAAO,IAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport default function findLineToAddPod(\n  podLines: Array<string>,\n  firstTargetLine: number,\n) {\n  // match line with new target: target 'project_name' do (most likely target inside podfile main target)\n  const nextTarget = /target ('|\")\\w+('|\") do/g;\n  // match line that has only 'end' (if we don't catch new target or function, this would mean this is end of current target)\n  const endOfCurrentTarget = /^\\s*end\\s*$/g;\n  // match function definition, like: post_install do |installer| (some Podfiles have function defined inside main target\n  const functionDefinition = /^\\s*[a-z_]+\\s+do(\\s+\\|[a-z]+\\|)?/g;\n\n  for (let i = firstTargetLine; i < podLines.length - 1; i++) {\n    const matchNextConstruct =\n      podLines[i].match(nextTarget) || podLines[i].match(functionDefinition);\n    const matchEnd = podLines[i].match(endOfCurrentTarget);\n\n    if (matchNextConstruct || matchEnd) {\n      const firstNonSpaceCharacter = podLines[i].search(/\\S/);\n      return {\n        indentation: firstNonSpaceCharacter + (matchEnd ? 2 : 0),\n        line: i,\n      };\n    }\n  }\n  return null;\n}\n"]}