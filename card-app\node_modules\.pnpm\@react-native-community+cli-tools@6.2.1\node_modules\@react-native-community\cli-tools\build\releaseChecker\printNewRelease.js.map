{"version": 3, "sources": ["../../src/releaseChecker/printNewRelease.ts"], "names": ["printNewRelease", "name", "latestRelease", "currentVersion", "logger", "info", "version", "chalk", "dim", "underline", "changelogUrl", "diffUrl", "bold", "cacheManager", "set", "Date", "toISOString"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;;;;AAEA;AACA;AACA;AACe,SAASA,eAAT,CACbC,IADa,EAEbC,aAFa,EAGbC,cAHa,EAIb;AACAC,kBAAOC,IAAP,CACG,iBAAgBH,aAAa,CAACI,OAAQ,kDAAiDH,cAAe,IADzG;;AAGAC,kBAAOC,IAAP,CAAa,cAAaE,iBAAMC,GAAN,CAAUC,SAAV,CAAoBP,aAAa,CAACQ,YAAlC,CAAgD,GAA1E;;AACAN,kBAAOC,IAAP,CAAa,SAAQE,iBAAMC,GAAN,CAAUC,SAAV,CAAoBP,aAAa,CAACS,OAAlC,CAA2C,GAAhE;;AACAP,kBAAOC,IAAP,CAAa,oBAAmBE,iBAAMK,IAAN,CAAW,sBAAX,CAAmC,IAAnE;;AAEAC,+BAAaC,GAAb,CAAiBb,IAAjB,EAAuB,aAAvB,EAAsC,IAAIc,IAAJ,GAAWC,WAAX,EAAtC;AACD", "sourcesContent": ["import chalk from 'chalk';\nimport logger from '../logger';\nimport {Release} from './getLatestRelease';\nimport cacheManager from './releaseCacheManager';\n\n/**\n * Notifies the user that a newer version of React Native is available.\n */\nexport default function printNewRelease(\n  name: string,\n  latestRelease: Release,\n  currentVersion: string,\n) {\n  logger.info(\n    `React Native v${latestRelease.version} is now available (your project is running on v${currentVersion}).`,\n  );\n  logger.info(`Changelog: ${chalk.dim.underline(latestRelease.changelogUrl)}.`);\n  logger.info(`Diff: ${chalk.dim.underline(latestRelease.diffUrl)}.`);\n  logger.info(`To upgrade, run \"${chalk.bold('react-native upgrade')}\".`);\n\n  cacheManager.set(name, 'lastChecked', new Date().toISOString());\n}\n"]}