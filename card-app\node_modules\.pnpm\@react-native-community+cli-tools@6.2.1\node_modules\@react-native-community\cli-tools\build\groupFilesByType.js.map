{"version": 3, "sources": ["../src/groupFilesByType.ts"], "names": ["groupFilesByType", "assets", "type", "mime", "getType", "split"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,gBAAT,CAA0BC,MAA1B,EAAiD;AAC9D,SAAO,uBAAQA,MAAR,EAAiBC,IAAD,IAAU,CAACC,gBAAKC,OAAL,CAAaF,IAAb,KAAsB,EAAvB,EAA2BG,KAA3B,CAAiC,GAAjC,EAAsC,CAAtC,CAA1B,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {groupBy} from 'lodash';\nimport mime from 'mime';\n\n/**\n * Given an array of files, it groups it by it's type.\n * Type of the file is inferred from it's mimetype based on the extension\n * file ends up with. The returned value is an object with properties that\n * correspond to the first part of the mimetype, e.g. images will be grouped\n * under `image` key since the mimetype for them is `image/jpg` etc.\n *\n * Example:\n * Given an array ['fonts/a.ttf', 'images/b.jpg'],\n * the returned object will be: {font: ['fonts/a.ttf'], image: ['images/b.jpg']}\n */\nexport default function groupFilesByType(assets: Array<string>) {\n  return groupBy(assets, (type) => (mime.getType(type) || '').split('/')[0]);\n}\n"]}