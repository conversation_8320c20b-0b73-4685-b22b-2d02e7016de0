{"version": 3, "sources": ["../../src/link/addFileToProject.ts"], "names": ["addFileToProject", "project", "filePath", "file", "PbxFile", "uuid", "generateUuid", "fileRef", "addToPbxFileReferenceSection"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACe,SAASA,gBAAT,CAA0BC,OAA1B,EAAwCC,QAAxC,EAA0D;AACvE,QAAMC,IAAI,GAAG,KAAIC,kBAAJ,EAAYF,QAAZ,CAAb;AACAC,EAAAA,IAAI,CAACE,IAAL,GAAYJ,OAAO,CAACK,YAAR,EAAZ;AACAH,EAAAA,IAAI,CAACI,OAAL,GAAeN,OAAO,CAACK,YAAR,EAAf;AACAL,EAAAA,OAAO,CAACO,4BAAR,CAAqCL,IAArC;AACA,SAAOA,IAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport PbxFile from 'xcode/lib/pbxFile';\n\n/**\n * Given xcodeproj and filePath, it creates new file\n * from path provided, adds it to the project\n * and returns newly created instance of a file\n */\nexport default function addFileToProject(project: any, filePath: string) {\n  const file = new PbxFile(filePath);\n  file.uuid = project.generateUuid();\n  file.fileRef = project.generateUuid();\n  project.addToPbxFileReferenceSection(file);\n  return file;\n}\n"]}