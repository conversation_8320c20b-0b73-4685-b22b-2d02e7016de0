{"version": 3, "names": ["printNewRelease", "name", "latestRelease", "currentVersion", "logger", "info", "stable", "chalk", "dim", "underline", "changelogUrl", "diffUrl", "link", "docs", "cacheManager", "set", "Date", "toISOString"], "sources": ["../../src/releaseChecker/printNewRelease.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as link from '../doclink';\n\nimport logger from '../logger';\nimport type {Release} from './getLatestRelease';\nimport cacheManager from '../cacheManager';\n\n/**\n * Notifies the user that a newer version of React Native is available.\n */\nexport default function printNewRelease(\n  name: string,\n  latestRelease: Release,\n  currentVersion: string,\n) {\n  logger.info(\n    `React Native v${latestRelease.stable} is now available (your project is running on v${currentVersion}).`,\n  );\n  logger.info(`Changelog: ${chalk.dim.underline(latestRelease.changelogUrl)}`);\n  logger.info(`Diff: ${chalk.dim.underline(latestRelease.diffUrl)}`);\n  logger.info(\n    `For more info, check out \"${chalk.dim.underline(\n      link.docs('upgrading', 'none'),\n    )}\".`,\n  );\n\n  cacheManager.set(name, 'lastChecked', new Date().toISOString());\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAEA;AAEA;AAA2C;AAAA;AAAA;AAE3C;AACA;AACA;AACe,SAASA,eAAe,CACrCC,IAAY,EACZC,aAAsB,EACtBC,cAAsB,EACtB;EACAC,eAAM,CAACC,IAAI,CACR,iBAAgBH,aAAa,CAACI,MAAO,kDAAiDH,cAAe,IAAG,CAC1G;EACDC,eAAM,CAACC,IAAI,CAAE,cAAaE,gBAAK,CAACC,GAAG,CAACC,SAAS,CAACP,aAAa,CAACQ,YAAY,CAAE,EAAC,CAAC;EAC5EN,eAAM,CAACC,IAAI,CAAE,SAAQE,gBAAK,CAACC,GAAG,CAACC,SAAS,CAACP,aAAa,CAACS,OAAO,CAAE,EAAC,CAAC;EAClEP,eAAM,CAACC,IAAI,CACR,6BAA4BE,gBAAK,CAACC,GAAG,CAACC,SAAS,CAC9CG,IAAI,CAACC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAC9B,IAAG,CACN;EAEDC,qBAAY,CAACC,GAAG,CAACd,IAAI,EAAE,aAAa,EAAE,IAAIe,IAAI,EAAE,CAACC,WAAW,EAAE,CAAC;AACjE"}