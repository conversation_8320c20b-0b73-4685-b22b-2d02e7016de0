/*
 * @Author: 高超
 * @Date: 2021-11-07 11:01:26
 * @LastEditTime: 2022-08-28 19:23:04
 * @FilePath: /TOY/Users/<USER>/oneCard/CardApp/src/utils/tools.js
 * love jiajia
 */
import { ToastAndroid, Linking } from 'react-native';
import { hex_md5 } from './md5';
import AsyncStorage from '@react-native-async-storage/async-storage';
const Tools = { 
    baseUrl: 'https://test.qqyhmmwg.com/checker/',
    //baseUrl: 'http://***********:8080/checker/',
    vin: 'V2.1',
    phone: "4006091798",
    tips: `北京风景名胜区协会 客服电话: 4006091798`,
    linkTo: async (url) => {
        await Linking.canOpenURL(url).then(supported => {
            if (!supported) {
                ToastAndroid.show('协议类型不支持', 1000) 
            } else {
                return Linking.openURL(url); 
            }
        }).catch(err => ToastAndroid.show('网络连接错误', 1000) ) 
    },
    setData : async (key, value) => {
        try {
            const jsonValue = JSON.stringify(value)
            await AsyncStorage.setItem(key, jsonValue)
        } catch (e) {
            //console.log(e)
        // saving error
        }
    },
    getData : async (key) => {
        try {
            const value = await AsyncStorage.getItem(key)
            return JSON.parse(value)
        } catch(e) {
        // error reading value
        }
    },
    getTrim : (str, s1 = " ", s2 = "") => {
        return str.replace(new RegExp(s1, "gm"), s2);
    },
    createId  : (l = 10) => {
        const x = '1234567890poiuytrewqasdfghjklmnbvcxz';
        let tmp = '';
        for (let i = 0; i < l; i++) {
            tmp += x.charAt(Math.ceil(Math.random() * 100000000) % x.length);
        }
        return tmp;
    },
    creatSign : (accessKeyId, method, data = {}, accessKey) => {
        const timestamp = (() => {
          return parseInt(Date.parse(new Date()) / 1000, 10)
        })()
        const nonce = `${Tools.createId(12)}`;
        const stringToSignArray = [
          `accessKeyId=${accessKeyId}`,
          `nonce=${nonce}`,
          `timestamp=${timestamp}`,
          `method=${method.toUpperCase()}`,
          `key=${accessKey}`
        ]
        stringToSignArray.push(`content=${hex_md5(encodeURI(JSON.stringify(data))).toUpperCase()}`);
        const stringToSign = stringToSignArray.sort();
        // console.log(encodeURI(JSON.stringify(data)))
        const signStr = hex_md5(stringToSign.join('&_*')).toString().toUpperCase()
        return [
          `accessKeyId=${accessKeyId}`,
          `nonce=${nonce}`,
          `timestamp=${timestamp}`,
          `signature=${signStr}`
          ].join(';')
    },
    Api: async (url , method = 'GET', data ={}, header = {}, key = null) => {
        const token = await Tools.getData('token')
        const apiParams = { 
            method,
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                'authorization' : `Bearer ${(token === null) ? 'nologin' : token}`,
                'customize-authorize': Tools.creatSign("0tTu8etSrB971nwaktfT91gOzIHD", method, data, "WehMeafRaFjtc2iZMpYEgglCcI3ZfoPh"),
                ...header
            }
        }
        if (method !== 'GET') {
            apiParams.body = JSON.stringify(data)
        }
        return await fetch(`${Tools.baseUrl}${url}`, apiParams)
                    .then( async (response) => await response.json())
                    .then((responseJson) => {
                        return responseJson
                    }).catch(function(err) {
                        ToastAndroid.show('网络连接错误', 1000) 
                    });
    }
}
export default Tools
