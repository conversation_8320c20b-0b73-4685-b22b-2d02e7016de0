{"version": 3, "sources": ["../../src/android/adb.ts"], "names": ["runAndroidLoggingProcess", "adbPath", "execPath", "getAdbPath", "spawnLogcatProcess", "customPath", "path", "resolve", "process", "env", "ANDROID_HOME", "error", "CodeError", "ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER", "message", "stdio", "ERR_ANDROID_CANNOT_START_LOGCAT", "getApplicationPid", "applicationId", "output", "ERR_ANDROID_CANNOT_GET_APP_PID", "pid", "parseInt", "toString", "NaN", "isNaN", "ERR_ANDROID_UNPROCESSABLE_PID"], "mappings": ";;;;;;;;;;AAAA;;AACA;;AACA;;;;AAQO,SAASA,wBAAT,CAAkCC,OAAlC,EAAkE;AACvE,QAAMC,QAAQ,GAAGC,UAAU,CAACF,OAAD,CAA3B;AACA,SAAOG,kBAAkB,CAACF,QAAD,CAAzB;AACD;;AAEM,SAASC,UAAT,CAAoBE,UAApB,EAAiD;AACtD,MAAIA,UAAJ,EAAgB;AACd,WAAOC,cAAKC,OAAL,CAAaF,UAAb,CAAP;AACD;;AAED,SAAOG,OAAO,CAACC,GAAR,CAAYC,YAAZ,GACF,GAAEF,OAAO,CAACC,GAAR,CAAYC,YAAa,qBADzB,GAEH,KAFJ;AAGD;;AAEM,SAASN,kBAAT,CAA4BH,OAA5B,EAA2D;AAChE,MAAI;AACF,qCAAaA,OAAb,EAAsB,CAAC,QAAD,EAAW,IAAX,CAAtB;AACD,GAFD,CAEE,OAAOU,KAAP,EAAc;AACd,UAAM,IAAIC,iBAAJ,CACJC,8CADI,EAEHF,KAAD,CAAiBG,OAFb,CAAN;AAID;;AAED,MAAI;AACF,WAAO,0BAAMb,OAAN,EAAe,CAAC,QAAD,EAAW,IAAX,EAAiB,MAAjB,EAAyB,SAAzB,EAAoC,KAApC,CAAf,EAA2D;AAChEc,MAAAA,KAAK,EAAE;AADyD,KAA3D,CAAP;AAGD,GAJD,CAIE,OAAOJ,KAAP,EAAc;AACd,UAAM,IAAIC,iBAAJ,CACJI,uCADI,EAEHL,KAAD,CAAiBG,OAFb,CAAN;AAID;AACF;;AAEM,SAASG,iBAAT,CACLC,aADK,EAELjB,OAFK,EAGG;AACR,MAAIkB,MAAJ;;AACA,MAAI;AACFA,IAAAA,MAAM,GAAG,iCAAahB,UAAU,CAACF,OAAD,CAAvB,EAAkC,CACzC,OADyC,EAEzC,OAFyC,EAGzC,IAHyC,EAIzCiB,aAJyC,CAAlC,CAAT;AAMD,GAPD,CAOE,OAAOP,KAAP,EAAc;AACd,UAAM,IAAIC,iBAAJ,CACJQ,sCADI,EAEHT,KAAD,CAAiBG,OAFb,CAAN;AAID;;AAED,QAAMO,GAAG,GAAGF,MAAM,GAAGG,QAAQ,CAACH,MAAM,CAACI,QAAP,EAAD,EAAoB,EAApB,CAAX,GAAqCC,GAAvD;;AACA,MAAIC,KAAK,CAACJ,GAAD,CAAT,EAAgB;AACd,UAAM,IAAIT,iBAAJ,CAAcc,qCAAd,CAAN;AACD;;AAED,SAAOL,GAAP;AACD", "sourcesContent": ["import { spawn, execFileSync, ChildProcess } from 'child_process';\nimport path from 'path';\nimport {\n  CodeError,\n  ERR_ANDROID_UNPROCESSABLE_PID,\n  ERR_ANDROID_CANNOT_GET_APP_PID,\n  ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER,\n  ERR_ANDROID_CANNOT_START_LOGCAT,\n} from '../errors';\n\nexport function runAndroidLoggingProcess(adbPath?: string): ChildProcess {\n  const execPath = getAdbPath(adbPath);\n  return spawnLogcatProcess(execPath);\n}\n\nexport function getAdbPath(customPath?: string): string {\n  if (customPath) {\n    return path.resolve(customPath);\n  }\n\n  return process.env.ANDROID_HOME\n    ? `${process.env.ANDROID_HOME}/platform-tools/adb`\n    : 'adb';\n}\n\nexport function spawnLogcatProcess(adbPath: string): ChildProcess {\n  try {\n    execFileSync(adbPath, ['logcat', '-c']);\n  } catch (error) {\n    throw new CodeError(\n      ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER,\n      (error as Error).message\n    );\n  }\n\n  try {\n    return spawn(adbPath, ['logcat', '-v', 'time', 'process', 'tag'], {\n      stdio: 'pipe',\n    });\n  } catch (error) {\n    throw new CodeError(\n      ERR_ANDROID_CANNOT_START_LOGCAT,\n      (error as Error).message\n    );\n  }\n}\n\nexport function getApplicationPid(\n  applicationId: string,\n  adbPath?: string\n): number {\n  let output: Buffer | String | undefined;\n  try {\n    output = execFileSync(getAdbPath(adbPath), [\n      'shell',\n      'pidof',\n      '-s',\n      applicationId,\n    ]);\n  } catch (error) {\n    throw new CodeError(\n      ERR_ANDROID_CANNOT_GET_APP_PID,\n      (error as Error).message\n    );\n  }\n\n  const pid = output ? parseInt(output.toString(), 10) : NaN;\n  if (isNaN(pid)) {\n    throw new CodeError(ERR_ANDROID_UNPROCESSABLE_PID);\n  }\n\n  return pid;\n}\n"], "file": "adb.js"}