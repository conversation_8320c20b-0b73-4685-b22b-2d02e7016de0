{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "spinner", "interval", "frames", "indent", "isSpinning", "text", "prefixText", "color", "succeed", "_text", "fail", "start", "stop", "warn", "info", "stopAndPersist", "clear", "render", "frame", "<PERSON><PERSON><PERSON><PERSON>", "options", "logger", "isVerbose", "ora", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../src/loader.ts"], "sourcesContent": ["import ora from 'ora';\nimport type {Or<PERSON>, Options, Spinner, Color} from 'ora';\nimport logger from './logger';\n\nexport type Loader = Ora;\n\nclass OraNoop implements Loader {\n  spinner: Spinner = {interval: 1, frames: []};\n  indent: number = 0;\n  isSpinning: boolean = false;\n  text: string = '';\n  prefixText: string = '';\n  color: Color = 'blue';\n\n  succeed(_text?: string | undefined) {\n    return this;\n  }\n  fail(_text?: string) {\n    return this;\n  }\n  start(_text?: string) {\n    return this;\n  }\n  stop() {\n    return this;\n  }\n  warn(_text?: string) {\n    return this;\n  }\n  info(_text?: string) {\n    return this;\n  }\n  stopAndPersist() {\n    return this;\n  }\n  clear() {\n    return this;\n  }\n  render() {\n    return this;\n  }\n  frame() {\n    return this.text;\n  }\n}\n\nexport function getLoader(options?: string | Options | undefined): Loader {\n  return logger.isVerbose() ? new OraNoop() : ora(options);\n}\n\nexport const NoopLoader = OraNoop;\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAA8B;AAI9B,MAAMA,OAAO,CAAmB;EAC9BC,OAAO,GAAY;IAACC,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5CC,MAAM,GAAW,CAAC;EAClBC,UAAU,GAAY,KAAK;EAC3BC,IAAI,GAAW,EAAE;EACjBC,UAAU,GAAW,EAAE;EACvBC,KAAK,GAAU,MAAM;EAErBC,OAAO,CAACC,KAA0B,EAAE;IAClC,OAAO,IAAI;EACb;EACAC,IAAI,CAACD,KAAc,EAAE;IACnB,OAAO,IAAI;EACb;EACAE,KAAK,CAACF,KAAc,EAAE;IACpB,OAAO,IAAI;EACb;EACAG,IAAI,GAAG;IACL,OAAO,IAAI;EACb;EACAC,IAAI,CAACJ,KAAc,EAAE;IACnB,OAAO,IAAI;EACb;EACAK,IAAI,CAACL,KAAc,EAAE;IACnB,OAAO,IAAI;EACb;EACAM,cAAc,GAAG;IACf,OAAO,IAAI;EACb;EACAC,KAAK,GAAG;IACN,OAAO,IAAI;EACb;EACAC,MAAM,GAAG;IACP,OAAO,IAAI;EACb;EACAC,KAAK,GAAG;IACN,OAAO,IAAI,CAACb,IAAI;EAClB;AACF;AAEO,SAASc,SAAS,CAACC,OAAsC,EAAU;EACxE,OAAOC,eAAM,CAACC,SAAS,EAAE,GAAG,IAAIvB,OAAO,EAAE,GAAG,IAAAwB,cAAG,EAACH,OAAO,CAAC;AAC1D;AAEO,MAAMI,UAAU,GAAGzB,OAAO;AAAC"}