{"name": "@react-native-community/masked-view", "description": "React Native MaskedView component", "types": "./types/index.d.ts", "main": "index.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "version": "0.1.11", "homepage": "https://github.com/react-native-community/react-native-masked-view#readme", "keywords": ["react-native", "react native", "masked-view", "masked view"], "scripts": {"test:flow": "flow check", "test:js": "echo 0", "test:lint": "eslint ./", "test:prettier": "prettier --check './**/*.js'"}, "peerDependencies": {"react": ">=16.0", "react-native": ">=0.57"}, "files": ["index.js", "android", "ios", "js", "types", "RNCMaskedView.podspec"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.6.4", "@react-native-community/eslint-config": "^0.0.7", "eslint": "^6.6.0", "eslint-plugin-prettier": "^3.1.2", "flow-bin": "^0.106.1", "prettier": "^1.18.2", "react-native": ">=0.57"}, "repository": {"type": "git", "url": "git+https://github.com/react-native-community/react-native-masked-view.git"}, "bugs": {"url": "https://github.com/react-native-community/react-native-masked-view/issues"}}