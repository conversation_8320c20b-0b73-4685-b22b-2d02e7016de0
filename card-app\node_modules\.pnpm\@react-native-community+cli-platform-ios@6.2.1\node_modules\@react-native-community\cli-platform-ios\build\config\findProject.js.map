{"version": 3, "sources": ["../../src/config/findProject.ts"], "names": ["GLOB_PATTERN", "TEST_PROJECTS", "IOS_BASE", "GLOB_EXCLUDE_PATTERN", "findProject", "folder", "projects", "glob", "sync", "cwd", "ignore", "filter", "project", "path", "dirname", "test", "sort", "length"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA,MAAMA,YAAY,GAAG,gBAArB;AAEA;AACA;AACA;;AACA,MAAMC,aAAa,GAAG,sBAAtB;AAEA;AACA;AACA;;AACA,MAAMC,QAAQ,GAAG,KAAjB;AAEA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG,CAAC,qCAAD,CAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACe,SAASC,WAAT,CAAqBC,MAArB,EAAoD;AACjE,QAAMC,QAAQ,GAAGC,gBACdC,IADc,CACTR,YADS,EACK;AAClBS,IAAAA,GAAG,EAAEJ,MADa;AAElBK,IAAAA,MAAM,EAAEP;AAFU,GADL,EAKdQ,MALc,CAMZC,OAAD,IACEC,gBAAKC,OAAL,CAAaF,OAAb,MAA0BV,QAA1B,IAAsC,CAACD,aAAa,CAACc,IAAd,CAAmBH,OAAnB,CAP5B,EASdI,IATc,CASRJ,OAAD,IAAcC,gBAAKC,OAAL,CAAaF,OAAb,MAA0BV,QAA1B,GAAqC,CAAC,CAAtC,GAA0C,CAT/C,CAAjB;;AAWA,MAAII,QAAQ,CAACW,MAAT,KAAoB,CAAxB,EAA2B;AACzB,WAAO,IAAP;AACD;;AAED,SAAOX,QAAQ,CAAC,CAAD,CAAf;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport glob from 'glob';\nimport path from 'path';\n\n/**\n * Glob pattern to look for xcodeproj\n */\nconst GLOB_PATTERN = '**/*.xcodeproj';\n\n/**\n * Regexp matching all test projects\n */\nconst TEST_PROJECTS = /test|example|sample/i;\n\n/**\n * Base iOS folder\n */\nconst IOS_BASE = 'ios';\n\n/**\n * These folders will be excluded from search to speed it up\n */\nconst GLOB_EXCLUDE_PATTERN = ['**/@(Pods|node_modules|Carthage)/**'];\n\n/**\n * Finds iOS project by looking for all .xcodeproj files\n * in given folder.\n *\n * Returns first match if files are found or null\n *\n * Note: `./ios/*.xcodeproj` are returned regardless of the name\n */\nexport default function findProject(folder: string): string | null {\n  const projects = glob\n    .sync(GLOB_PATTERN, {\n      cwd: folder,\n      ignore: GLOB_EXCLUDE_PATTERN,\n    })\n    .filter(\n      (project) =>\n        path.dirname(project) === IOS_BASE || !TEST_PROJECTS.test(project),\n    )\n    .sort((project) => (path.dirname(project) === IOS_BASE ? -1 : 1));\n\n  if (projects.length === 0) {\n    return null;\n  }\n\n  return projects[0];\n}\n"]}