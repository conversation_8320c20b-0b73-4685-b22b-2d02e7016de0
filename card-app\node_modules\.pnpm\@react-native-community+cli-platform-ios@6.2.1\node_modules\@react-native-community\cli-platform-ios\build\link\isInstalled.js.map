{"version": 3, "sources": ["../../src/link/isInstalled.ts"], "names": ["memo", "Map", "isInstalled", "projectConfig", "dependencyConfig", "project", "has", "pbxproj<PERSON><PERSON>", "get", "xcode", "parseSync", "set", "libraries", "libraryFolder", "projectName"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA,MAAMA,IAAI,GAAG,IAAIC,GAAJ,EAAb;AAEA;AACA;AACA;AACA;;AACe,SAASC,WAAT,CACbC,aADa,EAEbC,gBAFa,EAGb;AACA,MAAIC,OAAJ;;AAEA,MAAIL,IAAI,CAACM,GAAL,CAASH,aAAa,CAACI,WAAvB,CAAJ,EAAyC;AACvCF,IAAAA,OAAO,GAAGL,IAAI,CAACQ,GAAL,CAASL,aAAa,CAACI,WAAvB,CAAV;AACD,GAFD,MAEO;AACLF,IAAAA,OAAO,GAAGI,iBAAMJ,OAAN,CAAcF,aAAa,CAACI,WAA5B,EAAyCG,SAAzC,EAAV;AACAV,IAAAA,IAAI,CAACW,GAAL,CAASR,aAAa,CAACI,WAAvB,EAAoCF,OAApC;AACD;;AAED,QAAMO,SAAS,GAAG,uBAASP,OAAT,EAAkBF,aAAa,CAACU,aAAhC,CAAlB;;AAEA,MAAI,CAACD,SAAL,EAAgB;AACd,WAAO,KAAP;AACD;;AAED,SAAO,iCAAmBA,SAAnB,EAA8BR,gBAAgB,CAACU,WAA/C,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport xcode from 'xcode';\nimport getGroup from './getGroup';\nimport hasLibraryImported from './hasLibraryImported';\nimport {\n  IOSProjectConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\n\nconst memo = new Map();\n\n/**\n * Returns true if `xcodeproj` specified by dependencyConfig is present\n * in a top level `libraryFolder`\n */\nexport default function isInstalled(\n  projectConfig: IOSProjectConfig,\n  dependencyConfig: IOSDependencyConfig,\n) {\n  let project;\n\n  if (memo.has(projectConfig.pbxprojPath)) {\n    project = memo.get(projectConfig.pbxprojPath);\n  } else {\n    project = xcode.project(projectConfig.pbxprojPath).parseSync();\n    memo.set(projectConfig.pbxprojPath, project);\n  }\n\n  const libraries = getGroup(project, projectConfig.libraryFolder);\n\n  if (!libraries) {\n    return false;\n  }\n\n  return hasLibraryImported(libraries, dependencyConfig.projectName);\n}\n"]}