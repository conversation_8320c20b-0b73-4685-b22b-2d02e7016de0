{"version": 3, "sources": ["../../src/link/mapHeaderSearchPaths.ts"], "names": ["defaultHeaderPaths", "headerSearchPathIter", "project", "func", "config", "pbxXCBuildConfigurationSection", "Object", "keys", "filter", "ref", "indexOf", "for<PERSON>ach", "buildSettings", "shouldVisitBuildSettings", "Array", "isArray", "OTHER_LDFLAGS", "searchPaths", "HEADER_SEARCH_PATHS", "concat"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,kBAAkB,GAAG,CAAC,gBAAD,CAA3B;;AAEe,SAASC,oBAAT,CACbC,OADa,EAEbC,IAFa,EAGb;AACA,QAAMC,MAAM,GAAGF,OAAO,CAACG,8BAAR,EAAf;AAEAC,EAAAA,MAAM,CAACC,IAAP,CAAYH,MAAZ,EACGI,MADH,CACWC,GAAD,IAASA,GAAG,CAACC,OAAJ,CAAY,UAAZ,MAA4B,CAAC,CADhD,EAEGC,OAFH,CAEYF,GAAD,IAAS;AAChB,UAAM;AAACG,MAAAA;AAAD,QAAkBR,MAAM,CAACK,GAAD,CAA9B;AACA,UAAMI,wBAAwB,GAC5B,CAACC,KAAK,CAACC,OAAN,CAAcH,aAAa,CAACI,aAA5B,IACGJ,aAAa,CAACI,aADjB,GAEG,EAFJ,EAGEN,OAHF,CAGU,SAHV,KAGwB,CAJ1B;;AAMA,QAAIG,wBAAJ,EAA8B;AAC5B,YAAMI,WAAW,GAAGL,aAAa,CAACM,mBAAd,GAChB,GAAGC,MAAH,CAAUP,aAAa,CAACM,mBAAxB,CADgB,GAEhBlB,kBAFJ;AAIAY,MAAAA,aAAa,CAACM,mBAAd,GAAoCf,IAAI,CAACc,WAAD,CAAxC;AACD;AACF,GAjBH;AAkBD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Given Xcode project and path, iterate over all build configurations\n * and execute func with HEADER_SEARCH_PATHS from current section\n *\n * We cannot use builtin addToHeaderSearchPaths method since react-native init does not\n * use $(TARGET_NAME) for PRODUCT_NAME, but sets it manually so that method will skip\n * that target.\n *\n * To workaround that issue and make it more bullet-proof for different names,\n * we iterate over all configurations and look for `lc++` linker flag to detect\n * React Native target.\n *\n * Important: That function mutates `buildSettings` and it's not pure thus you should\n * not rely on its return value\n */\nconst defaultHeaderPaths = ['\"$(inherited)\"'];\n\nexport default function headerSearchPathIter(\n  project: any,\n  func: (searchPaths: Array<string>) => Array<string>,\n) {\n  const config = project.pbxXCBuildConfigurationSection();\n\n  Object.keys(config)\n    .filter((ref) => ref.indexOf('_comment') === -1)\n    .forEach((ref) => {\n      const {buildSettings} = config[ref];\n      const shouldVisitBuildSettings =\n        (Array.isArray(buildSettings.OTHER_LDFLAGS)\n          ? buildSettings.OTHER_LDFLAGS\n          : []\n        ).indexOf('\"-lc++\"') >= 0;\n\n      if (shouldVisitBuildSettings) {\n        const searchPaths = buildSettings.HEADER_SEARCH_PATHS\n          ? [].concat(buildSettings.HEADER_SEARCH_PATHS)\n          : defaultHeaderPaths;\n\n        buildSettings.HEADER_SEARCH_PATHS = func(searchPaths);\n      }\n    });\n}\n"]}