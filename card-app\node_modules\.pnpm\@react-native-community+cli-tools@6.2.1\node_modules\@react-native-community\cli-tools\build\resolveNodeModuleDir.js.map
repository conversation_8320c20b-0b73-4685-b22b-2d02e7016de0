{"version": 3, "sources": ["../src/resolveNodeModuleDir.ts"], "names": ["resolveNodeModuleDir", "root", "packageName", "path", "dirname", "require", "resolve", "join", "paths"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEA;AACA;AACA;AACe,SAASA,oBAAT,CACbC,IADa,EAEbC,WAFa,EAGL;AACR,SAAOC,gBAAKC,OAAL,CACLC,OAAO,CAACC,OAAR,CAAgBH,gBAAKI,IAAL,CAAUL,WAAV,EAAuB,cAAvB,CAAhB,EAAwD;AACtDM,IAAAA,KAAK,EAAE,CAACP,IAAD;AAD+C,GAAxD,CADK,CAAP;AAKD", "sourcesContent": ["import path from 'path';\n\n/**\n * Finds a path inside `node_modules`\n */\nexport default function resolveNodeModuleDir(\n  root: string,\n  packageName: string,\n): string {\n  return path.dirname(\n    require.resolve(path.join(packageName, 'package.json'), {\n      paths: [root],\n    }),\n  );\n}\n"]}