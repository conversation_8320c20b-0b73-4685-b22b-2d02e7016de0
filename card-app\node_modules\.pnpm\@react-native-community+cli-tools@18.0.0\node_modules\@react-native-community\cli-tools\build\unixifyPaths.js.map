{"version": 3, "names": ["unixifyPaths", "path", "replace"], "sources": ["../src/unixifyPaths.ts"], "sourcesContent": ["/**\n *\n * @param path string\n * @returns string\n *\n * This function converts Windows paths to Unix paths.\n */\n\nexport default function unixifyPaths(path: string): string {\n  return path.replace(/^([a-zA-Z]+:|\\.\\/)/, '');\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEe,SAASA,YAAY,CAACC,IAAY,EAAU;EACzD,OAAOA,IAAI,CAACC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;AAC/C"}