{"version": 3, "sources": ["/js/react-native-github/Libraries/BatchedBridge/MessageQueue.js"], "names": ["invariant", "require", "TO_JS", "TO_NATIVE", "MODULE_IDS", "METHOD_IDS", "PARAMS", "MIN_TIME_BETWEEN_FLUSHES_MS", "TRACE_TAG_REACT_APPS", "DEBUG_INFO_LIMIT", "MessageQueue", "MessageQueue::constructor", "_lazyCallableModules", "_queue", "_successCallbacks", "Map", "_failureCallbacks", "_callID", "_lastFlush", "_eventLoopStartTime", "Date", "now", "_immediates<PERSON><PERSON>back", "callFunctionReturnFlushedQueue", "bind", "callFunctionReturnResultAndFlushedQueue", "flushedQueue", "invokeCallbackAndReturnFlushedQueue", "MessageQueue::callFunctionReturnFlushedQueue", "module", "method", "args", "__guard", "_this", "__callFunction", "MessageQueue::callFunctionReturnResultAndFlushedQueue", "result", "_this2", "MessageQueue::invokeCallbackAndReturnFlushedQueue", "cbID", "_this3", "__invokeCallback", "MessageQueue::flushedQueue", "_this4", "__callImmediates", "queue", "length", "MessageQueue::getEventLoopRunningTime", "MessageQueue::registerCallableModule", "name", "MessageQueue::registerLazyCallableModule", "factory", "getValue", "<unnamed_class>::name", "MessageQueue::getCallableModule", "MessageQueue::callNativeSyncHook", "moduleID", "methodID", "params", "onFail", "onSucc", "processCallbacks", "global", "nativeCallSyncHook", "e", "framesToPop", "test", "message", "MessageQueue::processCallbacks", "push", "set", "MessageQueue::enqueueNativeCall", "nativeFlushQueueImmediate", "counterEvent", "__spy", "type", "MessageQueue::createDebugLookup", "methods", "MessageQueue::setImmediatesCallback", "fn", "MessageQueue::__guard", "__shouldPauseOnThrow", "error", "reportFatalError", "MessageQueue::__shouldPauseOnThrow", "DebuggerInternal", "shouldPauseOnThrow", "MessageQueue::__callImmediates", "beginEvent", "endEvent", "MessageQueue::__callFunction", "moduleMethods", "getCallableModule", "undefined", "apply", "MessageQueue::__invokeCallback", "callID", "isSuccess", "callback", "get", "delete", "babelHelpers", "toConsumableArray", "MessageQueue::spy", "spyOrToggle", "prototype", "MessageQueue::__spy", "info", "console", "log", "JSON", "stringify", "exports"], "mappings": "AAUA;;AAMA,IAAMA,SAAS,GAAGC,OAAO,yCAAzB;;AAUA,IAAMC,KAAK,GAAG,CAAd;AACA,IAAMC,SAAS,GAAG,CAAlB;AAEA,IAAMC,UAAU,GAAG,CAAnB;AACA,IAAMC,UAAU,GAAG,CAAnB;AACA,IAAMC,MAAM,GAAG,CAAf;AACA,IAAMC,2BAA2B,GAAG,CAApC;AAGA,IAAMC,oBAAoB,GAAA,MAA1B;AAEA,IAAMC,gBAAgB,GAAG,EAAzB;;IAEMC,YAAAA,GAAAA,YAAAA;AAgBJC,WAAAA,YAAAA,GAAcA;AAAAA,IAAAA,YAAAA,CAAAA,cAAAA,CAAAA,IAAAA,EAAAA,YAAAA;AACZ,SAAKC,EAAL,GAA4B,EAA5B;AACA,SAAKC,EAAL,GAAc,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,CAAb,CAAd;AACA,SAAKC,EAAL,GAAyB,IAAIC,GAAJ,EAAzB;AACA,SAAKC,EAAL,GAAyB,IAAID,GAAJ,EAAzB;AACA,SAAKE,EAAL,GAAe,CAAf;AACA,SAAKC,EAAL,GAAkB,CAAlB;AACA,SAAKC,EAAL,GAA2BC,IAAI,CAACC,GAALD,EAA3B;AACA,SAAKE,EAAL,GAA2B,IAA3B;AAQC,SAAWC,8BAAX,GAA4C,KAAKA,8BAAL,CAAoCC,IAApC,CAC3C,IAD2C,CAA5C;AAGA,SAAWC,uCAAX,GAAqD,KAAKA,uCAAL,CAA6CD,IAA7C,CACpD,IADoD,CAArD;AAGA,SAAWE,YAAX,GAA0B,KAAKA,YAAL,CAAkBF,IAAlB,CAAuB,IAAvB,CAA1B;AACA,SAAWG,mCAAX,GAAiD,KAAKA,mCAAL,CAAyCH,IAAzC,CAChD,IADgD,CAAjD;AAGF;;;;WAsB2EI,SAAAA,8BAAAA,CAA7CC,MAA6CD,EAA7BE,MAA6BF,EAAbG,IAAaH,EAAAA;AAAAA,UAAAA,KAAAA,GAAAA,IAAAA;;AAC1E,WAAKI,OAAL,CAAa,YAAM;AACjBC,QAAAA,KAAI,CAACC,cAALD,CAAoBJ,MAApBI,EAA4BH,MAA5BG,EAAoCF,IAApCE;AADF,OAAA;;AAIA,aAAO,KAAKP,YAAL,EAAP;AACD;;;WAMCS,SAAAA,uCAAAA,CAHAN,MAGAM,EAFAL,MAEAK,EADAJ,IACAI,EAAAA;AAAAA,UAAAA,MAAAA,GAAAA,IAAAA;;AACA,UAAIC,MAAJ;;AACA,WAAKJ,OAAL,CAAa,YAAM;AACjBI,QAAAA,MAAM,GAAGC,MAAI,CAACH,cAALG,CAAoBR,MAApBQ,EAA4BP,MAA5BO,EAAoCN,IAApCM,CAATD;AADF,OAAA;;AAIA,aAAO,CAACA,MAAD,EAAS,KAAKV,YAAL,EAAT,CAAP;AACD;;;WAE8DY,SAAAA,mCAAAA,CAA3BC,IAA2BD,EAAbP,IAAaO,EAAAA;AAAAA,UAAAA,MAAAA,GAAAA,IAAAA;;AAC7D,WAAKN,OAAL,CAAa,YAAM;AACjBQ,QAAAA,MAAI,CAACC,gBAALD,CAAsBD,IAAtBC,EAA4BT,IAA5BS;AADF,OAAA;;AAIA,aAAO,KAAKd,YAAL,EAAP;AACD;;;WAEcgB,SAAAA,YAAAA,GAAAA;AAAAA,UAAAA,MAAAA,GAAAA,IAAAA;;AACb,WAAKV,OAAL,CAAa,YAAM;AACjBW,QAAAA,MAAI,CAACC,gBAALD;AADF,OAAA;;AAIA,UAAME,KAAK,GAAG,KAAKhC,EAAnB;AACA,WAAKA,EAAL,GAAc,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,KAAKI,EAAlB,CAAd;AACA,aAAO4B,KAAK,CAAC,CAAD,CAALA,CAASC,MAATD,GAAkBA,KAAlBA,GAA0B,IAAjC;AACD;;;WAEyBE,SAAAA,uBAAAA,GAAAA;AACxB,aAAO3B,IAAI,CAACC,GAALD,KAAa,KAAKD,EAAzB;AACD;;;WAEoD6B,SAAAA,sBAAAA,CAA9BC,IAA8BD,EAAhBnB,MAAgBmB,EAAAA;AACnD,WAAKpC,EAAL,CAA0BqC,IAA1B,IAAkC,YAAA;AAAA,eAAMpB,MAAN;AAAlC,OAAA;AACD;;;WAEiEqB,SAAAA,0BAAAA,CAAvCD,IAAuCC,EAAzBC,OAAyBD,EAAAA;AAChE,UAAIrB,MAAJ;AACA,UAAIuB,QAA2B,GAAGD,OAAlC;;AACA,WAAKvC,EAAL,CAA0BqC,IAA1B,IAAkCI,YAAMA;AACtC,YAAID,QAAJ,EAAc;AACZvB,UAAAA,MAAM,GAAGuB,QAAQ,EAAjBvB;AACAuB,UAAAA,QAAQ,GAAG,IAAXA;AACD;;AACD,eAAOvB,MAAP;AALF,OAAA;AAOD;;;WAE+ByB,SAAAA,iBAAAA,CAAdL,IAAcK,EAAAA;AAC9B,UAAMF,QAAQ,GAAG,KAAKxC,EAAL,CAA0BqC,IAA1B,CAAjB;AACA,aAAOG,QAAQ,GAAGA,QAAQ,EAAX,GAAgB,IAA/B;AACD;;;WAQCG,SAAAA,kBAAAA,CALAC,QAKAD,EAJAE,QAIAF,EAHAG,MAGAH,EAFAI,MAEAJ,EADAK,MACAL,EAAAA;AAUA,WAAKM,gBAAL,CAAsBL,QAAtB,EAAgCC,QAAhC,EAA0CC,MAA1C,EAAkDC,MAAlD,EAA0DC,MAA1D;;AACA,UAAI;AACF,eAAOE,MAAM,CAACC,kBAAPD,CAA0BN,QAA1BM,EAAoCL,QAApCK,EAA8CJ,MAA9CI,CAAP;AADF,OAAA,CAEE,OAAOE,CAAP,EAAU;AACV,YACE,OAAOA,CAAP,KAAa,QAAb,IACAA,CAAC,IAAI,IADL,IAEA,OAAOA,CAAC,CAACC,WAAT,KAAyB,WAFzB,IAGA,+BAA+BC,IAA/B,CAAoCF,CAAC,CAACG,OAAtC,CAJF,EAKE;AACAH,UAAAA,CAAC,CAACC,WAAFD,GAAgB,CAAhBA;AACD;;AACD,cAAMA,CAAN;AACD;AACF;;;WAQCI,SAAAA,gBAAAA,CALAZ,QAKAY,EAJAX,QAIAW,EAHAV,MAGAU,EAFAT,MAEAS,EADAR,MACAQ,EAAAA;AAAAA,UAAAA,MAAAA,GAAAA,IAAAA;;AACA,UAAIT,MAAM,IAAIC,MAAd,EAAsB;AAyBpBD,QAAAA,MAAM,IAAID,MAAM,CAACW,IAAPX,CAAY,KAAKzC,EAAL,IAAgB,CAA5ByC,CAAVC;AAEAC,QAAAA,MAAM,IAAIF,MAAM,CAACW,IAAPX,CAAa,KAAKzC,EAAL,IAAgB,CAAhB,GAAqB,CAAlCyC,CAAVE;AACA,aAAK9C,EAAL,CAAuBwD,GAAvB,CAA2B,KAAKrD,EAAhC,EAAyC2C,MAAzC;AACA,aAAK5C,EAAL,CAAuBsD,GAAvB,CAA2B,KAAKrD,EAAhC,EAAyC0C,MAAzC;AACD;;AASD,WAAK1C,EAAL;AACD;;;WAQCsD,SAAAA,iBAAAA,CALAf,QAKAe,EAJAd,QAIAc,EAHAb,MAGAa,EAFAZ,MAEAY,EADAX,MACAW,EAAAA;AACA,WAAKV,gBAAL,CAAsBL,QAAtB,EAAgCC,QAAhC,EAA0CC,MAA1C,EAAkDC,MAAlD,EAA0DC,MAA1D;AAEA,WAAK/C,EAAL,CAAYT,UAAZ,EAAwBiE,IAAxB,CAA6Bb,QAA7B;AACA,WAAK3C,EAAL,CAAYR,UAAZ,EAAwBgE,IAAxB,CAA6BZ,QAA7B;AA0DA,WAAK5C,EAAL,CAAYP,MAAZ,EAAoB+D,IAApB,CAAyBX,MAAzB;AAEA,UAAMrC,GAAG,GAAGD,IAAI,CAACC,GAALD,EAAZ;;AACA,UACE0C,MAAM,CAACU,yBAAPV,IACAzC,GAAG,GAAG,KAAKH,EAAXG,IAAyBd,2BAF3B,EAGE;AACA,YAAMsC,KAAK,GAAG,KAAKhC,EAAnB;AACA,aAAKA,EAAL,GAAc,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,KAAKI,EAAlB,CAAd;AACA,aAAKC,EAAL,GAAkBG,GAAlB;AACAyC,QAAAA,MAAM,CAACU,yBAAPV,CAAiCjB,KAAjCiB;AACD;;AACD7D,MAAAA,OAAA,6DAAAA,CAASwE,YAATxE,CAAsB,4BAAtBA,EAAoD,KAAKY,EAAL,CAAY,CAAZ,EAAeiC,MAAnE7C;;AAQO,UAAI,KAAKyE,KAAT,EAAgB;AACrB,aAAKA,KAAL,CAAW;AACTC,UAAAA,IAAI,EAAExE,SADG;AAET0B,UAAAA,MAAM,EAAE2B,QAAQ,GAAG,EAFV;AAGT1B,UAAAA,MAAM,EAAE2B,QAHC;AAIT1B,UAAAA,IAAI,EAAE2B;AAJG,SAAX;AAMD;AACF;;;WAMCkB,SAAAA,iBAAAA,CAHApB,QAGAoB,EAFA3B,IAEA2B,EADAC,OACAD,EAAAA,CAKD;;;WAKqCE,SAAAA,qBAAAA,CAAhBC,EAAgBD,EAAAA;AACpC,WAAKxD,EAAL,GAA2ByD,EAA3B;AACD;;;WAMuBC,SAAAA,OAAAA,CAAhBD,EAAgBC,EAAAA;AACtB,UAAI,KAAKC,oBAAL,EAAJ,EAAiC;AAC/BF,QAAAA,EAAE;AADJ,OAAA,MAEO;AACL,YAAI;AACFA,UAAAA,EAAE;AADJ,SAAA,CAEE,OAAOG,KAAP,EAAc;AACdjF,UAAAA,OAAA,+DAAAA,CAAWkF,gBAAXlF,CAA4BiF,KAA5BjF;AACD;AACF;AACF;;;WAOsBmF,SAAAA,oBAAAA,GAAAA;AACrB,aAEE,OAAOC,gBAAP,KAA4B,WAA5B,IACAA,gBAAgB,CAACC,kBAAjBD,KAAwC,IAH1C;AAKD;;;WAEkBE,SAAAA,gBAAAA,GAAAA;AACjBtF,MAAAA,OAAA,6DAAAA,CAASuF,UAATvF,CAAoB,2BAApBA;;AACA,UAAI,KAAKqB,EAAL,IAA4B,IAAhC,EAAsC;AACpC,aAAKA,EAAL;AACD;;AACDrB,MAAAA,OAAA,6DAAAA,CAASwF,QAATxF;AACD;;;WAEgEyF,SAAAA,cAAAA,CAAlD7D,MAAkD6D,EAAlC5D,MAAkC4D,EAAlB3D,IAAkB2D,EAAAA;AAC/D,WAAKxE,EAAL,GAAkBE,IAAI,CAACC,GAALD,EAAlB;AACA,WAAKD,EAAL,GAA2B,KAAKD,EAAhC;;AACA,UAAe,KAAKwD,KAApB,EAA2B;AACzBzE,QAAAA,OAAA,6DAAAA,CAASuF,UAATvF,CAAuB4B,MAAvB,GAAA,GAAuBA,GAAUC,MAAVD,GAAvB,GAAuBA,GAAoB5B,OAAA,gEAAAA,CAAc8B,IAAd9B,CAApB4B,GAAvB,GAAA5B;AADF,OAAA,MAEO;AACLA,QAAAA,OAAA,6DAAAA,CAASuF,UAATvF,CAAuB4B,MAAvB,GAAA,GAAuBA,GAAUC,MAAVD,GAAvB,OAAA5B;AACD;;AACD,UAAI,KAAKyE,KAAT,EAAgB;AACd,aAAKA,KAAL,CAAW;AAACC,UAAAA,IAAI,EAAEzE,KAAP;AAAc2B,UAAAA,MAAM,EAANA,MAAd;AAAsBC,UAAAA,MAAM,EAANA,MAAtB;AAA8BC,UAAAA,IAAI,EAAJA;AAA9B,SAAX;AACD;;AACD,UAAM4D,aAAa,GAAG,KAAKC,iBAAL,CAAuB/D,MAAvB,CAAtB;AACA,OACE,CAAC,CAAC8D,aADJ,GAAA3F,SAAA,CAAA,CAAA,EAEE,4DAFF,EAGE6B,MAHF,EAIEC,MAJF,CAAA,GAAA+D,SAAA;AAMA,OACE,CAAC,CAACF,aAAa,CAAC7D,MAAD,CADjB,GAAA9B,SAAA,CAAA,CAAA,EAEE,uCAFF,EAGE8B,MAHF,EAIED,MAJF,CAAA,GAAAgE,SAAA;AAMA,UAAMzD,MAAM,GAAGuD,aAAa,CAAC7D,MAAD,CAAb6D,CAAsBG,KAAtBH,CAA4BA,aAA5BA,EAA2C5D,IAA3C4D,CAAf;;AACA1F,MAAAA,OAAA,6DAAAA,CAASwF,QAATxF;;AACA,aAAOmC,MAAP;AACD;;;WAE2C2D,SAAAA,gBAAAA,CAA3BxD,IAA2BwD,EAAbhE,IAAagE,EAAAA;AAC1C,WAAK7E,EAAL,GAAkBE,IAAI,CAACC,GAALD,EAAlB;AACA,WAAKD,EAAL,GAA2B,KAAKD,EAAhC;AAIA,UAAM8E,MAAM,GAAGzD,IAAI,KAAK,CAAxB;AAEA,UAAM0D,SAAS,GAAG1D,IAAI,GAAG,CAAzB;AACA,UAAM2D,QAAQ,GAAGD,SAAS,GACtB,KAAKnF,EAAL,CAAuBqF,GAAvB,CAA2BH,MAA3B,CADsB,GAEtB,KAAKhF,EAAL,CAAuBmF,GAAvB,CAA2BH,MAA3B,CAFJ;;AA2BA,UAAI,CAACE,QAAL,EAAe;AACb;AACD;;AAED,WAAKpF,EAAL,CAAuBsF,MAAvB,CAA8BJ,MAA9B;AACA,WAAKhF,EAAL,CAAuBoF,MAAvB,CAA8BJ,MAA9B;AACAE,MAAAA,QAAQ,CAAAJ,KAARI,CAAAA,SAAAA,EAAAG,YAAA,CAAAC,iBAAAD,CAAYtE,IAAZsE,CAAAH;AAKD;;;WAtY4DK,SAAAA,GAAAA,CAAlDC,WAAkDD,EAAAA;AAC3D,UAAIC,WAAW,KAAK,IAApB,EAA0B;AACxB9F,QAAAA,YAAY,CAAC+F,SAAb/F,CAAuBgE,KAAvBhE,GAA+BgG,UAAAC,IAAAD,EAAQA;AACrCE,UAAAA,OAAO,CAACC,GAARD,CACE,CAAGD,IAAI,CAAChC,IAALgC,KAAczG,KAAdyG,GAAsB,OAAtBA,GAAgC,OAAnC,IAAA,KAAA,IAAA,MACKA,IAAI,CAAC9E,MAAL8E,GAAcA,IAAI,CAAC9E,MAAL8E,GAAc,GAA5BA,GAAkC,EADvC,IAC4CA,IAAI,CAAC7E,MADjD,KAAA,MAEMgF,IAAI,CAACC,SAALD,CAAeH,IAAI,CAAC5E,IAApB+E,CAFN,GAAA,GAAA,CADFF;AADFlG,SAAAA;AADF,OAAA,MAQO,IAAI8F,WAAW,KAAK,KAApB,EAA2B;AAChC9F,QAAAA,YAAY,CAAC+F,SAAb/F,CAAuBgE,KAAvBhE,GAA+B,IAA/BA;AADK,OAAA,MAEA;AACLA,QAAAA,YAAY,CAAC+F,SAAb/F,CAAuBgE,KAAvBhE,GAA+B8F,WAA/B9F;AACD;AACF;;;CA9DGA;;AAybNmB,MAAM,CAACmF,OAAPnF,GAAiBnB,YAAjBmB"}