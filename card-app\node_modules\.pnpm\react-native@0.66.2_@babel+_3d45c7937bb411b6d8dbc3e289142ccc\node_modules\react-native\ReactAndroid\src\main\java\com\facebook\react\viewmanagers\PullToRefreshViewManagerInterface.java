/**
* Copyright (c) Facebook, Inc. and its affiliates.
*
* This source code is licensed under the MIT license found in the
* LICENSE file in the root directory of this source tree.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;

public interface PullToRefreshViewManagerInterface<T extends View> {
  void setTintColor(T view, @Nullable Integer value);
  void setTitleColor(T view, @Nullable Integer value);
  void setTitle(T view, @Nullable String value);
  void setRefreshing(T view, boolean value);
  void setNativeRefreshing(T view, boolean refreshing);
}
