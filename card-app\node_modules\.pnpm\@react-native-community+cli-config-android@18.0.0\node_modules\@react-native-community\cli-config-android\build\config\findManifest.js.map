{"version": 3, "names": ["findManifest", "folder", "manifestPaths", "glob", "sync", "cwd", "unixifyPaths", "ignore", "length", "mainManifest", "filter", "manifestPath", "includes", "path", "join"], "sources": ["../../src/config/findManifest.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport glob from 'fast-glob';\nimport path from 'path';\nimport {unixifyPaths} from '@react-native-community/cli-tools';\n\nexport default function findManifest(folder: string) {\n  let manifestPaths = glob.sync('**/AndroidManifest.xml', {\n    cwd: unixifyPaths(folder),\n    ignore: [\n      'node_modules/**',\n      '**/build/**',\n      '**/debug/**',\n      'Examples/**',\n      'examples/**',\n      '**/Pods/**',\n      '**/sdks/hermes/android/**',\n      '**/src/androidTest/**',\n      '**/src/test/**',\n    ],\n  });\n  if (manifestPaths.length > 1) {\n    // if we have more than one manifest, pick the one in the main folder if present\n    const mainManifest = manifestPaths.filter((manifestPath) =>\n      manifestPath.includes('src/main/'),\n    );\n    if (mainManifest.length === 1) {\n      manifestPaths = mainManifest;\n    }\n  }\n\n  return manifestPaths[0] ? path.join(folder, manifestPaths[0]) : null;\n}\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+D;AAV/D;AACA;AACA;AACA;AACA;AACA;AACA;;AAMe,SAASA,YAAY,CAACC,MAAc,EAAE;EACnD,IAAIC,aAAa,GAAGC,mBAAI,CAACC,IAAI,CAAC,wBAAwB,EAAE;IACtDC,GAAG,EAAE,IAAAC,wBAAY,EAACL,MAAM,CAAC;IACzBM,MAAM,EAAE,CACN,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,aAAa,EACb,aAAa,EACb,YAAY,EACZ,2BAA2B,EAC3B,uBAAuB,EACvB,gBAAgB;EAEpB,CAAC,CAAC;EACF,IAAIL,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;IAC5B;IACA,MAAMC,YAAY,GAAGP,aAAa,CAACQ,MAAM,CAAEC,YAAY,IACrDA,YAAY,CAACC,QAAQ,CAAC,WAAW,CAAC,CACnC;IACD,IAAIH,YAAY,CAACD,MAAM,KAAK,CAAC,EAAE;MAC7BN,aAAa,GAAGO,YAAY;IAC9B;EACF;EAEA,OAAOP,aAAa,CAAC,CAAC,CAAC,GAAGW,eAAI,CAACC,IAAI,CAACb,MAAM,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACtE"}