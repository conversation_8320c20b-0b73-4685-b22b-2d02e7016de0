load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "rn_android_library")

rn_android_library(
    name = "logging",
    srcs = glob(["**/*.java"]),
    autoglob = False,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
    ],
    exported_deps = [
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
    ],
)
