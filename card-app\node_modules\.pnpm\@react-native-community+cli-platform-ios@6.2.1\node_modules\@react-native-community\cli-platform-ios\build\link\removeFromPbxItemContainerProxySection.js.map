{"version": 3, "sources": ["../../src/link/removeFromPbxItemContainerProxySection.ts"], "names": ["removeFromPbxItemContainerProxySection", "project", "file", "section", "hash", "objects", "PBXContainerItemProxy", "key", "Object", "keys", "containerPortal", "uuid"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACe,SAASA,sCAAT,CACbC,OADa,EAEbC,IAFa,EAGb;AACA,QAAMC,OAAO,GAAGF,OAAO,CAACG,IAAR,CAAaH,OAAb,CAAqBI,OAArB,CAA6BC,qBAA7C;;AAEA,OAAK,MAAMC,GAAX,IAAkBC,MAAM,CAACC,IAAP,CAAYN,OAAZ,CAAlB,EAAwC;AACtC,QAAIA,OAAO,CAACI,GAAD,CAAP,CAAaG,eAAb,KAAiCR,IAAI,CAACS,IAA1C,EAAgD;AAC9C,aAAOR,OAAO,CAACI,GAAD,CAAd;AACD;AACF;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * For all files that are created and referenced from another `.xcodeproj` -\n * a new PBXItemContainerProxy is created that contains `containerPortal` value\n * which equals to xcodeproj file.uuid from PBXFileReference section.\n */\nexport default function removeFromPbxItemContainerProxySection(\n  project: any,\n  file: any,\n) {\n  const section = project.hash.project.objects.PBXContainerItemProxy;\n\n  for (const key of Object.keys(section)) {\n    if (section[key].containerPortal === file.uuid) {\n      delete section[key];\n    }\n  }\n}\n"]}