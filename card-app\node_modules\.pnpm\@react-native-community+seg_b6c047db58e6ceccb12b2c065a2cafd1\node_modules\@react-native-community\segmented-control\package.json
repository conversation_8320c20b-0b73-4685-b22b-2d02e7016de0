{"name": "@react-native-community/segmented-control", "version": "2.2.2", "description": "React Native SegmentedControlIOS library", "main": "js/index.js", "types": "index.d.ts", "files": ["index.d.ts", "ios", "js", "react-native-segmented-control.podspec"], "scripts": {"start": "react-native start", "test": "yarn flow && yarn lint", "ios": "cd example && react-native run-ios", "web": "expo web", "android": "react-native run-android", "flow": "flow", "lint": "eslint . --cache", "tsc": "tsc --noEmit"}, "peerDependencies": {"react": ">=16.0", "react-native": ">=0.62"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/runtime": "^7.10.2", "@expo/webpack-config": "^0.12.22", "@react-native-community/eslint-config": "^2.0.0", "@semantic-release/git": "^8.0.0", "@types/react": "^16.9.17", "@types/react-native": "^0.62.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.0.1", "babel-preset-expo": "^8.2.1", "eslint": "7", "eslint-plugin-prettier": "^3.1.2", "expo": "^38.0.10", "flow-bin": "^0.129.0", "husky": "^4.0.10", "jest": "^26.0.1", "metro-react-native-babel-preset": "0.59.0", "react": "16.13.1", "react-dom": "16.13.1", "react-native": "0.63.2", "react-native-web": "^0.13.12", "react-test-renderer": "16.13.1", "rimraf": "^3.0.2", "semantic-release": "^17.0.4", "typescript": "^3.8.3"}, "resolutions": {"@react-native-community/cli": "4.10.0"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/react-native-community/segmented-control.git"}, "keywords": ["SegmentedControlIOS", "react-native"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/react-native-community/segmented-control/issues"}, "homepage": "https://github.com/react-native-community/segmented-control#readme"}