{"version": 3, "sources": ["../../../src/commands/runAndroid/tryLaunchEmulator.ts"], "names": ["emulatorCommand", "process", "env", "ANDROID_HOME", "getEmulators", "emulatorsOutput", "execa", "sync", "stdout", "split", "os", "EOL", "filter", "name", "launchEmulator", "emulator<PERSON>ame", "adbPath", "Promise", "resolve", "reject", "cp", "detached", "stdio", "unref", "timeout", "rejectTimeout", "setTimeout", "cleanup", "bootCheckInterval", "setInterval", "Adb", "getDevices", "length", "clearTimeout", "clearInterval", "on", "error", "message", "tryLaunchEmulator", "emulators", "success"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAEA,MAAMA,eAAe,GAAGC,OAAO,CAACC,GAAR,CAAYC,YAAZ,GACnB,GAAEF,OAAO,CAACC,GAAR,CAAYC,YAAa,oBADR,GAEpB,UAFJ;;AAIA,MAAMC,YAAY,GAAG,MAAM;AACzB,MAAI;AACF,UAAMC,eAAe,GAAGC,iBAAMC,IAAN,CAAWP,eAAX,EAA4B,CAAC,YAAD,CAA5B,EAA4CQ,MAApE;;AACA,WAAOH,eAAe,CAACI,KAAhB,CAAsBC,cAAGC,GAAzB,EAA8BC,MAA9B,CAAsCC,IAAD,IAAUA,IAAI,KAAK,EAAxD,CAAP;AACD,GAHD,CAGE,MAAM;AACN,WAAO,EAAP;AACD;AACF,CAPD;;AASA,MAAMC,cAAc,GAAG,OAAOC,YAAP,EAA6BC,OAA7B,KAAiD;AACtE,SAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtC,UAAMC,EAAE,GAAG,sBAAMpB,eAAN,EAAuB,CAAE,IAAGe,YAAa,EAAlB,CAAvB,EAA6C;AACtDM,MAAAA,QAAQ,EAAE,IAD4C;AAEtDC,MAAAA,KAAK,EAAE;AAF+C,KAA7C,CAAX;AAIAF,IAAAA,EAAE,CAACG,KAAH;AACA,UAAMC,OAAO,GAAG,EAAhB,CANsC,CAQtC;;AACA,UAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;AACrCC,MAAAA,OAAO;AACPR,MAAAA,MAAM,CAAE,mCAAkCK,OAAQ,WAA5C,CAAN;AACD,KAH+B,EAG7BA,OAAO,GAAG,IAHmB,CAAhC;AAKA,UAAMI,iBAAiB,GAAGC,WAAW,CAAC,MAAM;AAC1C,UAAIC,aAAIC,UAAJ,CAAef,OAAf,EAAwBgB,MAAxB,GAAiC,CAArC,EAAwC;AACtCL,QAAAA,OAAO;AACPT,QAAAA,OAAO;AACR;AACF,KALoC,EAKlC,IALkC,CAArC;;AAOA,UAAMS,OAAO,GAAG,MAAM;AACpBM,MAAAA,YAAY,CAACR,aAAD,CAAZ;AACAS,MAAAA,aAAa,CAACN,iBAAD,CAAb;AACD,KAHD;;AAKAR,IAAAA,EAAE,CAACe,EAAH,CAAM,MAAN,EAAc,MAAM;AAClBR,MAAAA,OAAO;AACPR,MAAAA,MAAM,CAAC,8BAAD,CAAN;AACD,KAHD;AAKAC,IAAAA,EAAE,CAACe,EAAH,CAAM,OAAN,EAAgBC,KAAD,IAAW;AACxBT,MAAAA,OAAO;AACPR,MAAAA,MAAM,CAACiB,KAAK,CAACC,OAAP,CAAN;AACD,KAHD;AAID,GAnCM,CAAP;AAoCD,CArCD;;AAuCe,eAAeC,iBAAf,CACbtB,OADa,EAEgC;AAC7C,QAAMuB,SAAS,GAAGnC,YAAY,EAA9B;;AACA,MAAImC,SAAS,CAACP,MAAV,GAAmB,CAAvB,EAA0B;AACxB,QAAI;AACF,YAAMlB,cAAc,CAACyB,SAAS,CAAC,CAAD,CAAV,EAAevB,OAAf,CAApB;AACA,aAAO;AAACwB,QAAAA,OAAO,EAAE;AAAV,OAAP;AACD,KAHD,CAGE,OAAOJ,KAAP,EAAc;AACd,aAAO;AAACI,QAAAA,OAAO,EAAE,KAAV;AAAiBJ,QAAAA;AAAjB,OAAP;AACD;AACF;;AACD,SAAO;AACLI,IAAAA,OAAO,EAAE,KADJ;AAELJ,IAAAA,KAAK,EAAE;AAFF,GAAP;AAID", "sourcesContent": ["import os from 'os';\nimport execa from 'execa';\nimport Adb from './adb';\n\nconst emulatorCommand = process.env.ANDROID_HOME\n  ? `${process.env.ANDROID_HOME}/emulator/emulator`\n  : 'emulator';\n\nconst getEmulators = () => {\n  try {\n    const emulatorsOutput = execa.sync(emulatorCommand, ['-list-avds']).stdout;\n    return emulatorsOutput.split(os.EOL).filter((name) => name !== '');\n  } catch {\n    return [];\n  }\n};\n\nconst launchEmulator = async (emulatorName: string, adbPath: string) => {\n  return new Promise((resolve, reject) => {\n    const cp = execa(emulatorCommand, [`@${emulatorName}`], {\n      detached: true,\n      stdio: 'ignore',\n    });\n    cp.unref();\n    const timeout = 30;\n\n    // Reject command after timeout\n    const rejectTimeout = setTimeout(() => {\n      cleanup();\n      reject(`Could not start emulator within ${timeout} seconds.`);\n    }, timeout * 1000);\n\n    const bootCheckInterval = setInterval(() => {\n      if (Adb.getDevices(adbPath).length > 0) {\n        cleanup();\n        resolve();\n      }\n    }, 1000);\n\n    const cleanup = () => {\n      clearTimeout(rejectTimeout);\n      clearInterval(bootCheckInterval);\n    };\n\n    cp.on('exit', () => {\n      cleanup();\n      reject('Emulator exited before boot.');\n    });\n\n    cp.on('error', (error) => {\n      cleanup();\n      reject(error.message);\n    });\n  });\n};\n\nexport default async function tryLaunchEmulator(\n  adbPath: string,\n): Promise<{success: boolean; error?: string}> {\n  const emulators = getEmulators();\n  if (emulators.length > 0) {\n    try {\n      await launchEmulator(emulators[0], adbPath);\n      return {success: true};\n    } catch (error) {\n      return {success: false, error};\n    }\n  }\n  return {\n    success: false,\n    error: 'No emulators found as an output of `emulator -list-avds`',\n  };\n}\n"]}