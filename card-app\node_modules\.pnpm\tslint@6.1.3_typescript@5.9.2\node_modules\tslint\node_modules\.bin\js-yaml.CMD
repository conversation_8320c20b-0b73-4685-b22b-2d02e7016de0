@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules\js-yaml\bin\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules\js-yaml\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules\js-yaml\bin\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules\js-yaml\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\js-yaml@3.14.1\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\js-yaml@3.14.1\node_modules\js-yaml\bin\js-yaml.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\js-yaml@3.14.1\node_modules\js-yaml\bin\js-yaml.js" %*
)
