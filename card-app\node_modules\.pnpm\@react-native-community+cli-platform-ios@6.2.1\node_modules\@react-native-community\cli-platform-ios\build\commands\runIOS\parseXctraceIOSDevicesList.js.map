{"version": 3, "sources": ["../../../src/commands/runIOS/parseXctraceIOSDevicesList.ts"], "names": ["parseIOSDevicesList", "text", "devices", "isSimulator", "indexOf", "split", "for<PERSON>ach", "line", "device", "match", "name", "version", "udid", "metadata", "type", "push"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAT,CAA6BC,IAA7B,EAA0D;AACxD,QAAMC,OAAsB,GAAG,EAA/B;AACA,MAAIC,WAAW,GAAG,KAAlB;;AACA,MAAIF,IAAI,CAACG,OAAL,CAAa,kBAAb,MAAqC,CAAC,CAA1C,EAA6C;AAC3C,WAAO,EAAP;AACD;;AACDH,EAAAA,IAAI,CAACI,KAAL,CAAW,IAAX,EAAiBC,OAAjB,CAA0BC,IAAD,IAAU;AACjC,QAAIA,IAAI,KAAK,kBAAb,EAAiC;AAC/BJ,MAAAA,WAAW,GAAG,IAAd;AACD;;AACD,UAAMK,MAAM,GAAGD,IAAI,CAACE,KAAL,CAAW,0CAAX,CAAf;;AACA,QAAID,MAAJ,EAAY;AACV,YAAM,GAAGE,IAAH,GAAWC,OAAX,EAAoBC,IAApB,IAA4BJ,MAAlC;AACA,YAAMK,QAAgB,GAAG;AAACH,QAAAA,IAAD;AAAOE,QAAAA;AAAP,OAAzB;;AACA,UAAID,OAAJ,EAAa;AACXE,QAAAA,QAAQ,CAACF,OAAT,GAAmBA,OAAnB;AACAE,QAAAA,QAAQ,CAACC,IAAT,GAAgBX,WAAW,GAAG,WAAH,GAAiB,QAA5C;AACD,OAHD,MAGO;AACLU,QAAAA,QAAQ,CAACC,IAAT,GAAgB,UAAhB;AACD;;AACDZ,MAAAA,OAAO,CAACa,IAAR,CAAaF,QAAb;AACD;AACF,GAhBD;AAkBA,SAAOX,OAAP;AACD;;eAEcF,mB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport {Device} from '../../types';\n\n/**\n * Parses the output of the `xcrun instruments -s` command and returns metadata\n * about available iOS simulators and physical devices, as well as host Mac for\n * Catalyst purposes.\n *\n * Expected text looks roughly like this:\n *\n * ```\n * == Devices ==\n * this-mac-device [UDID]\n * A Physical Device (OS Version) (UDID)\n *\n * == Simulators ==\n * A Simulator Device (OS Version) (UDID)\n * ```\n */\nfunction parseIOSDevicesList(text: string): Array<Device> {\n  const devices: Array<Device> = [];\n  let isSimulator = false;\n  if (text.indexOf('== Simulators ==') === -1) {\n    return [];\n  }\n  text.split('\\n').forEach((line) => {\n    if (line === '== Simulators ==') {\n      isSimulator = true;\n    }\n    const device = line.match(/(.*?) (\\(([0-9.]+)\\) )?\\(([0-9A-F-]+)\\)/i);\n    if (device) {\n      const [, name, , version, udid] = device;\n      const metadata: Device = {name, udid};\n      if (version) {\n        metadata.version = version;\n        metadata.type = isSimulator ? 'simulator' : 'device';\n      } else {\n        metadata.type = 'catalyst';\n      }\n      devices.push(metadata);\n    }\n  });\n\n  return devices;\n}\n\nexport default parseIOSDevicesList;\n"]}