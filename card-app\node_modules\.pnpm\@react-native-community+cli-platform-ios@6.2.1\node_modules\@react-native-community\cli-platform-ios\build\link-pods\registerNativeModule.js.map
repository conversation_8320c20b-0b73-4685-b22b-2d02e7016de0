{"version": 3, "sources": ["../../src/link-pods/registerNativeModule.ts"], "names": ["registerNativeModulePods", "name", "podspecPath", "iOSProject", "podLines", "podfile", "linesToAddEntry", "getLinesToAddEntry", "projectName", "linesToAddPodWithMarker", "length", "firstTargetLined", "CLIError", "chalk", "dim", "replace", "MARKER_TEXT"], "mappings": ";;;;;;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;;AACA;;AACA;;AAGA;;AACA;;;;;;;;AAjBA;AACA;AACA;AACA;AACA;AACA;AACA;AAae,SAASA,wBAAT,CACbC,IADa,EAEbC,WAFa,EAGbC,UAHa,EAIb;AACA,QAAMC,QAAQ,GAAG,0BAAYD,UAAU,CAACE,OAAvB,CAAjB;AACA,QAAMC,eAAe,GAAGC,kBAAkB,CAACH,QAAD,EAAWD,UAAX,CAA1C;AACA,4BAAYC,QAAZ,EAAsBE,eAAtB,EAAuCJ,WAAvC,EAAoDD,IAApD;AACA,4BAAYE,UAAU,CAACE,OAAvB,EAAgCD,QAAhC;AACD;;AAED,SAASG,kBAAT,CACEH,QADF,EAEE;AAACI,EAAAA;AAAD,CAFF,EAGE;AACA,QAAMC,uBAAuB,GAAG,uCAAyBL,QAAzB,CAAhC;;AACA,MAAIK,uBAAuB,CAACC,MAAxB,GAAiC,CAArC,EAAwC;AACtC,WAAOD,uBAAP;AACD;;AACD,QAAME,gBAAgB,GAAG,gCAAkBP,QAAlB,EAA4BI,WAA5B,CAAzB;;AACA,MAAIG,gBAAgB,KAAK,IAAzB,EAA+B;AAC7B,UAAM,KAAIC,oBAAJ,EAAc;AACxB;AACA;AACA,mCAAmCC,iBAAMC,GAAN,CAC1B,WAAUN,WAAW,CAACO,OAAZ,CAAoB,YAApB,EAAkC,EAAlC,CAAsC,MADtB,CAE3B;AACR;AACA,gCAAgCF,iBAAMC,GAAN,CACxBE,qCADwB,CAExB;AACR;AACA,KAXU,CAAN;AAYD;;AACD,SAAO,+BAAiBZ,QAAjB,EAA2BO,gBAA3B,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport chalk from 'chalk';\nimport {CLIError} from '@react-native-community/cli-tools';\nimport {IOSProjectConfig} from '@react-native-community/cli-types';\nimport readPodfile from './readPodfile';\nimport findPodTargetLine from './findPodTargetLine';\nimport findLineToAddPod from './findLineToAddPod';\nimport findMarkedLinesInPodfile, {\n  MARKER_TEXT,\n} from './findMarkedLinesInPodfile';\nimport addPodEntry from './addPodEntry';\nimport savePodFile from './savePodFile';\n\nexport default function registerNativeModulePods(\n  name: string,\n  podspecPath: string,\n  iOSProject: IOSProjectConfig,\n) {\n  const podLines = readPodfile(iOSProject.podfile);\n  const linesToAddEntry = getLinesToAddEntry(podLines, iOSProject);\n  addPodEntry(podLines, linesToAddEntry, podspecPath, name);\n  savePodFile(iOSProject.podfile, podLines);\n}\n\nfunction getLinesToAddEntry(\n  podLines: Array<string>,\n  {projectName}: IOSProjectConfig,\n) {\n  const linesToAddPodWithMarker = findMarkedLinesInPodfile(podLines);\n  if (linesToAddPodWithMarker.length > 0) {\n    return linesToAddPodWithMarker;\n  }\n  const firstTargetLined = findPodTargetLine(podLines, projectName);\n  if (firstTargetLined === null) {\n    throw new CLIError(`\n      We couldn't find a target to add a CocoaPods dependency.\n\n      Make sure that you have a \"${chalk.dim(\n        `target '${projectName.replace('.xcodeproj', '')}' do`,\n      )}\" line in your Podfile.\n\n      Alternatively, include \"${chalk.dim(\n        MARKER_TEXT,\n      )}\" in a Podfile where we should add\n      linked dependencies.\n    `);\n  }\n  return findLineToAddPod(podLines, firstTargetLined);\n}\n"]}