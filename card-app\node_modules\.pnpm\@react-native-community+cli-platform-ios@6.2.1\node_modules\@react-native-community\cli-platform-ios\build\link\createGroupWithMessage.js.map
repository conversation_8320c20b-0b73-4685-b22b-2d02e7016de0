{"version": 3, "sources": ["../../src/link/createGroupWithMessage.ts"], "names": ["createGroupWithMessage", "project", "path", "group", "logger", "warn"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,sBAAT,CAAgCC,OAAhC,EAA8CC,IAA9C,EAA4D;AACzE,MAAIC,KAAK,GAAG,uBAASF,OAAT,EAAkBC,IAAlB,CAAZ;;AAEA,MAAI,CAACC,KAAL,EAAY;AACVA,IAAAA,KAAK,GAAG,0BAAYF,OAAZ,EAAqBC,IAArB,CAAR;;AAEAE,uBAAOC,IAAP,CACG,UAASH,IAAK,mFADjB;AAGD;;AAED,SAAOC,KAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {logger} from '@react-native-community/cli-tools';\nimport createGroup from './createGroup';\nimport getGroup from './getGroup';\n\n/**\n * Given project and path of the group, it checks if a group exists at that path,\n * and deeply creates a group for that path if its does not already exist.\n *\n * Returns the existing or newly created group\n */\nexport default function createGroupWithMessage(project: any, path: string) {\n  let group = getGroup(project, path);\n\n  if (!group) {\n    group = createGroup(project, path);\n\n    logger.warn(\n      `Group '${path}' does not exist in your Xcode project. We have created it automatically for you.`,\n    );\n  }\n\n  return group;\n}\n"]}