{"version": 3, "sources": ["../../src/link/removeSharedLibraries.ts"], "names": ["removeSharedLibraries", "project", "libraries", "length", "target", "get<PERSON><PERSON>t<PERSON>arget", "uuid", "name", "removeFramework"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEe,SAASA,qBAAT,CACbC,OADa,EAEbC,SAFa,EAGb;AACA,MAAI,CAACA,SAAS,CAACC,MAAf,EAAuB;AACrB;AACD;;AAED,QAAMC,MAAM,GAAGH,OAAO,CAACI,cAAR,GAAyBC,IAAxC;;AAEA,OAAK,MAAMC,IAAX,IAAmBL,SAAnB,EAA8B;AAC5BD,IAAAA,OAAO,CAACO,eAAR,CAAwBD,IAAxB,EAA8B;AAACH,MAAAA;AAAD,KAA9B;AACD;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport default function removeSharedLibraries(\n  project: any,\n  libraries: Array<string>,\n) {\n  if (!libraries.length) {\n    return;\n  }\n\n  const target = project.getFirstTarget().uuid;\n\n  for (const name of libraries) {\n    project.removeFramework(name, {target});\n  }\n}\n"]}