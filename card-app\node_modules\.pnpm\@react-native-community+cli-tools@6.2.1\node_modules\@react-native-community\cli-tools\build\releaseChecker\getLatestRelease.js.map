{"version": 3, "sources": ["../../src/releaseChecker/getLatestRelease.ts"], "names": ["getLatestRelease", "name", "currentVersion", "logger", "debug", "cachedLatest", "cacheManager", "get", "aWeek", "lastChecked", "now", "Date", "Number", "eTag", "latestVersion", "getLatestRnDiffPurgeVersion", "semver", "compare", "prerelease", "version", "changelogUrl", "buildChangelogUrl", "diffUrl", "buildDiffUrl", "e", "options", "headers", "data", "status", "body", "substring", "eTagHeader", "set"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,eAAeA,gBAAf,CACbC,IADa,EAEbC,cAFa,EAGY;AACzBC,kBAAOC,KAAP,CAAa,8CAAb;;AACA,MAAI;AACFD,oBAAOC,KAAP,CAAc,oBAAmBF,cAAe,EAAhD;;AAEA,UAAMG,YAAY,GAAGC,6BAAaC,GAAb,CAAiBN,IAAjB,EAAuB,eAAvB,CAArB;;AAEA,QAAII,YAAJ,EAAkB;AAChBF,sBAAOC,KAAP,CAAc,2BAA0BC,YAAa,EAArD;AACD;;AAED,UAAMG,KAAK,GAAG,IAAI,EAAJ,GAAS,EAAT,GAAc,EAAd,GAAmB,IAAjC;;AACA,UAAMC,WAAW,GAAGH,6BAAaC,GAAb,CAAiBN,IAAjB,EAAuB,aAAvB,CAApB;;AACA,UAAMS,GAAG,GAAG,IAAIC,IAAJ,EAAZ;;AACA,QAAIF,WAAW,IAAIG,MAAM,CAACF,GAAD,CAAN,GAAcE,MAAM,CAAC,IAAID,IAAJ,CAASF,WAAT,CAAD,CAApB,GAA8CD,KAAjE,EAAwE;AACtEL,sBAAOC,KAAP,CAAa,uDAAb;;AACA;AACD;;AAEDD,oBAAOC,KAAP,CAAa,uCAAb;;AACA,UAAMS,IAAI,GAAGP,6BAAaC,GAAb,CAAiBN,IAAjB,EAAuB,MAAvB,CAAb;;AACA,UAAMa,aAAa,GAAG,MAAMC,2BAA2B,CAACd,IAAD,EAAOY,IAAP,CAAvD;;AACAV,oBAAOC,KAAP,CAAc,mBAAkBU,aAAc,EAA9C;;AAEA,QACEE,kBAAOC,OAAP,CAAeH,aAAf,EAA8BZ,cAA9B,MAAkD,CAAlD,IACA,CAACc,kBAAOE,UAAP,CAAkBJ,aAAlB,CAFH,EAGE;AACA,aAAO;AACLK,QAAAA,OAAO,EAAEL,aADJ;AAELM,QAAAA,YAAY,EAAEC,iBAAiB,CAACP,aAAD,CAF1B;AAGLQ,QAAAA,OAAO,EAAEC,YAAY,CAACrB,cAAD;AAHhB,OAAP;AAKD;AACF,GAhCD,CAgCE,OAAOsB,CAAP,EAAU;AACVrB,oBAAOC,KAAP,CACE,8DADF;;AAGAD,oBAAOC,KAAP,CAAaoB,CAAb;AACD;AACF;;AAED,SAASH,iBAAT,CAA2BF,OAA3B,EAA4C;AAC1C,SAAQ,0DAAyDA,OAAQ,EAAzE;AACD;;AAED,SAASI,YAAT,CAAsBJ,OAAtB,EAAuC;AACrC,SAAQ,iEAAgEA,OAAQ,EAAhF;AACD;AAED;AACA;AACA;;;AACA,eAAeJ,2BAAf,CACEd,IADF,EAEEY,IAFF,EAGmB;AACjB,QAAMY,OAAO,GAAG;AACd;AACAC,IAAAA,OAAO,EAAE;AAAC,oBAAc;AAAf;AAFK,GAAhB;;AAKA,MAAIb,IAAJ,EAAU;AACRY,IAAAA,OAAO,CAACC,OAAR,CAAgB,eAAhB,IAAmCb,IAAnC;AACD;;AAED,QAAM;AAACc,IAAAA,IAAD;AAAOC,IAAAA,MAAP;AAAeF,IAAAA;AAAf,MAA0B,MAAM,kBACpC,wEADoC,EAEpCD,OAFoC,CAAtC,CAViB,CAejB;;AACA,MAAIG,MAAM,KAAK,GAAf,EAAoB;AAClB,UAAMC,IAAgB,GAAGF,IAAzB;AACA,UAAMb,aAAa,GAAGe,IAAI,CAAC,CAAD,CAAJ,CAAQ5B,IAAR,CAAa6B,SAAb,CAAuB,CAAvB,CAAtB;AACA,UAAMC,UAAU,GAAGL,OAAO,CAACnB,GAAR,CAAY,MAAZ,CAAnB,CAHkB,CAKlB;;AACA,QAAI,CAACS,kBAAOE,UAAP,CAAkBJ,aAAlB,CAAD,IAAqCiB,UAAzC,EAAqD;AACnD5B,sBAAOC,KAAP,CAAc,UAAS2B,UAAW,WAAlC;;AACAzB,mCAAa0B,GAAb,CAAiB/B,IAAjB,EAAuB,MAAvB,EAA+B8B,UAA/B;;AACAzB,mCAAa0B,GAAb,CAAiB/B,IAAjB,EAAuB,eAAvB,EAAwCa,aAAxC;AACD;;AAED,WAAOA,aAAP;AACD,GA7BgB,CA+BjB;;;AACA,MAAIc,MAAM,KAAK,GAAf,EAAoB;AAClB,UAAMd,aAAa,GAAGR,6BAAaC,GAAb,CAAiBN,IAAjB,EAAuB,eAAvB,CAAtB;;AACA,QAAIa,aAAJ,EAAmB;AACjB,aAAOA,aAAP;AACD;AACF,GArCgB,CAuCjB;;;AACA,SAAO,OAAP;AACD", "sourcesContent": ["import semver from 'semver';\nimport cacheManager from './releaseCacheManager';\nimport {fetch} from '../fetch';\nimport logger from '../logger';\n\nexport type Release = {\n  version: string;\n  changelogUrl: string;\n  diffUrl: string;\n};\n\n/**\n * Checks via GitHub API if there is a newer stable React Native release and,\n * if it exists, returns the release data.\n *\n * If the latest release is not newer or if it's a prerelease, the function\n * will return undefined.\n */\nexport default async function getLatestRelease(\n  name: string,\n  currentVersion: string,\n): Promise<Release | void> {\n  logger.debug('Checking for a newer version of React Native');\n  try {\n    logger.debug(`Current version: ${currentVersion}`);\n\n    const cachedLatest = cacheManager.get(name, 'latestVersion');\n\n    if (cachedLatest) {\n      logger.debug(`Cached release version: ${cachedLatest}`);\n    }\n\n    const aWeek = 7 * 24 * 60 * 60 * 1000;\n    const lastChecked = cacheManager.get(name, 'lastChecked');\n    const now = new Date();\n    if (lastChecked && Number(now) - Number(new Date(lastChecked)) < aWeek) {\n      logger.debug('Cached release is still recent, skipping remote check');\n      return;\n    }\n\n    logger.debug('Checking for newer releases on GitHub');\n    const eTag = cacheManager.get(name, 'eTag');\n    const latestVersion = await getLatestRnDiffPurgeVersion(name, eTag);\n    logger.debug(`Latest release: ${latestVersion}`);\n\n    if (\n      semver.compare(latestVersion, currentVersion) === 1 &&\n      !semver.prerelease(latestVersion)\n    ) {\n      return {\n        version: latestVersion,\n        changelogUrl: buildChangelogUrl(latestVersion),\n        diffUrl: buildDiffUrl(currentVersion),\n      };\n    }\n  } catch (e) {\n    logger.debug(\n      'Something went wrong with remote version checking, moving on',\n    );\n    logger.debug(e);\n  }\n}\n\nfunction buildChangelogUrl(version: string) {\n  return `https://github.com/facebook/react-native/releases/tag/v${version}`;\n}\n\nfunction buildDiffUrl(version: string) {\n  return `https://react-native-community.github.io/upgrade-helper/?from=${version}`;\n}\n\n/**\n * Returns the most recent React Native version available to upgrade to.\n */\nasync function getLatestRnDiffPurgeVersion(\n  name: string,\n  eTag?: string,\n): Promise<string> {\n  const options = {\n    // https://developer.github.com/v3/#user-agent-required\n    headers: {'User-Agent': 'React-Native-CLI'} as Headers,\n  };\n\n  if (eTag) {\n    options.headers['If-None-Match'] = eTag;\n  }\n\n  const {data, status, headers} = await fetch(\n    'https://api.github.com/repos/react-native-community/rn-diff-purge/tags',\n    options,\n  );\n\n  // Remote is newer.\n  if (status === 200) {\n    const body: Array<any> = data;\n    const latestVersion = body[0].name.substring(8);\n    const eTagHeader = headers.get('eTag');\n\n    // Update cache only if newer release is stable.\n    if (!semver.prerelease(latestVersion) && eTagHeader) {\n      logger.debug(`Saving ${eTagHeader} to cache`);\n      cacheManager.set(name, 'eTag', eTagHeader);\n      cacheManager.set(name, 'latestVersion', latestVersion);\n    }\n\n    return latestVersion;\n  }\n\n  // Cache is still valid.\n  if (status === 304) {\n    const latestVersion = cacheManager.get(name, 'latestVersion');\n    if (latestVersion) {\n      return latestVersion;\n    }\n  }\n\n  // Should be returned only if something went wrong.\n  return '0.0.0';\n}\n\ntype Headers = {\n  'User-Agent': string;\n  [header: string]: string;\n};\n"]}