load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_robolectric_test")

rn_robolectric_test(
    name = "util",
    srcs = glob(["**/*.java"]),
    contacts = ["<EMAIL>"],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/java/fest:fest"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_target("java/com/facebook/react/util:util"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
    ],
)
