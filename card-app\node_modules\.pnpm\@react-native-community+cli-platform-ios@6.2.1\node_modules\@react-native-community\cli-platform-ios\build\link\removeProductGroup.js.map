{"version": 3, "sources": ["../../src/link/removeProductGroup.ts"], "names": ["removeProductGroup", "project", "productGroupId", "section", "hash", "objects", "PBXGroup", "key", "Object", "keys"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEe,SAASA,kBAAT,CAA4BC,OAA5B,EAA0CC,cAA1C,EAA+D;AAC5E,QAAMC,OAAO,GAAGF,OAAO,CAACG,IAAR,CAAaH,OAAb,CAAqBI,OAArB,CAA6BC,QAA7C;;AAEA,OAAK,MAAMC,GAAX,IAAkBC,MAAM,CAACC,IAAP,CAAYN,OAAZ,CAAlB,EAAwC;AACtC,QAAII,GAAG,KAAKL,cAAZ,EAA4B;AAC1B,aAAOC,OAAO,CAACI,GAAD,CAAd;AACD;AACF;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport default function removeProductGroup(project: any, productGroupId: any) {\n  const section = project.hash.project.objects.PBXGroup;\n\n  for (const key of Object.keys(section)) {\n    if (key === productGroupId) {\n      delete section[key];\n    }\n  }\n}\n"]}