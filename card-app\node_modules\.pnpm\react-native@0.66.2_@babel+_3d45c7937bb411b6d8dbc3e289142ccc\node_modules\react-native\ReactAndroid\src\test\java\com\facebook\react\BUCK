load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_target", "react_native_tests_target", "rn_robolectric_test")

rn_robolectric_test(
    name = "react",
    srcs = glob(["*.java"]),
    contacts = ["<EMAIL>"],
    deps = [
        YOGA_TARGET,
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-ui"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/fest:fest"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_dep("third-party/java/okhttp:okhttp3"),
        react_native_dep("third-party/java/okio:okio"),
        react_native_target("java/com/facebook/react:react"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/touch:touch"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_target("java/com/facebook/react/views/text:text"),
        react_native_target("java/com/facebook/react/views/view:view"),
        react_native_tests_target("java/com/facebook/react/bridge:testhelpers"),
    ],
)
