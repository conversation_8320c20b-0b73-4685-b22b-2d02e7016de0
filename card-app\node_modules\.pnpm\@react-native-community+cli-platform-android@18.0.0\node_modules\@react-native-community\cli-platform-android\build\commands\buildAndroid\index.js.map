{"version": 3, "names": ["buildAndroid", "_argv", "config", "args", "androidProject", "getAndroidProject", "tasks", "mode", "logger", "warn", "interactive", "selectedTask", "promptForTaskSelection", "sourceDir", "grad<PERSON><PERSON><PERSON><PERSON>", "getTaskNames", "appName", "extraParams", "push", "activeArchOnly", "adbPath", "getAdbPath", "devices", "adb", "getDevices", "architectures", "map", "device", "getCPU", "filter", "arch", "index", "array", "indexOf", "length", "info", "join", "build", "process", "chdir", "cmd", "platform", "startsWith", "debug", "execa", "sync", "stdio", "cwd", "error", "printRunDoctorTip", "CLIError", "options", "name", "description", "parse", "val", "split", "default", "func"], "sources": ["../../../src/commands/buildAndroid/index.ts"], "sourcesContent": ["import {\n  CLIError,\n  logger,\n  printRunDoctorTip,\n} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport execa from 'execa';\nimport {getAndroidProject} from '@react-native-community/cli-config-android';\nimport adb from '../runAndroid/adb';\nimport getAdbPath from '../runAndroid/getAdbPath';\nimport {getTaskNames} from '../runAndroid/getTaskNames';\nimport {promptForTaskSelection} from '../runAndroid/listAndroidTasks';\n\nexport interface BuildFlags {\n  mode?: string;\n  activeArchOnly?: boolean;\n  tasks?: Array<string>;\n  extraParams?: Array<string>;\n  interactive?: boolean;\n}\n\nasync function buildAndroid(\n  _argv: Array<string>,\n  config: Config,\n  args: BuildFlags,\n) {\n  const androidProject = getAndroidProject(config);\n\n  if (args.tasks && args.mode) {\n    logger.warn(\n      'Both \"tasks\" and \"mode\" parameters were passed to \"build\" command. Using \"tasks\" for building the app.',\n    );\n  }\n\n  let {tasks} = args;\n\n  if (args.interactive) {\n    const selectedTask = await promptForTaskSelection(\n      'build',\n      androidProject.sourceDir,\n    );\n    if (selectedTask) {\n      tasks = [selectedTask];\n    }\n  }\n\n  let gradleArgs = getTaskNames(\n    androidProject.appName,\n    args.mode,\n    tasks,\n    'bundle',\n  );\n\n  if (args.extraParams) {\n    gradleArgs.push(...args.extraParams);\n  }\n\n  if (args.activeArchOnly) {\n    const adbPath = getAdbPath();\n    const devices = adb.getDevices(adbPath);\n    const architectures = devices\n      .map((device) => {\n        return adb.getCPU(adbPath, device);\n      })\n      .filter(\n        (arch, index, array) => arch != null && array.indexOf(arch) === index,\n      );\n    if (architectures.length > 0) {\n      logger.info(`Detected architectures ${architectures.join(', ')}`);\n      // `reactNativeDebugArchitectures` was renamed to `reactNativeArchitectures` in 0.68.\n      // Can be removed when 0.67 no longer needs to be supported.\n      gradleArgs.push(\n        '-PreactNativeDebugArchitectures=' + architectures.join(','),\n      );\n      gradleArgs.push('-PreactNativeArchitectures=' + architectures.join(','));\n    }\n  }\n\n  return build(gradleArgs, androidProject.sourceDir);\n}\n\nexport function build(gradleArgs: string[], sourceDir: string) {\n  process.chdir(sourceDir);\n  const cmd = process.platform.startsWith('win') ? 'gradlew.bat' : './gradlew';\n  logger.info('Building the app...');\n  logger.debug(`Running command \"${cmd} ${gradleArgs.join(' ')}\"`);\n  try {\n    execa.sync(cmd, gradleArgs, {\n      stdio: 'inherit',\n      cwd: sourceDir,\n    });\n  } catch (error) {\n    printRunDoctorTip();\n    throw new CLIError('Failed to build the app.', error as Error);\n  }\n}\n\nexport const options = [\n  {\n    name: '--mode <string>',\n    description: \"Specify your app's build variant\",\n  },\n  {\n    name: '--tasks <list>',\n    description:\n      'Run custom Gradle tasks. By default it\\'s \"assembleDebug\". Will override passed mode and variant arguments.',\n    parse: (val: string) => val.split(','),\n  },\n  {\n    name: '--active-arch-only',\n    description:\n      'Build native libraries only for the current device architecture for debug builds.',\n    default: false,\n  },\n  {\n    name: '--extra-params <string>',\n    description: 'Custom params passed to gradle build command',\n    parse: (val: string) => val.split(' '),\n  },\n  {\n    name: '-i --interactive',\n    description:\n      'Explicitly select build type and flavour to use before running a build',\n  },\n];\n\nexport default {\n  name: 'build-android',\n  description: 'builds your app',\n  func: buildAndroid,\n  options,\n};\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAAsE;AAUtE,eAAeA,YAAY,CACzBC,KAAoB,EACpBC,MAAc,EACdC,IAAgB,EAChB;EACA,MAAMC,cAAc,GAAG,IAAAC,qCAAiB,EAACH,MAAM,CAAC;EAEhD,IAAIC,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACI,IAAI,EAAE;IAC3BC,kBAAM,CAACC,IAAI,CACT,wGAAwG,CACzG;EACH;EAEA,IAAI;IAACH;EAAK,CAAC,GAAGH,IAAI;EAElB,IAAIA,IAAI,CAACO,WAAW,EAAE;IACpB,MAAMC,YAAY,GAAG,MAAM,IAAAC,wCAAsB,EAC/C,OAAO,EACPR,cAAc,CAACS,SAAS,CACzB;IACD,IAAIF,YAAY,EAAE;MAChBL,KAAK,GAAG,CAACK,YAAY,CAAC;IACxB;EACF;EAEA,IAAIG,UAAU,GAAG,IAAAC,0BAAY,EAC3BX,cAAc,CAACY,OAAO,EACtBb,IAAI,CAACI,IAAI,EACTD,KAAK,EACL,QAAQ,CACT;EAED,IAAIH,IAAI,CAACc,WAAW,EAAE;IACpBH,UAAU,CAACI,IAAI,CAAC,GAAGf,IAAI,CAACc,WAAW,CAAC;EACtC;EAEA,IAAId,IAAI,CAACgB,cAAc,EAAE;IACvB,MAAMC,OAAO,GAAG,IAAAC,mBAAU,GAAE;IAC5B,MAAMC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;IACvC,MAAMK,aAAa,GAAGH,OAAO,CAC1BI,GAAG,CAAEC,MAAM,IAAK;MACf,OAAOJ,YAAG,CAACK,MAAM,CAACR,OAAO,EAAEO,MAAM,CAAC;IACpC,CAAC,CAAC,CACDE,MAAM,CACL,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KAAKF,IAAI,IAAI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,KAAKC,KAAK,CACtE;IACH,IAAIN,aAAa,CAACS,MAAM,GAAG,CAAC,EAAE;MAC5B1B,kBAAM,CAAC2B,IAAI,CAAE,0BAAyBV,aAAa,CAACW,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;MACjE;MACA;MACAtB,UAAU,CAACI,IAAI,CACb,kCAAkC,GAAGO,aAAa,CAACW,IAAI,CAAC,GAAG,CAAC,CAC7D;MACDtB,UAAU,CAACI,IAAI,CAAC,6BAA6B,GAAGO,aAAa,CAACW,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1E;EACF;EAEA,OAAOC,KAAK,CAACvB,UAAU,EAAEV,cAAc,CAACS,SAAS,CAAC;AACpD;AAEO,SAASwB,KAAK,CAACvB,UAAoB,EAAED,SAAiB,EAAE;EAC7DyB,OAAO,CAACC,KAAK,CAAC1B,SAAS,CAAC;EACxB,MAAM2B,GAAG,GAAGF,OAAO,CAACG,QAAQ,CAACC,UAAU,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;EAC5ElC,kBAAM,CAAC2B,IAAI,CAAC,qBAAqB,CAAC;EAClC3B,kBAAM,CAACmC,KAAK,CAAE,oBAAmBH,GAAI,IAAG1B,UAAU,CAACsB,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;EAChE,IAAI;IACFQ,gBAAK,CAACC,IAAI,CAACL,GAAG,EAAE1B,UAAU,EAAE;MAC1BgC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAElC;IACP,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOmC,KAAK,EAAE;IACd,IAAAC,6BAAiB,GAAE;IACnB,MAAM,KAAIC,oBAAQ,EAAC,0BAA0B,EAAEF,KAAK,CAAU;EAChE;AACF;AAEO,MAAMG,OAAO,GAAG,CACrB;EACEC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE;AACf,CAAC,EACD;EACED,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EACT,6GAA6G;EAC/GC,KAAK,EAAGC,GAAW,IAAKA,GAAG,CAACC,KAAK,CAAC,GAAG;AACvC,CAAC,EACD;EACEJ,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EACT,mFAAmF;EACrFI,OAAO,EAAE;AACX,CAAC,EACD;EACEL,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,8CAA8C;EAC3DC,KAAK,EAAGC,GAAW,IAAKA,GAAG,CAACC,KAAK,CAAC,GAAG;AACvC,CAAC,EACD;EACEJ,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EACT;AACJ,CAAC,CACF;AAAC;AAAA,eAEa;EACbD,IAAI,EAAE,eAAe;EACrBC,WAAW,EAAE,iBAAiB;EAC9BK,IAAI,EAAE1D,YAAY;EAClBmD;AACF,CAAC;AAAA"}