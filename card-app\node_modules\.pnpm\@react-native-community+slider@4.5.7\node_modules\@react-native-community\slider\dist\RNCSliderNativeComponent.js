var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.__INTERNAL_VIEW_CONFIG=void 0;var _codegenNativeComponent=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));var NativeComponentRegistry=require('react-native/Libraries/NativeComponent/NativeComponentRegistry');var _require=require('react-native/Libraries/NativeComponent/ViewConfigIgnore'),ConditionallyIgnoredEventHandlers=_require.ConditionallyIgnoredEventHandlers;var nativeComponentName='RNCSlider';var __INTERNAL_VIEW_CONFIG=exports.__INTERNAL_VIEW_CONFIG={uiViewClassName:'RNCSlider',bubblingEventTypes:{topChange:{phasedRegistrationNames:{captured:'onChangeCapture',bubbled:'onChange'}},topRNCSliderValueChange:{phasedRegistrationNames:{captured:'onRNCSliderValueChangeCapture',bubbled:'onRNCSliderValueChange'}}},directEventTypes:{topRNCSliderSlidingStart:{registrationName:'onRNCSliderSlidingStart'},topRNCSliderSlidingComplete:{registrationName:'onRNCSliderSlidingComplete'}},validAttributes:Object.assign({accessibilityUnits:true,accessibilityIncrements:true,disabled:true,inverted:true,vertical:true,tapToSeek:true,maximumTrackImage:{process:require('react-native/Libraries/Image/resolveAssetSource')},maximumTrackTintColor:{process:require('react-native/Libraries/StyleSheet/processColor').default},maximumValue:true,minimumTrackImage:{process:require('react-native/Libraries/Image/resolveAssetSource')},minimumTrackTintColor:{process:require('react-native/Libraries/StyleSheet/processColor').default},minimumValue:true,step:true,testID:true,thumbImage:{process:require('react-native/Libraries/Image/resolveAssetSource')},thumbTintColor:{process:require('react-native/Libraries/StyleSheet/processColor').default},trackImage:{process:require('react-native/Libraries/Image/resolveAssetSource')},value:true,lowerLimit:true,upperLimit:true},ConditionallyIgnoredEventHandlers({onChange:true,onRNCSliderSlidingStart:true,onRNCSliderSlidingComplete:true,onRNCSliderValueChange:true}))};var _default=exports.default=NativeComponentRegistry.get(nativeComponentName,function(){return __INTERNAL_VIEW_CONFIG;});