/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
/**
 * Every file added to the project from another project is attached to
 * `PBXItemContainerProxy` through `PBXReferenceProxy`.
 */
export default function removeFromPbxReferenceProxySection(project: any, file: any): void;
//# sourceMappingURL=removeFromPbxReferenceProxySection.d.ts.map