/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

// placeholder token that will be replaced by signedsource script
export const GeneratedHeader: string =
  '// Copyright 2004-present Facebook. All Rights Reserved.\n' +
  '// @generated <<SignedSource::*O*zOeWoEQle#+L!plEphiEmie@IsG>>';
