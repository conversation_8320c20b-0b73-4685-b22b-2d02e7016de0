{"version": 3, "sources": ["../../src/link/removeProjectFromLibraries.ts"], "names": ["removeProjectFromLibraries", "libraries", "file", "children", "filter", "library", "comment", "basename"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,0BAAT,CACbC,SADa,EAEbC,IAFa,EAGb;AACAD,EAAAA,SAAS,CAACE,QAAV,GAAqBF,SAAS,CAACE,QAAV,CAAmBC,MAAnB,CAClBC,OAAD,IAAaA,OAAO,CAACC,OAAR,KAAoBJ,IAAI,CAACK,QADnB,CAArB;AAGD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n/**\n * Given an array of xcodeproj libraries and pbxFile,\n * it removes it from that group by comparing basenames\n *\n * Important: That function mutates `libraries` and it's not pure.\n * It's mainly due to limitations of `xcode` library.\n */\nexport default function removeProjectFromLibraries(\n  libraries: {children: Array<{comment: string}>},\n  file: {basename: string},\n) {\n  libraries.children = libraries.children.filter(\n    (library) => library.comment !== file.basename,\n  );\n}\n"]}