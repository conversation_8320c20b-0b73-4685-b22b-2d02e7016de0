load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "mockito",
    autoglob = False,
    visibility = ["//ReactAndroid/..."],
    exported_deps = [
        ":mockito-core",
        ":objenesis",
    ],
)

rn_prebuilt_jar(
    name = "mockito-core",
    binary_jar = ":mockito-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "mockito-binary.jar",
    sha1 = "c54c55cae0f4742ad6bf8a1987ada35363f2c4e2",
    url = "mvn:org.mockito:mockito-core:jar:2.19.1",
)

rn_prebuilt_jar(
    name = "objenesis",
    binary_jar = ":objenesis-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "objenesis-binary.jar",
    sha1 = "87c0ea803b69252868d09308b4618f766f135a96",
    url = "mvn:org.objenesis:objenesis:jar:2.1",
)
