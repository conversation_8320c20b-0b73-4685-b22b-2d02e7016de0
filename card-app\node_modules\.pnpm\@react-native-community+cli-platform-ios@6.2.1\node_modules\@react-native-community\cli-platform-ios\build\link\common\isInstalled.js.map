{"version": 3, "sources": ["../../../src/link/common/isInstalled.ts"], "names": ["isInstalled", "projectConfig", "_name", "dependencyConfig"], "mappings": ";;;;;;;AAQA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AASe,SAASA,WAAT,CACbC,aADa,EAEb;AACAC,KAHa,EAIbC,gBAJa,EAKb;AACA,SACE,0BAAeF,aAAf,EAA8BE,gBAA9B,KACA,2BAAgBF,aAAhB,EAA+BE,gBAA/B,CAFF;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport isInstalledIOS from '../isInstalled';\nimport isInstalledPods from '../../link-pods/isInstalled';\nimport {\n  IOSProjectConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\n\nexport default function isInstalled(\n  projectConfig: IOSProjectConfig,\n  // FIXME: name is never used\n  _name: string | undefined,\n  dependencyConfig: IOSDependencyConfig,\n) {\n  return (\n    isInstalledIOS(projectConfig, dependencyConfig) ||\n    isInstalledPods(projectConfig, dependencyConfig)\n  );\n}\n"]}