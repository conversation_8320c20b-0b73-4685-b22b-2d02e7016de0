load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "react_native_tests_target", "rn_robolectric_test")

rn_robolectric_test(
    name = "animated",
    srcs = glob(["**/*.java"]),
    contacts = ["<EMAIL>"],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/java/fest:fest"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_target("java/com/facebook/react:react"),
        react_native_target("java/com/facebook/react/animated:animated"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/modules/core:core"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_tests_target("java/com/facebook/react/bridge:testhelpers"),
    ],
)
