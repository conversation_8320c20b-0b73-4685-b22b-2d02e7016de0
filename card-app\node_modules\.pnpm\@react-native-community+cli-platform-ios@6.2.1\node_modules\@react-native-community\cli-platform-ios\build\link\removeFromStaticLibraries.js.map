{"version": 3, "sources": ["../../src/link/removeFromStaticLibraries.ts"], "names": ["removeFromStaticLibraries", "project", "path", "opts", "file", "PbxFile", "target", "undefined", "removeFromPbxFileReferenceSection", "removeFromPbxBuildFileSection", "removeFromPbxFrameworksBuildPhase", "removeFromLibrarySearchPaths"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACe,SAASA,yBAAT,CACbC,OADa,EAEbC,IAFa,EAGbC,IAHa,EAIb;AACA,QAAMC,IAAI,GAAG,KAAIC,kBAAJ,EAAYH,IAAZ,CAAb;AAEAE,EAAAA,IAAI,CAACE,MAAL,GAAcH,IAAI,GAAGA,IAAI,CAACG,MAAR,GAAiBC,SAAnC;AAEAN,EAAAA,OAAO,CAACO,iCAAR,CAA0CJ,IAA1C;AACAH,EAAAA,OAAO,CAACQ,6BAAR,CAAsCL,IAAtC;AACAH,EAAAA,OAAO,CAACS,iCAAR,CAA0CN,IAA1C;AACAH,EAAAA,OAAO,CAACU,4BAAR,CAAqCP,IAArC;AACA,mDAAmCH,OAAnC,EAA4CG,IAA5C;AAEA,SAAOA,IAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport PbxFile from 'xcode/lib/pbxFile';\nimport removeFromPbxReferenceProxySection from './removeFromPbxReferenceProxySection';\n\n/**\n * Removes file from static libraries\n *\n * Similar to `node-xcode` addStaticLibrary\n */\nexport default function removeFromStaticLibraries(\n  project: any,\n  path: string,\n  opts: {[key: string]: any},\n) {\n  const file = new PbxFile(path);\n\n  file.target = opts ? opts.target : undefined;\n\n  project.removeFromPbxFileReferenceSection(file);\n  project.removeFromPbxBuildFileSection(file);\n  project.removeFromPbxFrameworksBuildPhase(file);\n  project.removeFromLibrarySearchPaths(file);\n  removeFromPbxReferenceProxySection(project, file);\n\n  return file;\n}\n"]}