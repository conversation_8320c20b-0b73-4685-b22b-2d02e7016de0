{"version": 3, "sources": ["../src/utils.ts"], "names": ["getMinPriority", "Priority", "priorities", "defaultPriority", "parsedPriorities", "Object", "keys", "filter", "key", "map", "fromName", "length", "Math", "min"], "mappings": ";;;;;;;AAAO,SAASA,cAAT,CACLC,QADK,EAELC,UAFK,EAGLC,eAHK,EAIG;AACR,QAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAP,CAAYJ,UAAZ,EACtBK,MADsB,CACdC,GAAD,IAAiBN,UAAU,CAACM,GAAD,CADZ,EAEtBC,GAFsB,CAEjBD,GAAD,IAAiB;AACpB,WAAOP,QAAQ,CAACS,QAAT,CAAkBF,GAAlB,CAAP;AACD,GAJsB,CAAzB;AAKA,SAAOJ,gBAAgB,CAACO,MAAjB,GACHC,IAAI,CAACC,GAAL,CAAS,GAAGT,gBAAZ,CADG,GAEHD,eAFJ;AAGD", "sourcesContent": ["export function getMinPriority(\n  Priority: { fromName: (key: any) => number },\n  priorities: { [key: string]: boolean },\n  defaultPriority: number\n): number {\n  const parsedPriorities = Object.keys(priorities)\n    .filter((key: string) => priorities[key])\n    .map((key: string) => {\n      return Priority.fromName(key);\n    });\n  return parsedPriorities.length\n    ? Math.min(...parsedPriorities)\n    : defaultPriority;\n}\n"], "file": "utils.js"}