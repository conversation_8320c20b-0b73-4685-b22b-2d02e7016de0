{"version": 3, "sources": ["../../src/link/removeFromHeaderSearchPaths.ts"], "names": ["addToHeaderSearchPaths", "project", "path", "logger", "debug", "searchPaths", "filter", "searchPath"], "mappings": ";;;;;;;AAQA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACe,SAASA,sBAAT,CAAgCC,OAAhC,EAA8CC,IAA9C,EAA4D;AACzEC,qBAAOC,KAAP,CAAc,YAAWF,IAAK,2BAA9B;;AACA,qCAAqBD,OAArB,EAA+BI,WAAD,IAC5BA,WAAW,CAACC,MAAZ,CAAoBC,UAAD,IAAgBA,UAAU,KAAKL,IAAlD,CADF;AAGD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport mapHeaderSearchPaths from './mapHeaderSearchPaths';\nimport {logger} from '@react-native-community/cli-tools';\n\n/**\n * Given Xcode project and absolute path, it makes sure there are no headers referring to it\n */\nexport default function addToHeaderSearchPaths(project: any, path: string) {\n  logger.debug(`Removing ${path} from header search paths`);\n  mapHeaderSearchPaths(project, (searchPaths) =>\n    searchPaths.filter((searchPath) => searchPath !== path),\n  );\n}\n"]}