{"version": 3, "sources": ["../../src/link/unregisterNativeModule.ts"], "names": ["unregisterNativeModule", "dependencyConfig", "projectConfig", "iOSDependencies", "logger", "debug", "pbxproj<PERSON><PERSON>", "project", "xcode", "parseSync", "dependencyProject", "libraries", "libraryFolder", "file", "path", "relative", "sourceDir", "projectPath", "for<PERSON>ach", "target", "name", "get<PERSON><PERSON>t<PERSON>arget", "firstTarget", "uuid", "sharedLibraries", "reduce", "libs", "dependency", "concat", "headers", "folder", "fs", "writeFileSync", "writeSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAtBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAuBA;AACA;AACA;AACA;AACA;AACe,SAASA,sBAAT,CACbC,gBADa,EAEbC,aAFa,EAGbC,eAHa,EAIb;AACAC,qBAAOC,KAAP,CAAc,WAAUH,aAAa,CAACI,WAAY,EAAlD;;AACA,QAAMC,OAAO,GAAGC,iBAAMD,OAAN,CAAcL,aAAa,CAACI,WAA5B,EAAyCG,SAAzC,EAAhB;;AACA,QAAMC,iBAAiB,GAAGF,iBACvBD,OADuB,CACfN,gBAAgB,CAACK,WADF,EAEvBG,SAFuB,EAA1B;;AAIA,QAAME,SAAS,GAAG,uBAASJ,OAAT,EAAkBL,aAAa,CAACU,aAAhC,CAAlB;AAEA,QAAMC,IAAI,GAAG,uCACXN,OADW,EAEXO,gBAAKC,QAAL,CAAcb,aAAa,CAACc,SAA5B,EAAuCf,gBAAgB,CAACgB,WAAxD,CAFW,CAAb;AAKA,2CAA2BN,SAA3B,EAAsCE,IAAtC;AAEA,2BAAWH,iBAAX,EAA8BQ,OAA9B,CAAuCC,MAAD,IAAiB;AACrDf,uBAAOC,KAAP,CACG,YAAWc,MAAM,CAACC,IAAK,SACtBb,OAAO,CAACc,cAAR,GAAyBC,WAAzB,CAAqCF,IACtC,EAHH;;AAKA,4CAA0Bb,OAA1B,EAAmCY,MAAM,CAACC,IAA1C,EAAgD;AAC9CD,MAAAA,MAAM,EAAEZ,OAAO,CAACc,cAAR,GAAyBE;AADa,KAAhD;AAGD,GATD;AAWA,QAAMC,eAAe,GAAG,0BACtBvB,gBAAgB,CAACuB,eADK,EAEtBrB,eAAe,CAACsB,MAAhB,CACE,CAACC,IAAD,EAAOC,UAAP,KAAsBD,IAAI,CAACE,MAAL,CAAYD,UAAU,CAACH,eAAvB,CADxB,EAEEtB,aAAa,CAACsB,eAFhB,CAFsB,CAAxB;AAQA,sCAAsBjB,OAAtB,EAA+BiB,eAA/B;AAEA,QAAMK,OAAO,GAAG,iCAAmB5B,gBAAgB,CAAC6B,MAApC,CAAhB;;AACA,MAAI,CAAC,uBAAQD,OAAR,CAAL,EAAuB;AACrB,8CACEtB,OADF,EAEE,kCAAoBL,aAAa,CAACc,SAAlC,EAA6Ca,OAA7C,CAFF;AAID;;AAEDzB,qBAAOC,KAAP,CAAc,sBAAqBH,aAAa,CAACI,WAAY,EAA7D;;AACAyB,gBAAGC,aAAH,CAAiB9B,aAAa,CAACI,WAA/B,EAA4CC,OAAO,CAAC0B,SAAR,EAA5C;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport xcode from 'xcode';\nimport path from 'path';\nimport fs from 'fs';\nimport {difference, isEmpty} from 'lodash';\n\nimport getGroup from './getGroup';\nimport getTargets from './getTargets';\nimport getHeadersInFolder from './getHeadersInFolder';\nimport getHeaderSearchPath from './getHeaderSearchPath';\nimport removeProjectFromProject from './removeProjectFromProject';\nimport removeProjectFromLibraries from './removeProjectFromLibraries';\nimport removeFromStaticLibraries from './removeFromStaticLibraries';\nimport removeFromHeaderSearchPaths from './removeFromHeaderSearchPaths';\nimport removeSharedLibraries from './removeSharedLibraries';\nimport {logger} from '@react-native-community/cli-tools';\nimport {\n  IOSDependencyConfig,\n  IOSProjectConfig,\n  IOSProjectParams,\n} from '@react-native-community/cli-types';\n\n/**\n * Unregister native module IOS\n *\n * If library is already unlinked, this action is a no-op.\n */\nexport default function unregisterNativeModule(\n  dependencyConfig: IOSDependencyConfig,\n  projectConfig: IOSProjectConfig,\n  iOSDependencies: Array<IOSProjectParams>,\n) {\n  logger.debug(`Reading ${projectConfig.pbxprojPath}`);\n  const project = xcode.project(projectConfig.pbxprojPath).parseSync();\n  const dependencyProject = xcode\n    .project(dependencyConfig.pbxprojPath)\n    .parseSync();\n\n  const libraries = getGroup(project, projectConfig.libraryFolder);\n\n  const file = removeProjectFromProject(\n    project,\n    path.relative(projectConfig.sourceDir, dependencyConfig.projectPath),\n  );\n\n  removeProjectFromLibraries(libraries, file);\n\n  getTargets(dependencyProject).forEach((target: any) => {\n    logger.debug(\n      `Removing ${target.name} from ${\n        project.getFirstTarget().firstTarget.name\n      }`,\n    );\n    removeFromStaticLibraries(project, target.name, {\n      target: project.getFirstTarget().uuid,\n    });\n  });\n\n  const sharedLibraries = difference(\n    dependencyConfig.sharedLibraries,\n    iOSDependencies.reduce(\n      (libs, dependency) => libs.concat(dependency.sharedLibraries),\n      projectConfig.sharedLibraries,\n    ),\n  );\n\n  removeSharedLibraries(project, sharedLibraries);\n\n  const headers = getHeadersInFolder(dependencyConfig.folder);\n  if (!isEmpty(headers)) {\n    removeFromHeaderSearchPaths(\n      project,\n      getHeaderSearchPath(projectConfig.sourceDir, headers),\n    );\n  }\n\n  logger.debug(`Writing changes to ${projectConfig.pbxprojPath}`);\n  fs.writeFileSync(projectConfig.pbxprojPath, project.writeSync());\n}\n"]}