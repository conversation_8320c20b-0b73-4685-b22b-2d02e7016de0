/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { IOSProjectConfig } from '@react-native-community/cli-types';
/**
 * This function works in a similar manner to its Android version,
 * except it does not copy fonts but creates Xcode Group references
 */
export default function linkAssetsIOS(files: Array<string>, projectConfig: IOSProjectConfig): void;
//# sourceMappingURL=copyAssets.d.ts.map