{"version": 3, "file": "api.d.ts", "sourceRoot": "", "sources": ["../src/api.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,OAAO,EAAS,QAAQ,EAAE,MAAM,SAAS,CAAC;AAMnD,OAAO,EAAE,QAAQ,IAAI,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAOlE,OAAO,EAAE,QAAQ,IAAI,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAG1D,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACxD,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,oBAAY,eAAe,GAAG;IAC5B,QAAQ,EAAE,QAAQ,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,aAAa,CAAC;CACxB,CAAC;AAEF,oBAAY,aAAa,GAAG,CAC1B,QAAQ,EAAE,QAAQ,EAClB,WAAW,CAAC,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,MAAM,KACb,OAAO,CAAC;AAEb,wBAAgB,cAAc,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,aAAa,CAS/D;AAED,wBAAgB,aAAa,CAAC,aAAa,EAAE,MAAM,GAAG,aAAa,CAUlE;AAED,wBAAgB,eAAe,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,aAAa,CASnE;AAED,wBAAgB,gBAAgB,CAAC,GAAG,QAAQ,EAAE,MAAM,EAAE,GAAG,aAAa,CAUrE;AAED,wBAAgB,QAAQ,CAAC,OAAO,EAAE,eAAe,GAAG,YAAY,CAwE/D"}