{"version": 3, "sources": ["../../src/releaseChecker/index.ts"], "names": ["release<PERSON><PERSON><PERSON>", "root", "version", "currentVersion", "require", "path", "join", "name", "latestRelease", "e", "logger", "debug"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;;AACA;;AACA;;;;AAHA;AAKe,eAAeA,cAAf,CAA8BC,IAA9B,EAA4C;AACzD,MAAI;AACF,UAAM;AAACC,MAAAA,OAAO,EAAEC;AAAV,QAA4BC,OAAO,CAACC,gBAAKC,IAAL,CACxC,mCAAqBL,IAArB,EAA2B,cAA3B,CADwC,EAExC,cAFwC,CAAD,CAAzC;;AAIA,UAAM;AAACM,MAAAA;AAAD,QAASH,OAAO,CAACC,gBAAKC,IAAL,CAAUL,IAAV,EAAgB,cAAhB,CAAD,CAAtB;;AACA,UAAMO,aAAa,GAAG,MAAM,+BAAiBD,IAAjB,EAAuBJ,cAAvB,CAA5B;;AAEA,QAAIK,aAAJ,EAAmB;AACjB,oCAAgBD,IAAhB,EAAsBC,aAAtB,EAAqCL,cAArC;AACD;AACF,GAXD,CAWE,OAAOM,CAAP,EAAU;AACV;AACA;AACAC,oBAAOC,KAAP,CACE,oDACE,oCAFJ;;AAIAD,oBAAOC,KAAP,CAAaF,CAAb;AACD;AACF", "sourcesContent": ["import path from 'path';\nimport logger from '../logger';\n// @ts-ignore - JS file\nimport resolveNodeModuleDir from '../resolveNodeModuleDir';\nimport getLatestRelease from './getLatestRelease';\nimport printNewRelease from './printNewRelease';\n\nexport default async function releaseChecker(root: string) {\n  try {\n    const {version: currentVersion} = require(path.join(\n      resolveNodeModuleDir(root, 'react-native'),\n      'package.json',\n    ));\n    const {name} = require(path.join(root, 'package.json'));\n    const latestRelease = await getLatestRelease(name, currentVersion);\n\n    if (latestRelease) {\n      printNewRelease(name, latestRelease, currentVersion);\n    }\n  } catch (e) {\n    // We let the flow continue as this component is not vital for the rest of\n    // the CLI.\n    logger.debug(\n      'Cannot detect current version of React Native, ' +\n        'skipping check for a newer release',\n    );\n    logger.debug(e);\n  }\n}\n"]}