/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const path = require('path');
const {getDefaultConfig} = require('metro-config');

module.exports = (async () => {
  const {
    resolver: {sourceExts, assetExts},
  } = await getDefaultConfig();

  return {
    transformer: {
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: true,
        },
      }),
    },
    resolver: {
      sourceExts,
      assetExts,
      nodeModulesPaths: [
        path.resolve(__dirname, 'node_modules'),
        path.resolve(__dirname, 'node_modules/.pnpm/node_modules'),
      ],
      alias: {
        '@babel/runtime': path.resolve(__dirname, 'node_modules/@babel/runtime'),
      },
    },
    watchFolders: [
      path.resolve(__dirname, 'node_modules'),
    ],
  };
})();
