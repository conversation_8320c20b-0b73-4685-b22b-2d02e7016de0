export declare const flat: {
    android: {
        'build.gradle': any;
        src: {
            'AndroidManifest.xml': any;
            main: {
                com: {
                    some: {
                        example: {
                            [x: string]: any;
                            'Main.java': any;
                        };
                    };
                };
            };
        };
    };
};
export declare const nested: {
    android: {
        app: {
            'build.gradle': any;
            src: {
                'AndroidManifest.xml': any;
                main: {
                    com: {
                        some: {
                            example: {
                                [x: string]: any;
                                'Main.java': any;
                            };
                        };
                    };
                };
            };
        };
    };
};
export declare const withExamples: {
    Examples: {
        android: {
            'build.gradle': any;
            src: {
                'AndroidManifest.xml': any;
                main: {
                    com: {
                        some: {
                            example: {
                                [x: string]: any;
                                'Main.java': any;
                            };
                        };
                    };
                };
            };
        };
    };
    android: {
        'build.gradle': any;
        src: {
            'AndroidManifest.xml': any;
            main: {
                com: {
                    some: {
                        example: {
                            [x: string]: any;
                            'Main.java': any;
                        };
                    };
                };
            };
        };
    };
};
//# sourceMappingURL=projects.d.ts.map