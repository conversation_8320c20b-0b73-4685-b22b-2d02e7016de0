{"version": 3, "sources": ["../src/isPackagerRunning.ts"], "names": ["isPackagerRunning", "packagerPort", "process", "env", "RCT_METRO_PORT", "data", "_error"], "mappings": ";;;;;;;AAQA;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,iBAAf,CACEC,YAA6B,GAAGC,OAAO,CAACC,GAAR,CAAYC,cAAZ,IAA8B,MADhE,EAEuD;AACrD,MAAI;AACF,UAAM;AAACC,MAAAA;AAAD,QAAS,MAAM,kBAAO,oBAAmBJ,YAAa,SAAvC,CAArB;AAEA,WAAOI,IAAI,KAAK,yBAAT,GAAqC,SAArC,GAAiD,cAAxD;AACD,GAJD,CAIE,OAAOC,MAAP,EAAe;AACf,WAAO,aAAP;AACD;AACF;;eAEcN,iB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {fetch} from './fetch';\n\n/**\n * Indicates whether or not the packager is running. It returns a promise that\n * returns one of these possible values:\n *   - `running`: the packager is running\n *   - `not_running`: the packager nor any process is running on the expected port.\n *   - `unrecognized`: one other process is running on the port we expect the packager to be running.\n */\nasync function isPackagerRunning(\n  packagerPort: string | number = process.env.RCT_METRO_PORT || '8081',\n): Promise<'running' | 'not_running' | 'unrecognized'> {\n  try {\n    const {data} = await fetch(`http://localhost:${packagerPort}/status`);\n\n    return data === 'packager-status:running' ? 'running' : 'unrecognized';\n  } catch (_error) {\n    return 'not_running';\n  }\n}\n\nexport default isPackagerRunning;\n"]}