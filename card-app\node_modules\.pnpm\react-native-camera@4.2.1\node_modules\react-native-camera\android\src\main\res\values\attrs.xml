<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (C) 2016 The Android Open Source Project
    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at
         http://www.apache.org/licenses/LICENSE-2.0
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<resources>
    <declare-styleable name="CameraView">
        <!--
          Set this to true if you want the CameraView to adjust its bounds to preserve the aspect
          ratio of its camera preview.
        -->
        <attr name="android:adjustViewBounds"/>
        <!-- Direction the camera faces relative to device screen. -->
        <attr name="facing" format="enum">
            <!-- The camera device faces the opposite direction as the device's screen. -->
            <enum name="back" value="0"/>
            <!-- The camera device faces the same direction as the device's screen. -->
            <enum name="front" value="1"/>
        </attr>
        <!-- Aspect ratio of camera preview and pictures. -->
        <attr name="aspectRatio" format="string"/>
        <!-- Continuous auto focus mode. -->
        <attr name="autoFocus" format="boolean"/>
        <!-- The flash mode. -->
        <attr name="flash" format="enum">
            <!-- Flash will not be fired. -->
            <enum name="off" value="0"/>
            <!--
              Flash will always be fired during snapshot.
              The flash may also be fired during preview or auto-focus depending on the driver.
            -->
            <enum name="on" value="1"/>
            <!--
              Constant emission of light during preview, auto-focus and snapshot.
              This can also be used for video recording.
            -->
            <enum name="torch" value="2"/>
            <!--
              Flash will be fired automatically when required.
              The flash may be fired during preview, auto-focus, or snapshot depending on the
              driver.
            -->
            <enum name="auto" value="3"/>
            <!--
              Flash will be fired in red-eye reduction mode.
            -->
            <enum name="redEye" value="4"/>
        </attr>
    </declare-styleable>
</resources>
