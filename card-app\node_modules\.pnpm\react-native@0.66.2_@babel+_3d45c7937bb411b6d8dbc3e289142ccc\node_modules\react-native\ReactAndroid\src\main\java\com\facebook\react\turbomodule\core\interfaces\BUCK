load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "rn_android_library")

rn_android_library(
    name = "interfaces",
    srcs = glob(
        ["*.java"],
    ),
    autoglob = False,
    is_androidx = True,
    labels = ["supermodule:xplat/default/public.react_native.infra"],
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
    ],
)
