{"version": 3, "sources": ["../../src/link/copyAssets.ts"], "names": ["linkAssetsIOS", "files", "projectConfig", "project", "xcode", "pbxproj<PERSON><PERSON>", "parseSync", "assets", "plist", "sourceDir", "addResourceFile", "f", "map", "asset", "logger", "debug", "path", "relative", "target", "get<PERSON><PERSON>t<PERSON>arget", "uuid", "filter", "Boolean", "file", "basename", "image", "fonts", "font", "existingFonts", "UIAppFonts", "allFonts", "Array", "from", "Set", "fs", "writeFileSync", "writeSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;;AAWA;AACA;AACA;AACA;AACe,SAASA,aAAT,CACbC,KADa,EAEbC,aAFa,EAGb;AACA,QAAMC,OAAO,GAAGC,iBAAMD,OAAN,CAAcD,aAAa,CAACG,WAA5B,EAAyCC,SAAzC,EAAhB;;AACA,QAAMC,MAAM,GAAG,kCAAiBN,KAAjB,CAAf;AACA,QAAMO,KAAK,GAAG,uBAASL,OAAT,EAAkBD,aAAa,CAACO,SAAhC,CAAd;AAEA,uCAAuBN,OAAvB,EAAgC,WAAhC;;AAEA,WAASO,eAAT,CAAyBC,CAAzB,EAA2C;AACzC,WAAO,CAACA,CAAC,IAAI,EAAN,EACJC,GADI,CACCC,KAAD,IAAW;AACdC,yBAAOC,KAAP,CAAc,iBAAgBF,KAAM,EAApC;;AACA,aAAOV,OAAO,CAACO,eAAR,CACLM,gBAAKC,QAAL,CAAcf,aAAa,CAACO,SAA5B,EAAuCI,KAAvC,CADK,EAEL;AAACK,QAAAA,MAAM,EAAEf,OAAO,CAACgB,cAAR,GAAyBC;AAAlC,OAFK,CAAP;AAID,KAPI,EAQJC,MARI,CAQGC,OARH,EAQY;AARZ,KASJV,GATI,CASCW,IAAD,IAAUA,IAAI,CAACC,QATf,CAAP;AAUD;;AAEDd,EAAAA,eAAe,CAACH,MAAM,CAACkB,KAAR,CAAf;AAEA,QAAMC,KAAK,GAAGhB,eAAe,CAACH,MAAM,CAACoB,IAAR,CAA7B,CAtBA,CAwBA;;AACA,QAAMC,aAAa,GAAGpB,KAAK,CAACqB,UAAN,IAAoB,EAA1C;AACA,QAAMC,QAAQ,GAAG,CAAC,GAAGF,aAAJ,EAAmB,GAAGF,KAAtB,CAAjB,CA1BA,CA2BA;;AACAlB,EAAAA,KAAK,CAACqB,UAAN,GAAmBE,KAAK,CAACC,IAAN,CAAW,IAAIC,GAAJ,CAAQH,QAAR,CAAX,CAAnB,CA5BA,CA4BkD;;AAElDI,gBAAGC,aAAH,CAAiBjC,aAAa,CAACG,WAA/B,EAA4CF,OAAO,CAACiC,SAAR,EAA5C;;AAEA,2BAAWjC,OAAX,EAAoBD,aAAa,CAACO,SAAlC,EAA6CD,KAA7C;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport path from 'path';\nimport xcode from 'xcode';\nimport createGroupWithMessage from './createGroupWithMessage';\nimport getPlist from './getPlist';\nimport writePlist from './writePlist';\nimport {logger, groupFilesByType} from '@react-native-community/cli-tools';\nimport {IOSProjectConfig} from '@react-native-community/cli-types';\n\n/**\n * This function works in a similar manner to its Android version,\n * except it does not copy fonts but creates Xcode Group references\n */\nexport default function linkAssetsIOS(\n  files: Array<string>,\n  projectConfig: IOSProjectConfig,\n) {\n  const project = xcode.project(projectConfig.pbxprojPath).parseSync();\n  const assets = groupFilesByType(files);\n  const plist = getPlist(project, projectConfig.sourceDir);\n\n  createGroupWithMessage(project, 'Resources');\n\n  function addResourceFile(f: Array<string>) {\n    return (f || [])\n      .map((asset) => {\n        logger.debug(`Linking asset ${asset}`);\n        return project.addResourceFile(\n          path.relative(projectConfig.sourceDir, asset),\n          {target: project.getFirstTarget().uuid},\n        );\n      })\n      .filter(Boolean) // xcode returns false if file is already there\n      .map((file) => file.basename);\n  }\n\n  addResourceFile(assets.image);\n\n  const fonts = addResourceFile(assets.font);\n\n  // @ts-ignore Type mismatch with the lib\n  const existingFonts = plist.UIAppFonts || [];\n  const allFonts = [...existingFonts, ...fonts];\n  // @ts-ignore Type mismatch with the lib\n  plist.UIAppFonts = Array.from(new Set(allFonts)); // use Set to dedupe w/existing\n\n  fs.writeFileSync(projectConfig.pbxprojPath, project.writeSync());\n\n  writePlist(project, projectConfig.sourceDir, plist);\n}\n"]}