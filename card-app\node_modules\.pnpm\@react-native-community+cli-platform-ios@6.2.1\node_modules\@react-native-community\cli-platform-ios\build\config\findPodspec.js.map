{"version": 3, "sources": ["../../src/config/findPodspec.ts"], "names": ["findPodspec", "folder", "podspecs", "glob", "sync", "cwd", "length", "packagePodspec", "path", "basename", "podspecFile", "includes", "join"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEe,SAASA,WAAT,CAAqBC,MAArB,EAAoD;AACjE,QAAMC,QAAQ,GAAGC,gBAAKC,IAAL,CAAU,WAAV,EAAuB;AAACC,IAAAA,GAAG,EAAEJ;AAAN,GAAvB,CAAjB;;AAEA,MAAIC,QAAQ,CAACI,MAAT,KAAoB,CAAxB,EAA2B;AACzB,WAAO,IAAP;AACD;;AAED,QAAMC,cAAc,GAAGC,gBAAKC,QAAL,CAAcR,MAAd,IAAwB,UAA/C;AACA,QAAMS,WAAW,GAAGR,QAAQ,CAACS,QAAT,CAAkBJ,cAAlB,IAChBA,cADgB,GAEhBL,QAAQ,CAAC,CAAD,CAFZ;AAIA,SAAOM,gBAAKI,IAAL,CAAUX,MAAV,EAAkBS,WAAlB,CAAP;AACD", "sourcesContent": ["import glob from 'glob';\nimport path from 'path';\n\nexport default function findPodspec(folder: string): string | null {\n  const podspecs = glob.sync('*.podspec', {cwd: folder});\n\n  if (podspecs.length === 0) {\n    return null;\n  }\n\n  const packagePodspec = path.basename(folder) + '.podspec';\n  const podspecFile = podspecs.includes(packagePodspec)\n    ? packagePodspec\n    : podspecs[0];\n\n  return path.join(folder, podspecFile);\n}\n"]}