{"version": 3, "sources": ["../../../src/link/common/registerNativeModule.ts"], "names": ["registerNativeModule", "name", "dependencyConfig", "_params", "projectConfig", "podfile", "podspecPath"], "mappings": ";;;;;;;AAYA;;AACA;;;;AAbA;AACA;AACA;AACA;AACA;AACA;AACA;AASe,SAASA,oBAAT,CACbC,IADa,EAEbC,gBAFa,EAGb;AACAC,OAJa,EAKbC,aALa,EAMb;AACA,MAAIA,aAAa,CAACC,OAAd,IAAyBH,gBAAgB,CAACI,WAA9C,EAA2D;AACzD,wCAAuBL,IAAvB,EAA6BC,gBAAgB,CAACI,WAA9C,EAA2DF,aAA3D;AACD,GAFD,MAEO;AACL,uCAAsBF,gBAAtB,EAAwCE,aAAxC;AACD;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  IOSDependencyConfig,\n  IOSProjectConfig,\n} from '@react-native-community/cli-types';\nimport registerDependencyIOS from '../registerNativeModule';\nimport registerDependencyPods from '../../link-pods/registerNativeModule';\n\nexport default function registerNativeModule(\n  name: string,\n  dependencyConfig: IOSDependencyConfig,\n  // FIXME: Params is never used\n  _params: any | undefined,\n  projectConfig: IOSProjectConfig,\n) {\n  if (projectConfig.podfile && dependencyConfig.podspecPath) {\n    registerDependencyPods(name, dependencyConfig.podspecPath, projectConfig);\n  } else {\n    registerDependencyIOS(dependencyConfig, projectConfig);\n  }\n}\n"]}