load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "robolectric",
    autoglob = False,
    visibility = ["PUBLIC"],
    exported_deps = [
        ":android-all-5.0.2_r3-robolectric-r0",
        ":bouncycastle",
        ":guava",
        ":javax-annotation-api",
        ":javax-inject",
        ":robolectric4-annotations-prebuilt",
        ":robolectric4-junit-prebuilt",
        ":robolectric4-pluginapi-prebuilt",
        ":robolectric4-plugins-maven-dependency-resolver-prebuilt",
        ":robolectric4-prebuilt",
        ":robolectric4-resources-prebuilt",
        ":robolectric4-sandbox-prebuilt",
        ":robolectric4-shadowapi-prebuilt",
        ":robolectric4-shadows-framework-prebuilt",
        ":robolectric4-utils-prebuilt",
        ":robolectric4-utils-reflector-prebuilt",
        react_native_dep("third-party/java/asm:asm"),
        react_native_dep("third-party/java/sqlite:sqlite"),
        react_native_dep("third-party/java/junit:junit"),
        react_native_dep("third-party/android/androidx:test-monitor"),
    ],
)

# This is based on the minimum SDK version for tests: 21.
rn_prebuilt_jar(
    name = "android-all-5.0.2_r3-robolectric-r0",  # name defines filename used by robolectric in runtime
    binary_jar = ":robolectric-android-all-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

# This new rule will make the .jar file appear in the "right" location,
# though that may change in the future
fb_native.export_file(
    name = "robolectric-android-all-binary.jar",
    src = ":robolectric-android-all-binary-remote.jar",
    out = "../android-all-5.0.2_r3-robolectric-r0.jar",  # name defines filename used by robolectric in runtime
)

fb_native.remote_file(
    name = "robolectric-android-all-binary-remote.jar",
    sha1 = "ae6e8f47f73ffe34054852d9c7f4f4ec489254f1",
    url = "mvn:org.robolectric:android-all:jar:5.0.2_r3-robolectric-r0",
)

rn_prebuilt_jar(
    name = "bouncycastle",
    binary_jar = ":bouncycastle-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "bouncycastle-binary.jar",
    sha1 = "320b989112f00a63a3bcfa5a98f31a4f865a20fa",
    url = "mvn:org.bouncycastle:bcprov-jdk15on:jar:1.65",
)

rn_prebuilt_jar(
    name = "guava",
    binary_jar = ":guava-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "guava-binary.jar",
    sha1 = "ef69663836b339db335fde0df06fb3cd84e3742b",
    url = "mvn:com.google.guava:guava:jar:26.0-android",
)

rn_prebuilt_jar(
    name = "robolectric4-prebuilt",
    binary_jar = ":robolectric4.jar",
)

fb_native.remote_file(
    name = "robolectric4.jar",
    sha1 = "418c5bfae392fdbf71cd463a42a3e8c3b839a924",
    url = "mvn:org.robolectric:robolectric:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-annotations-prebuilt",
    binary_jar = ":robolectric4-annotations.jar",
)

fb_native.remote_file(
    name = "robolectric4-annotations.jar",
    sha1 = "70fc5b1699467dfd7de606fc6c02ff9fc1816d9f",
    url = "mvn:org.robolectric:annotations:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-junit-prebuilt",
    binary_jar = ":robolectric4-junit.jar",
)

fb_native.remote_file(
    name = "robolectric4-junit.jar",
    sha1 = "fbcda51d8e6f3a3897ae5cedc7aa481815745290",
    url = "mvn:org.robolectric:junit:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-pluginapi-prebuilt",
    binary_jar = ":robolectric4-pluginapi.jar",
)

fb_native.remote_file(
    name = "robolectric4-pluginapi.jar",
    sha1 = "1ee94260f8c51620a35eac33fc1efc01350c751f",
    url = "mvn:org.robolectric:pluginapi:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-plugins-maven-dependency-resolver-prebuilt",
    binary_jar = ":robolectric4-plugins-maven-dependency-resolver.jar",
)

fb_native.remote_file(
    name = "robolectric4-plugins-maven-dependency-resolver.jar",
    sha1 = "9241a3c4bd01627447c76d9b67614808c78ffdd9",
    url = "mvn:org.robolectric:plugins-maven-dependency-resolver:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-resources-prebuilt",
    binary_jar = ":robolectric4-resources.jar",
)

fb_native.remote_file(
    name = "robolectric4-resources.jar",
    sha1 = "a2ee1324bcb62724e6cbfa655bdb5683948a554c",
    url = "mvn:org.robolectric:resources:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-sandbox-prebuilt",
    binary_jar = ":robolectric4-sandbox.jar",
)

fb_native.remote_file(
    name = "robolectric4-sandbox.jar",
    sha1 = "03cedd73c5aedaf79fb9a593552816c9fb3282f2",
    url = "mvn:org.robolectric:sandbox:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-shadowapi-prebuilt",
    binary_jar = ":robolectric4-shadowapi.jar",
)

fb_native.remote_file(
    name = "robolectric4-shadowapi.jar",
    sha1 = "529649474b53cf8f6f4a483044ade43aebed8a4c",
    url = "mvn:org.robolectric:shadowapi:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-shadows-framework-prebuilt",
    binary_jar = ":robolectric4-shadows-framework.jar",
)

fb_native.remote_file(
    name = "robolectric4-shadows-framework.jar",
    sha1 = "90028766e71353ad6f57d7bcb56ac0d861da18c3",
    url = "mvn:org.robolectric:shadows-framework:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-utils-prebuilt",
    binary_jar = ":robolectric4-utils.jar",
)

fb_native.remote_file(
    name = "robolectric4-utils.jar",
    sha1 = "c54b2638d64e7bd4e1e45c4fe8038305402bd711",
    url = "mvn:org.robolectric:utils:jar:4.4",
)

rn_prebuilt_jar(
    name = "robolectric4-utils-reflector-prebuilt",
    binary_jar = ":robolectric4-utils-reflector.jar",
)

fb_native.remote_file(
    name = "robolectric4-utils-reflector.jar",
    sha1 = "44c40ac0d2ef1e7c8b0f6c4e224ef26d356170f1",
    url = "mvn:org.robolectric:utils-reflector:jar:4.4",
)

rn_prebuilt_jar(
    name = "javax-annotation-api",
    binary_jar = ":javax-annotation-api.jar",
)

fb_native.remote_file(
    name = "javax-annotation-api.jar",
    sha1 = "934c04d3cfef185a8008e7bf34331b79730a9d43",
    url = "mvn:javax.annotation:javax.annotation-api:jar:1.3.2",
)

rn_prebuilt_jar(
    name = "javax-inject",
    binary_jar = ":javax-inject.jar",
)

fb_native.remote_file(
    name = "javax-inject.jar",
    sha1 = "6975da39a7040257bd51d21a231b76c915872d38",
    url = "mvn:javax.inject:javax.inject:jar:1",
)
