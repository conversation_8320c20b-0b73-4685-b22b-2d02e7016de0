{"version": 3, "sources": ["../src/fetch.ts"], "names": ["unwrapFetchResult", "response", "data", "text", "JSON", "parse", "e", "fetchToTemp", "url", "Promise", "resolve", "reject", "fileName", "path", "basename", "tmpDir", "join", "os", "tmpdir", "then", "result", "status", "dest", "fs", "createWriteStream", "body", "pipe", "on", "logger", "error", "fetch", "options", "CLIError", "headers"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAMA;;AACA;;;;;;;;AAEA,eAAeA,iBAAf,CAAiCC,QAAjC,EAAqD;AACnD,QAAMC,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAT,EAAnB;;AAEA,MAAI;AACF,WAAOC,IAAI,CAACC,KAAL,CAAWH,IAAX,CAAP;AACD,GAFD,CAEE,OAAOI,CAAP,EAAU;AACV,WAAOJ,IAAP;AACD;AACF;AAED;AACA;AACA;AACA;;;AACA,MAAMK,WAAW,GAAIC,GAAD,IAAkC;AACpD,MAAI;AACF,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtC,YAAMC,QAAQ,GAAGC,IAAI,GAACC,QAAL,CAAcN,GAAd,CAAjB;AACA,YAAMO,MAAM,GAAGF,IAAI,GAACG,IAAL,CAAUC,EAAE,GAACC,MAAH,EAAV,EAAuBN,QAAvB,CAAf;AAEA,gCAAUJ,GAAV,EAAeW,IAAf,CAAqBC,MAAD,IAAY;AAC9B,YAAIA,MAAM,CAACC,MAAP,IAAiB,GAArB,EAA0B;AACxB,iBAAOV,MAAM,CAAE,oCAAmCS,MAAM,CAACC,MAAO,EAAnD,CAAb;AACD;;AAED,cAAMC,IAAI,GAAGC,EAAE,GAACC,iBAAH,CAAqBT,MAArB,CAAb;AACAK,QAAAA,MAAM,CAACK,IAAP,CAAYC,IAAZ,CAAiBJ,IAAjB;AAEAF,QAAAA,MAAM,CAACK,IAAP,CAAYE,EAAZ,CAAe,KAAf,EAAsB,MAAM;AAC1BjB,UAAAA,OAAO,CAACK,MAAD,CAAP;AACD,SAFD;AAIAK,QAAAA,MAAM,CAACK,IAAP,CAAYE,EAAZ,CAAe,OAAf,EAAwBhB,MAAxB;AACD,OAbD;AAcD,KAlBM,CAAP;AAmBD,GApBD,CAoBE,OAAOL,CAAP,EAAU;AACVsB,oBAAOC,KAAP,CAAavB,CAAb;;AACA,UAAMA,CAAN;AACD;AACF,CAzBD;;;;AA2BA,MAAMwB,KAAK,GAAG,OACZtB,GADY,EAEZuB,OAFY,KAG+C;AAC3D,QAAMX,MAAM,GAAG,MAAM,0BAAUZ,GAAV,EAAeuB,OAAf,CAArB;AACA,QAAM7B,IAAI,GAAG,MAAMF,iBAAiB,CAACoB,MAAD,CAApC;;AAEA,MAAIA,MAAM,CAACC,MAAP,IAAiB,GAArB,EAA0B;AACxB,UAAM,IAAIW,gBAAJ,CACH,oCAAmCZ,MAAM,CAACC,MAAO,KAAInB,IAAK,GADvD,CAAN;AAGD;;AAED,SAAO;AACLmB,IAAAA,MAAM,EAAED,MAAM,CAACC,MADV;AAELY,IAAAA,OAAO,EAAEb,MAAM,CAACa,OAFX;AAGL/B,IAAAA;AAHK,GAAP;AAKD,CAlBD", "sourcesContent": ["import * as os from 'os';\nimport * as path from 'path';\nimport * as fs from 'fs';\n\nimport nodeFetch, {\n  RequestInit as FetchOptions,\n  Response,\n  Request,\n  Headers,\n} from 'node-fetch';\nimport {CLIError} from './errors';\nimport logger from './logger';\n\nasync function unwrapFetchResult(response: Response) {\n  const data = await response.text();\n\n  try {\n    return JSON.parse(data);\n  } catch (e) {\n    return data;\n  }\n}\n\n/**\n * Downloads the given `url` to the OS's temp folder and\n * returns the path to it.\n */\nconst fetchToTemp = (url: string): Promise<string> => {\n  try {\n    return new Promise((resolve, reject) => {\n      const fileName = path.basename(url);\n      const tmpDir = path.join(os.tmpdir(), fileName);\n\n      nodeFetch(url).then((result) => {\n        if (result.status >= 400) {\n          return reject(`Fetch request failed with status ${result.status}`);\n        }\n\n        const dest = fs.createWriteStream(tmpDir);\n        result.body.pipe(dest);\n\n        result.body.on('end', () => {\n          resolve(tmpDir);\n        });\n\n        result.body.on('error', reject);\n      });\n    });\n  } catch (e) {\n    logger.error(e);\n    throw e;\n  }\n};\n\nconst fetch = async (\n  url: string | Request,\n  options?: FetchOptions,\n): Promise<{status: number; data: any; headers: Headers}> => {\n  const result = await nodeFetch(url, options);\n  const data = await unwrapFetchResult(result);\n\n  if (result.status >= 400) {\n    throw new CLIError(\n      `Fetch request failed with status ${result.status}: ${data}.`,\n    );\n  }\n\n  return {\n    status: result.status,\n    headers: result.headers,\n    data,\n  };\n};\n\nexport {fetch, fetchToTemp};\n"]}