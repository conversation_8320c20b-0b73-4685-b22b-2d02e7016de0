{"version": 3, "sources": ["../../src/android/constants.ts"], "names": ["codes", "UNKNOWN", "VERBOSE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL", "SILENT", "Priority", "fromName", "name", "value", "toUpperCase", "to<PERSON>ame", "code", "Object", "keys", "find", "key", "fromLetter", "letter", "toLetter"], "mappings": ";;;;;;AAAA,MAAMA,KAAK,GAAG;AACZC,EAAAA,OAAO,EAAE,CADG;AAEZC,EAAAA,OAAO,EAAE,CAFG;AAGZC,EAAAA,KAAK,EAAE,CAHK;AAIZC,EAAAA,IAAI,EAAE,CAJM;AAKZC,EAAAA,IAAI,EAAE,CALM;AAMZC,EAAAA,KAAK,EAAE,CANK;AAOZC,EAAAA,KAAK,EAAE,CAPK;AAQZC,EAAAA,MAAM,EAAE;AARI,CAAd;AAaO,MAAMC,QAAQ,GAAG,EACtB,GAAGT,KADmB;;AAEtBU,EAAAA,QAAQ,CAACC,IAAD,EAA8B;AACpC,UAAMC,KAAK,GAAGZ,KAAK,CAACW,IAAI,CAACE,WAAL,EAAD,CAAnB;AACA,WAAOD,KAAK,GAAGA,KAAH,GAAW,CAAvB;AACD,GALqB;;AAMtBE,EAAAA,MAAM,CAACC,IAAD,EAA8B;AAClC,WACGC,MAAM,CAACC,IAAP,CAAYjB,KAAZ,CAAD,CAAwCkB,IAAxC,CACGC,GAAD,IAAwBnB,KAAK,CAACmB,GAAD,CAAL,KAAeJ,IADzC,KAEK,SAHP;AAKD,GAZqB;;AAatBK,EAAAA,UAAU,CAACC,MAAD,EAAyB;AACjC,WAAOrB,KAAK,CACTgB,MAAM,CAACC,IAAP,CAAYjB,KAAZ,CAAD,CAAwCkB,IAAxC,CACGC,GAAD,IAAwBA,GAAG,CAAC,CAAD,CAAH,KAAWE,MAAM,CAACR,WAAP,EADrC,KAEK,SAHK,CAAZ;AAKD,GAnBqB;;AAoBtBS,EAAAA,QAAQ,CAACP,IAAD,EAAuB;AAC7B,WAAON,QAAQ,CAACK,MAAT,CAAgBC,IAAhB,EAAsB,CAAtB,CAAP;AACD;;AAtBqB,CAAjB", "sourcesContent": ["const codes = {\n  UNKNOWN: 0,\n  VERBOSE: 1,\n  DEBUG: 2,\n  INFO: 3,\n  WARN: 4,\n  ERROR: 5,\n  FATAL: 6,\n  SILENT: 7,\n};\n\nexport type PriorityNames = keyof typeof codes;\n\nexport const Priority = {\n  ...codes,\n  fromName(name: PriorityNames): number {\n    const value = codes[name.toUpperCase() as PriorityNames];\n    return value ? value : 0;\n  },\n  toName(code: number): PriorityNames {\n    return (\n      (Object.keys(codes) as PriorityNames[]).find(\n        (key: PriorityNames) => codes[key] === code\n      ) || 'UNKNOWN'\n    );\n  },\n  fromLetter(letter: string): number {\n    return codes[\n      (Object.keys(codes) as PriorityNames[]).find(\n        (key: PriorityNames) => key[0] === letter.toUpperCase()\n      ) || 'UNKNOWN'\n    ];\n  },\n  toLetter(code: number): string {\n    return Priority.toName(code)[0];\n  },\n};\n"], "file": "constants.js"}