{"version": 3, "names": ["handlePortUnavailable", "initialPort", "projectRoot", "nextPort", "start", "getNextPort", "packager", "port", "logAlreadyRunningBundler", "change", "askForPortChange", "logChangePortInstructions"], "sources": ["../src/handlePortUnavailable.ts"], "sourcesContent": ["import getNextPort from './getNextPort';\nimport {\n  askForPortChange,\n  logAlreadyRunningBundler,\n  logChangePortInstructions,\n} from './port';\n\nconst handlePortUnavailable = async (\n  initialPort: number,\n  projectRoot: string,\n): Promise<{\n  port: number;\n  packager: boolean;\n}> => {\n  const {nextPort, start} = await getNextPort(initialPort, projectRoot);\n  let packager = true;\n  let port = initialPort;\n\n  if (!start) {\n    packager = false;\n    logAlreadyRunningBundler(nextPort);\n  } else {\n    const {change} = await askForPortChange(port, nextPort);\n\n    if (change) {\n      port = nextPort;\n    } else {\n      packager = false;\n      logChangePortInstructions();\n    }\n  }\n\n  return {\n    port,\n    packager,\n  };\n};\n\nexport default handlePortUnavailable;\n"], "mappings": ";;;;;;AAAA;AACA;AAIgB;AAEhB,MAAMA,qBAAqB,GAAG,OAC5BC,WAAmB,EACnBC,WAAmB,KAIf;EACJ,MAAM;IAACC,QAAQ;IAAEC;EAAK,CAAC,GAAG,MAAM,IAAAC,oBAAW,EAACJ,WAAW,EAAEC,WAAW,CAAC;EACrE,IAAII,QAAQ,GAAG,IAAI;EACnB,IAAIC,IAAI,GAAGN,WAAW;EAEtB,IAAI,CAACG,KAAK,EAAE;IACVE,QAAQ,GAAG,KAAK;IAChB,IAAAE,8BAAwB,EAACL,QAAQ,CAAC;EACpC,CAAC,MAAM;IACL,MAAM;MAACM;IAAM,CAAC,GAAG,MAAM,IAAAC,sBAAgB,EAACH,IAAI,EAAEJ,QAAQ,CAAC;IAEvD,IAAIM,MAAM,EAAE;MACVF,IAAI,GAAGJ,QAAQ;IACjB,CAAC,MAAM;MACLG,QAAQ,GAAG,KAAK;MAChB,IAAAK,+BAAyB,GAAE;IAC7B;EACF;EAEA,OAAO;IACLJ,IAAI;IACJD;EACF,CAAC;AACH,CAAC;AAAC,eAEaN,qBAAqB;AAAA"}