{"version": 3, "sources": ["../../src/link/warnAboutManuallyLinkedLibs.ts"], "names": ["warnAboutManuallyLinkedLibs", "config", "platform", "linkConfig", "deps", "projectConfig", "project", "key", "dependencies", "dependency", "dependencyConfig", "platforms", "x", "isInstalled", "name", "concat", "installedModules", "Set", "length", "logger", "error", "map", "chalk", "bold", "dim", "join", "underline"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;;;AAEA;AACA;AACe,SAASA,2BAAT,CACbC,MADa,EAEbC,QAAgB,GAAG,KAFN,EAGbC,UAEC,GAAG,qBALS,EAMb;AACA,MAAIC,IAAmB,GAAG,EAA1B;AACA,QAAMC,aAAa,GAAGJ,MAAM,CAACK,OAAP,CAAeJ,QAAf,CAAtB;;AAEA,OAAK,IAAIK,GAAT,IAAgBN,MAAM,CAACO,YAAvB,EAAqC;AACnC,UAAMC,UAAU,GAAGR,MAAM,CAACO,YAAP,CAAoBD,GAApB,CAAnB;AAEA,UAAMG,gBAAgB,GAAGD,UAAU,CAACE,SAAX,CAAqBT,QAArB,CAAzB;;AACA,QAAIG,aAAa,IAAIK,gBAArB,EAAuC;AACrC,YAAME,CAAC,GAAGT,UAAU,CAACU,WAAX,CACRR,aADQ,EAERI,UAAU,CAACK,IAFH,EAGRJ,gBAHQ,CAAV;AAKAN,MAAAA,IAAI,GAAGA,IAAI,CAACW,MAAL,CAAYH,CAAC,GAAGH,UAAU,CAACK,IAAd,GAAqB,EAAlC,CAAP;AACD;AACF;;AAED,QAAME,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAJ,CAAQb,IAAR,CAAJ,CAAzB;;AAEA,MAAIY,gBAAgB,CAACE,MAArB,EAA6B;AAC3BC,uBAAOC,KAAP,CACG,+GAA8GJ,gBAAgB,CAC5HK,GAD4G,CAE1GT,CAAD,IACG,OAAMU,iBAAMC,IAAN,CAAWX,CAAX,CAAc,IAAGU,iBAAME,GAAN,CACrB,wCAAuCZ,CAAE,IADpB,CAEtB,EALuG,EAO5Ga,IAP4G,CAQ3G,IAR2G,CAS3G,6WAA4WH,iBAAME,GAAN,CAAUE,SAAV,CAC9W,+EAD8W,CAE9W,EAZJ;AAcD;AACF", "sourcesContent": ["import chalk from 'chalk';\nimport {logger} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport getLinkConfig from './index';\n\n// TODO: move to cli-tools once platform-ios and platform-android are migrated\n// to TS and unify with Android implementation\nexport default function warnAboutManuallyLinkedLibs(\n  config: Config,\n  platform: string = 'ios',\n  linkConfig: ReturnType<\n    Config['platforms']['ios']['linkConfig']\n  > = getLinkConfig(),\n) {\n  let deps: Array<string> = [];\n  const projectConfig = config.project[platform];\n\n  for (let key in config.dependencies) {\n    const dependency = config.dependencies[key];\n\n    const dependencyConfig = dependency.platforms[platform];\n    if (projectConfig && dependencyConfig) {\n      const x = linkConfig.isInstalled(\n        projectConfig,\n        dependency.name,\n        dependencyConfig,\n      );\n      deps = deps.concat(x ? dependency.name : []);\n    }\n  }\n\n  const installedModules = [...new Set(deps)];\n\n  if (installedModules.length) {\n    logger.error(\n      `React Native CLI uses autolinking for native dependencies, but the following modules are linked manually: \\n${installedModules\n        .map(\n          (x) =>\n            `  - ${chalk.bold(x)} ${chalk.dim(\n              `(to unlink run: \"react-native unlink ${x}\")`,\n            )}`,\n        )\n        .join(\n          '\\n',\n        )}\\nThis is likely happening when upgrading React Native from below 0.60 to 0.60 or above. Going forward, you can unlink this dependency via \"react-native unlink <dependency>\" and it will be included in your app automatically. If a library isn't compatible with autolinking, disregard this message and notify the library maintainers.\\nRead more about autolinking: ${chalk.dim.underline(\n        'https://github.com/react-native-community/cli/blob/master/docs/autolinking.md',\n      )}`,\n    );\n  }\n}\n"]}