load("//tools/build_defs/oss:rn_defs.bzl", "JNI_TARGET", "oss_cxx_library", "subdir_glob")

# This target is only used in open source
oss_cxx_library(
    name = "jni",
    srcs = glob([
        "*.cpp",
        "jni/*.cpp",
        "lyra/*.cpp",
    ]),
    header_namespace = "",
    exported_headers = subdir_glob([
        ("include", "fb/**/*.h"),
        ("include", "fbjni/**/*.h"),
        ("include", "jni/*.h"),
    ]),
    compiler_flags = [
        "-fno-omit-frame-pointer",
        "-fexceptions",
        "-Wall",
        "-Werror",
        "-std=c++14",
        "-DDISABLE_CPUCAP",
        "-DDISABLE_XPLAT",
    ],
    linker_flags = [
        "-llog",
    ],
    preferred_linkage = "static",
    soname = "libfb.$(ext)",
    visibility = ["PUBLIC"],
    deps = [
        JNI_TARGET,
    ],
)
