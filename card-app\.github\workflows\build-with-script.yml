name: Build Android APK (Using build script)

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
        
    - name: Setup Java JDK
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: '11'
        
    - name: Install dependencies
      run: |
        cd card-app
        npm install
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Make scripts executable
      run: |
        chmod +x card-app/bin/build-github.sh
        chmod +x card-app/android/gradlew
        
    - name: Build using custom script
      run: |
        cd card-app
        ./bin/build-github.sh
        
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-release
        path: card-app/android/app/build/outputs/apk/release/app-release.apk
        
    - name: Upload APK (all variants)
      uses: actions/upload-artifact@v3
      with:
        name: all-apks
        path: card-app/android/app/build/outputs/apk/release/*.apk
