{"version": 3, "names": ["tryRunAdbReverse", "packagerPort", "device", "adbPath", "getAdbPath", "adbArgs", "unshift", "logger", "info", "debug", "join", "execFileSync", "stdio", "e", "warn", "message"], "sources": ["../../../src/commands/runAndroid/tryRunAdbReverse.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execFileSync} from 'child_process';\nimport {logger} from '@react-native-community/cli-tools';\nimport getAdbPath from './getAdbPath';\n\n// Runs ADB reverse tcp:8081 tcp:8081 to allow loading the jsbundle from the packager\nfunction tryRunAdbReverse(\n  packagerPort: number | string,\n  device?: string | void,\n) {\n  try {\n    const adbPath = getAdbPath();\n    const adbArgs = ['reverse', `tcp:${packagerPort}`, `tcp:${packagerPort}`];\n\n    // If a device is specified then tell adb to use it\n    if (device) {\n      adbArgs.unshift('-s', device);\n    }\n\n    logger.info('Connecting to the development server...');\n    logger.debug(`Running command \"${adbPath} ${adbArgs.join(' ')}\"`);\n\n    execFileSync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (e) {\n    logger.warn(\n      `Failed to connect to development server using \"adb reverse\": ${\n        (e as any).message\n      }`,\n    );\n  }\n}\n\nexport default tryRunAdbReverse;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAsC;AAVtC;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA,SAASA,gBAAgB,CACvBC,YAA6B,EAC7BC,MAAsB,EACtB;EACA,IAAI;IACF,MAAMC,OAAO,GAAG,IAAAC,mBAAU,GAAE;IAC5B,MAAMC,OAAO,GAAG,CAAC,SAAS,EAAG,OAAMJ,YAAa,EAAC,EAAG,OAAMA,YAAa,EAAC,CAAC;;IAEzE;IACA,IAAIC,MAAM,EAAE;MACVG,OAAO,CAACC,OAAO,CAAC,IAAI,EAAEJ,MAAM,CAAC;IAC/B;IAEAK,kBAAM,CAACC,IAAI,CAAC,yCAAyC,CAAC;IACtDD,kBAAM,CAACE,KAAK,CAAE,oBAAmBN,OAAQ,IAAGE,OAAO,CAACK,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;IAEjE,IAAAC,6BAAY,EAACR,OAAO,EAAEE,OAAO,EAAE;MAACO,KAAK,EAAE;IAAS,CAAC,CAAC;EACpD,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVN,kBAAM,CAACO,IAAI,CACR,gEACED,CAAC,CAASE,OACZ,EAAC,CACH;EACH;AACF;AAAC,eAEcf,gBAAgB;AAAA"}