/**
* Copyright (c) Facebook, Inc. and its affiliates.
*
* This source code is licensed under the MIT license found in the
* LICENSE file in the root directory of this source tree.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;

public interface AndroidViewPagerManagerInterface<T extends View> {
  void setInitialPage(T view, int value);
  void setPageMargin(T view, int value);
  void setPeekEnabled(T view, boolean value);
  void setKeyboardDismissMode(T view, @Nullable String value);
  void setScrollEnabled(T view, boolean value);
  void setPage(T view, int page);
  void setPageWithoutAnimation(T view, int page);
}
