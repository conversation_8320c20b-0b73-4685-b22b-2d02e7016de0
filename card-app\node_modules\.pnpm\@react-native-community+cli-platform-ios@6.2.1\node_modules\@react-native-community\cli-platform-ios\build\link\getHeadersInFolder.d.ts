/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
/**
 * Given folder, it returns an array of all header files
 * inside it, ignoring node_modules and examples
 */
export default function getHeadersInFolder(folder: string): string[];
//# sourceMappingURL=getHeadersInFolder.d.ts.map