{"version": 3, "sources": ["../../src/link-pods/getDependenciesFromPodfileLock.ts"], "names": ["CHECKSUM_KEY", "getDependenciesFromPodfileLock", "podfileLockPath", "logger", "debug", "fileContent", "fs", "readFileSync", "err", "error", "chalk", "dim", "bold", "tail", "split", "slice", "checksumTail", "Object", "keys"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEA,MAAMA,YAAY,GAAG,gBAArB;;AAEe,SAASC,8BAAT,CACbC,eADa,EAEb;AACAC,qBAAOC,KAAP,CAAc,WAAUF,eAAgB,EAAxC;;AACA,MAAIG,WAAJ;;AACA,MAAI;AACFA,IAAAA,WAAW,GAAGC,cAAGC,YAAH,CAAgBL,eAAhB,EAAiC,MAAjC,CAAd;AACD,GAFD,CAEE,OAAOM,GAAP,EAAY;AACZL,uBAAOM,KAAP,CACG,oCAAmCC,iBAAMC,GAAN,CAClCT,eADkC,CAElC,kBAAiBQ,iBAAME,IAAN,CAAW,aAAX,CAA0B,qBAH/C;;AAKA,WAAO,EAAP;AACD,GAZD,CAcA;AACA;;;AACA,QAAMC,IAAI,GAAGR,WAAW,CAACS,KAAZ,CAAkBd,YAAlB,EAAgCe,KAAhC,CAAsC,CAAtC,CAAb;AACA,QAAMC,YAAY,GAAGhB,YAAY,GAAGa,IAApC;AAEA,SAAOI,MAAM,CAACC,IAAP,CAAY,wBAASF,YAAT,EAAuBhB,YAAvB,KAAwC,EAApD,CAAP;AACD", "sourcesContent": ["import fs from 'fs';\nimport chalk from 'chalk';\nimport {logger} from '@react-native-community/cli-tools';\nimport {safeLoad} from 'js-yaml';\n\nconst CHECKSUM_KEY = 'SPEC CHECKSUMS';\n\nexport default function getDependenciesFromPodfileLock(\n  podfileLockPath: string,\n) {\n  logger.debug(`Reading ${podfileLockPath}`);\n  let fileContent;\n  try {\n    fileContent = fs.readFileSync(podfileLockPath, 'utf8');\n  } catch (err) {\n    logger.error(\n      `Could not find \"Podfile.lock\" at ${chalk.dim(\n        podfileLockPath,\n      )}. Did you run \"${chalk.bold('pod install')}\" in iOS directory?`,\n    );\n    return [];\n  }\n\n  // Previous portions of the lock file could be invalid yaml.\n  // Only parse parts that are valid\n  const tail = fileContent.split(CHECKSUM_KEY).slice(1);\n  const checksumTail = CHECKSUM_KEY + tail;\n\n  return Object.keys(safeLoad(checksumTail)[CHECKSUM_KEY] || {});\n}\n"]}