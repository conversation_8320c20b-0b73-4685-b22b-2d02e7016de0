{"version": 3, "names": ["getNextPort", "port", "root", "nextPort", "start", "result", "isPackagerRunning", "isRunning", "status"], "sources": ["../src/getNextPort.ts"], "sourcesContent": ["import isPackagerRunning from './isPackagerRunning';\n\ntype Result = {\n  start: boolean;\n  nextPort: number;\n};\n\n/**\n * Increases by one the port number until it finds an available port.\n * @param port Port number to start with.\n * @param root Root of the project.\n */\n\nconst getNextPort = async (port: number, root: string): Promise<Result> => {\n  let nextPort = port + 1;\n  let start = true;\n\n  const result = await isPackagerRunning(nextPort);\n\n  const isRunning = typeof result === 'object' && result.status === 'running';\n\n  if (isRunning && result.root === root) {\n    // Found running bundler for this project, so we do not need to start packager!\n    start = false;\n  } else if (isRunning || result === 'unrecognized') {\n    return getNextPort(nextPort, root);\n  }\n\n  return {\n    start,\n    nextPort,\n  };\n};\n\nexport default getNextPort;\n"], "mappings": ";;;;;;AAAA;AAAoD;AAOpD;AACA;AACA;AACA;AACA;;AAEA,MAAMA,WAAW,GAAG,OAAOC,IAAY,EAAEC,IAAY,KAAsB;EACzE,IAAIC,QAAQ,GAAGF,IAAI,GAAG,CAAC;EACvB,IAAIG,KAAK,GAAG,IAAI;EAEhB,MAAMC,MAAM,GAAG,MAAM,IAAAC,0BAAiB,EAACH,QAAQ,CAAC;EAEhD,MAAMI,SAAS,GAAG,OAAOF,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACG,MAAM,KAAK,SAAS;EAE3E,IAAID,SAAS,IAAIF,MAAM,CAACH,IAAI,KAAKA,IAAI,EAAE;IACrC;IACAE,KAAK,GAAG,KAAK;EACf,CAAC,MAAM,IAAIG,SAAS,IAAIF,MAAM,KAAK,cAAc,EAAE;IACjD,OAAOL,WAAW,CAACG,QAAQ,EAAED,IAAI,CAAC;EACpC;EAEA,OAAO;IACLE,KAAK;IACLD;EACF,CAAC;AACH,CAAC;AAAC,eAEaH,WAAW;AAAA"}