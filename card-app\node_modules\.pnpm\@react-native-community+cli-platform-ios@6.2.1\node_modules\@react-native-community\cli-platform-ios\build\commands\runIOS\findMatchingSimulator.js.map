{"version": 3, "sources": ["../../../src/commands/runIOS/findMatchingSimulator.ts"], "names": ["findMatchingSimulator", "simulators", "findOptions", "devices", "simulatorVersion", "simulatorName", "simulator", "parsedSimulatorName", "match", "undefined", "versionDescriptor", "device", "version", "test", "replace", "includes", "endsWith", "i", "availability", "isAvailable", "booted", "state", "simulatorDescriptor", "udid", "name"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAT,CACEC,UADF,EAEEC,WAFF,EAGE;AACA,MAAI,CAACD,UAAU,CAACE,OAAhB,EAAyB;AACvB,WAAO,IAAP;AACD;;AACD,QAAMA,OAAO,GAAGF,UAAU,CAACE,OAA3B;AACA,MAAIC,gBAAJ;AACA,MAAIC,aAAa,GAAG,IAApB;;AAEA,MAAIH,WAAW,IAAIA,WAAW,CAACI,SAA/B,EAA0C;AACxC,UAAMC,mBAAmB,GAAGL,WAAW,CAACI,SAAZ,CAAsBE,KAAtB,CAC1B,4BAD0B,CAA5B;;AAGA,QAAID,mBAAmB,IAAIA,mBAAmB,CAAC,CAAD,CAAnB,KAA2BE,SAAtD,EAAiE;AAC/DL,MAAAA,gBAAgB,GAAGG,mBAAmB,CAAC,CAAD,CAAtC;AACAF,MAAAA,aAAa,GAAGE,mBAAmB,CAAC,CAAD,CAAnC;AACD,KAHD,MAGO;AACLF,MAAAA,aAAa,GAAGH,WAAW,CAACI,SAA5B;AACD;AACF;;AAED,MAAIE,KAAJ;;AACA,OAAK,MAAME,iBAAX,IAAgCP,OAAhC,EAAyC;AACvC,UAAMQ,MAAM,GAAGR,OAAO,CAACO,iBAAD,CAAtB;AACA,QAAIE,OAAO,GAAGF,iBAAd;;AAEA,QAAI,4CAA4CG,IAA5C,CAAiDD,OAAjD,CAAJ,EAA+D;AAC7D;AACAA,MAAAA,OAAO,GAAGA,OAAO,CAACE,OAAR,CACR,mEADQ,EAER,UAFQ,CAAV;AAID,KAVsC,CAYvC;;;AACA,QAAI,CAACF,OAAO,CAACG,QAAR,CAAiB,KAAjB,CAAD,IAA4B,CAACH,OAAO,CAACG,QAAR,CAAiB,MAAjB,CAAjC,EAA2D;AACzD;AACD;;AACD,QAAIX,gBAAgB,IAAI,CAACQ,OAAO,CAACI,QAAR,CAAiBZ,gBAAjB,CAAzB,EAA6D;AAC3D;AACD;;AACD,SAAK,MAAMa,CAAX,IAAgBN,MAAhB,EAAwB;AACtB,YAAML,SAAS,GAAGK,MAAM,CAACM,CAAD,CAAxB,CADsB,CAEtB;;AACA,UACEX,SAAS,CAACY,YAAV,KAA2B,aAA3B,IACA;AACAZ,MAAAA,SAAS,CAACa,WAAV,KAA0B,KAF1B,IAGAb,SAAS,CAACa,WAAV,KAA0B,IAJ5B,EAKE;AACA;AACD;;AACD,YAAMC,MAAM,GAAGd,SAAS,CAACe,KAAV,KAAoB,QAAnC;AACA,YAAMC,mBAAmB,GAAG;AAC1BC,QAAAA,IAAI,EAAEjB,SAAS,CAACiB,IADU;AAE1BC,QAAAA,IAAI,EAAElB,SAAS,CAACkB,IAFU;AAG1BJ,QAAAA,MAH0B;AAI1BR,QAAAA;AAJ0B,OAA5B;;AAMA,UAAIV,WAAW,IAAIA,WAAW,CAACqB,IAA/B,EAAqC;AACnC,YAAIjB,SAAS,CAACiB,IAAV,KAAmBrB,WAAW,CAACqB,IAAnC,EAAyC;AACvC,iBAAOD,mBAAP;AACD;AACF,OAJD,MAIO;AACL,YAAIF,MAAM,IAAIf,aAAa,KAAK,IAAhC,EAAsC;AACpC,iBAAOiB,mBAAP;AACD;;AACD,YAAIhB,SAAS,CAACkB,IAAV,KAAmBnB,aAAnB,IAAoC,CAACG,KAAzC,EAAgD;AAC9CA,UAAAA,KAAK,GAAGc,mBAAR;AACD,SANI,CAOL;;;AACA,YAAIjB,aAAa,KAAK,IAAlB,IAA0B,CAACG,KAA/B,EAAsC;AACpCA,UAAAA,KAAK,GAAGc,mBAAR;AACD;AACF;AACF;AACF;;AACD,MAAId,KAAJ,EAAW;AACT,WAAOA,KAAP;AACD;;AACD,SAAO,IAAP;AACD;;eAEcR,qB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {Device} from '../../types';\n\n/**\n * Takes in a parsed simulator list and a desired name, and returns an object with the matching simulator. The desired\n * name can optionally include the iOS version in between parenthesis after the device name. Ex: \"iPhone 6 (9.2)\" in\n * which case it'll attempt to find a simulator with the exact version specified.\n *\n * If the simulatorString argument is null, we'll go into default mode and return the currently booted simulator, or if\n * none is booted, it will be the first in the list.\n *\n * @param simulators a parsed list from `xcrun simctl list --json devices` command\n * @param simulatorString the string with the name of desired simulator. If null, it will use the currently\n *        booted simulator, or if none are booted, the first in the list.\n */\nfunction findMatchingSimulator(\n  simulators: {devices: {[index: string]: Array<Device>}},\n  findOptions?: null | {simulator?: string; udid?: string},\n) {\n  if (!simulators.devices) {\n    return null;\n  }\n  const devices = simulators.devices;\n  let simulatorVersion;\n  let simulatorName = null;\n\n  if (findOptions && findOptions.simulator) {\n    const parsedSimulatorName = findOptions.simulator.match(\n      /(.*)? (?:\\((\\d+\\.\\d+)?\\))$/,\n    );\n    if (parsedSimulatorName && parsedSimulatorName[2] !== undefined) {\n      simulatorVersion = parsedSimulatorName[2];\n      simulatorName = parsedSimulatorName[1];\n    } else {\n      simulatorName = findOptions.simulator;\n    }\n  }\n\n  let match;\n  for (const versionDescriptor in devices) {\n    const device = devices[versionDescriptor];\n    let version = versionDescriptor;\n\n    if (/^com\\.apple\\.CoreSimulator\\.SimRuntime\\./g.test(version)) {\n      // Transform \"com.apple.CoreSimulator.SimRuntime.iOS-12-2\" into \"iOS 12.2\"\n      version = version.replace(\n        /^com\\.apple\\.CoreSimulator\\.SimRuntime\\.([^-]+)-([^-]+)-([^-]+)$/g,\n        '$1 $2.$3',\n      );\n    }\n\n    // Making sure the version of the simulator is an iOS or tvOS (Removes Apple Watch, etc)\n    if (!version.includes('iOS') && !version.includes('tvOS')) {\n      continue;\n    }\n    if (simulatorVersion && !version.endsWith(simulatorVersion)) {\n      continue;\n    }\n    for (const i in device) {\n      const simulator = device[i];\n      // Skipping non-available simulator\n      if (\n        simulator.availability !== '(available)' &&\n        // @ts-ignore verify isAvailable parameter\n        simulator.isAvailable !== 'YES' &&\n        simulator.isAvailable !== true\n      ) {\n        continue;\n      }\n      const booted = simulator.state === 'Booted';\n      const simulatorDescriptor = {\n        udid: simulator.udid,\n        name: simulator.name,\n        booted,\n        version,\n      };\n      if (findOptions && findOptions.udid) {\n        if (simulator.udid === findOptions.udid) {\n          return simulatorDescriptor;\n        }\n      } else {\n        if (booted && simulatorName === null) {\n          return simulatorDescriptor;\n        }\n        if (simulator.name === simulatorName && !match) {\n          match = simulatorDescriptor;\n        }\n        // Keeps track of the first available simulator for use if we can't find one above.\n        if (simulatorName === null && !match) {\n          match = simulatorDescriptor;\n        }\n      }\n    }\n  }\n  if (match) {\n    return match;\n  }\n  return null;\n}\n\nexport default findMatchingSimulator;\n"]}