{"version": 3, "sources": ["../../src/link/removeProjectFromProject.ts"], "names": ["removeProjectFromProject", "project", "filePath", "file", "removeFromPbxFileReferenceSection", "PbxFile", "projectRef", "ProductGroup"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,wBAAT,CACbC,OADa,EAEbC,QAFa,EAGb;AACA,QAAMC,IAAI,GAAGF,OAAO,CAACG,iCAAR,CAA0C,KAAIC,kBAAJ,EAAYH,QAAZ,CAA1C,CAAb;AACA,QAAMI,UAAU,GAAG,0CAA4BL,OAA5B,EAAqCE,IAArC,CAAnB;;AAEA,MAAIG,UAAJ,EAAgB;AACd,qCAAmBL,OAAnB,EAA4BK,UAAU,CAACC,YAAvC;AACD;;AAED,uDAAuCN,OAAvC,EAAgDE,IAAhD;AAEA,SAAOA,IAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport PbxFile from 'xcode/lib/pbxFile';\nimport removeFromPbxItemContainerProxySection from './removeFromPbxItemContainerProxySection';\nimport removeFromProjectReferences from './removeFromProjectReferences';\nimport removeProductGroup from './removeProductGroup';\n\n/**\n * Given xcodeproj and filePath, it creates new file\n * from path provided and removes it. That operation is required since\n * underlying method requires PbxFile instance to be passed (it does not\n * have to have uuid or fileRef defined since it will do equality check\n * by path)\n *\n * Returns removed file (that one will have UUID)\n */\nexport default function removeProjectFromProject(\n  project: any,\n  filePath: string,\n) {\n  const file = project.removeFromPbxFileReferenceSection(new PbxFile(filePath));\n  const projectRef = removeFromProjectReferences(project, file);\n\n  if (projectRef) {\n    removeProductGroup(project, projectRef.ProductGroup);\n  }\n\n  removeFromPbxItemContainerProxySection(project, file);\n\n  return file;\n}\n"]}