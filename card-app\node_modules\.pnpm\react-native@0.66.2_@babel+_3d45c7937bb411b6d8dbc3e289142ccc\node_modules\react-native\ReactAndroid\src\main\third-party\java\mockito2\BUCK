load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_prebuilt_jar")

rn_android_library(
    name = "mockito2",
    autoglob = False,
    visibility = ["PUBLIC"],
    exported_deps = [
        ":byte-buddy",
        ":byte-buddy-agent",
        ":mockito-core-prebuilt",
        ":objenesis",
    ],
)

rn_prebuilt_jar(
    name = "mockito-core-prebuilt",
    binary_jar = ":mockito-core.jar",
)

fb_native.remote_file(
    name = "mockito-core.jar",
    sha1 = "17fb1bf75af4f5a18d8dec73b3aa55f18e6fa21a",
    url = "mvn:org.mockito:mockito-core:jar:2.26.0",
)

rn_prebuilt_jar(
    name = "byte-buddy",
    binary_jar = ":byte-buddy-binary-with-module-info-stripped",
    visibility = ["//ReactAndroid/..."],
)

# Strip module-info.class because some Android tooling cannot parse the file. Matches configuration in fbandroid.
fb_native.genrule(
    name = "byte-buddy-binary-with-module-info-stripped",
    out = "byte-buddy-binary-with-module-info-stripped.jar",
    cmd = "cp $(location :byte-buddy-binary.jar) \"$OUT\" && zip -d \"$OUT\" META-INF/versions/9/module-info.class",
)

fb_native.remote_file(
    name = "byte-buddy-binary.jar",
    sha1 = "211a2b4d3df1eeef2a6cacf78d74a1f725e7a840",
    url = "mvn:net.bytebuddy:byte-buddy:jar:1.9.10",
)

rn_prebuilt_jar(
    name = "byte-buddy-agent",
    binary_jar = ":byte-buddy-agent-with-module-info-stripped",
    visibility = ["//ReactAndroid/..."],
)

# Strip module-info.class because some Android tooling cannot parse the file. Matches configuration in fbandroid.
fb_native.genrule(
    name = "byte-buddy-agent-with-module-info-stripped",
    out = "byte-buddy-agent-with-module-info-stripped.jar",
    cmd = "cp $(location :byte-buddy-agent-binary.jar) \"$OUT\" && zip -d \"$OUT\" META-INF/versions/9/module-info.class",
)

fb_native.remote_file(
    name = "byte-buddy-agent-binary.jar",
    sha1 = "9674aba5ee793e54b864952b001166848da0f26b",
    url = "mvn:net.bytebuddy:byte-buddy-agent:jar:1.9.10",
)

rn_prebuilt_jar(
    name = "objenesis",
    binary_jar = ":objenesis-binary.jar",
    visibility = ["//ReactAndroid/..."],
)

fb_native.remote_file(
    name = "objenesis-binary.jar",
    sha1 = "639033469776fd37c08358c6b92a4761feb2af4b",
    url = "mvn:org.objenesis:objenesis:jar:2.6",
)
